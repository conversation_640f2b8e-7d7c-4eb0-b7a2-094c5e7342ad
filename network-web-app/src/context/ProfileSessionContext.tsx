'use client';

import {
  getStorageValue,
  removeStorageValue,
  setStorageValue,
} from '@/utils/storage';
import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
  useCallback,
} from 'react';

export type ActiveProfileType = 'USER' | 'ENTITY';

type ProfileSessionContextValue = {
  mounted: boolean;
  activeProfileType: ActiveProfileType;
  entityProfileId: string | null;
  setActiveProfileTypeLocal: (
    type: ActiveProfileType,
    entityId?: string | null
  ) => void;
};

const ProfileSessionContext = createContext<
  ProfileSessionContextValue | undefined
>(undefined);

const STORAGE_KEYS = {
  ACTIVE_PROFILE_TYPE: 'activeProfileType',
  ENTITY_PROFILE_ID: 'entityProfileId',
} as const;

const isValidProfileType = (value: string | null): value is ActiveProfileType =>
  value === 'USER' || value === 'ENTITY';

export const ProfileSessionProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [mounted, setMounted] = useState(false);
  const [activeProfileType, setActiveProfileType] =
    useState<ActiveProfileType>('USER');
  const [entityProfileId, setEntityProfileId] = useState<string | null>(null);

  useEffect(() => {
    const storedType = getStorageValue(STORAGE_KEYS.ACTIVE_PROFILE_TYPE);
    const storedEntityId = getStorageValue(STORAGE_KEYS.ENTITY_PROFILE_ID);

    const validType = isValidProfileType(storedType) ? storedType : 'USER';

    setActiveProfileType(validType);
    setEntityProfileId(storedEntityId);
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEYS.ACTIVE_PROFILE_TYPE) {
        const newType = isValidProfileType(e.newValue) ? e.newValue : 'USER';
        setActiveProfileType(prevType =>
          prevType !== newType ? newType : prevType
        );
      } else if (e.key === STORAGE_KEYS.ENTITY_PROFILE_ID) {
        const newEntityId = e.newValue;
        setEntityProfileId(prevId =>
          prevId !== newEntityId ? newEntityId : prevId
        );
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const setActiveProfileTypeLocal = useCallback(
    (type: ActiveProfileType, entityId?: string | null) => {
      setStorageValue(STORAGE_KEYS.ACTIVE_PROFILE_TYPE, type);

      if (type === 'ENTITY' && entityId) {
        setStorageValue(STORAGE_KEYS.ENTITY_PROFILE_ID, entityId);
      } else {
        removeStorageValue(STORAGE_KEYS.ENTITY_PROFILE_ID);
      }

      const newEntityId = type === 'ENTITY' ? entityId || null : null;
      setActiveProfileType(type);
      setEntityProfileId(newEntityId);
    },
    []
  );

  const value = useMemo<ProfileSessionContextValue>(
    () => ({
      mounted,
      activeProfileType,
      entityProfileId,
      setActiveProfileTypeLocal,
    }),
    [mounted, activeProfileType, entityProfileId, setActiveProfileTypeLocal]
  );

  return (
    <ProfileSessionContext.Provider value={value}>
      {children}
    </ProfileSessionContext.Provider>
  );
};

export const useProfileSession = () => {
  const ctx = useContext(ProfileSessionContext);
  if (!ctx) {
    throw new Error(
      'useProfileSession must be used within ProfileSessionProvider'
    );
  }
  return ctx;
};
