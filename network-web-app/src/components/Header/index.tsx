'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import Logo from '../Logo';
import SearchDropdown from '../SearchDropdown';
import { CONFIG } from '../../constants/config';
import ForumIcon from '@assets/svg/Forum';
import SuitCase from '@assets/svg/Suitcase';
import { useProfileSession } from '@/context/ProfileSessionContext';

const Header = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { mounted, activeProfileType } = useProfileSession();

  const isPathActive = (path: string) => pathname === path;

  useEffect(() => {
    if (!mounted) return;
    if (activeProfileType === 'ENTITY' && pathname.startsWith('/forums')) {
      router.replace('/jobs');
    }
  }, [mounted, activeProfileType, pathname, router]);

  // NAVBAR ITEMS
  const menuItems = useMemo(() => {
    const base = [
      { title: 'Forums', href: '/forums', icon: <ForumIcon /> },
      { title: 'Jobs', href: '/jobs', icon: <SuitCase /> },
    ];
    if (!mounted) return base; // render baseline until mounted to avoid hydration mismatch
    return activeProfileType === 'ENTITY'
      ? base.filter(item => item.title !== 'Forums')
      : base;
  }, [mounted, activeProfileType]);

  const [isMobileMenuActive, setIsMobileMenuActive] = useState<boolean>(false);
  const [isCompact, setIsCompact] = useState<boolean>(false);

  const mobileMenuRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const mql: MediaQueryList = window.matchMedia('(max-width: 767px)');

    setIsCompact(mql.matches);

    const handle = (e: MediaQueryListEvent) => setIsCompact(e.matches);

    mql.addEventListener('change', handle);

    return () => {
      mql.removeEventListener('change', handle);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target as Node)
      ) {
        setIsMobileMenuActive(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMobileMenuActive(false);
      }
    };

    if (isMobileMenuActive) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isMobileMenuActive]);

  return (
    <>
      <header
        className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm"
        style={{ height: CONFIG.layout.headerHeight }}
      >
        <div className="max-w-screen-xl mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <Logo compact={isCompact} />
              </Link>

              <div className="flex items-center">
                {/* Search Component */}
                <div className="flex-1 flex justify-center mx-4 md:mx-8">
                  <SearchDropdown className="w-full min-w-0 max-w-xs md:min-w-lg" />
                </div>
              </div>

              {/* DESKTOP NAVIGATION */}
              <div className="hidden md:flex  items-center">
                {menuItems.map(item => (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={`flex items-center justify-center px-4 py-2 transition-all duration-200 relative ${
                      isPathActive(item.href)
                        ? 'text-[#448600] bg-green-50'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                    aria-label={`Navigate to ${item.title}`}
                    title={item.title}
                  >
                    {React.cloneElement(item.icon, {
                      size: 20,
                      className: 'mr-2',
                    })}
                    <span className="text-sm font-medium">{item.title}</span>
                    {isPathActive(item.href) && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#448600] rounded-t-sm"></div>
                    )}
                  </Link>
                ))}
              </div>

              {/* MOBILE Hamburger Menu */}
              <div className="md:hidden">
                <span
                  onClick={() => setIsMobileMenuActive(!isMobileMenuActive)}
                  className="p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 transition-colors duration-200"
                  aria-label="Toggle mobile menu"
                >
                  <div className="w-6 h-6 flex flex-col justify-center items-center">
                    <div
                      className={`w-5 h-0.5 bg-gray-600 transition-all duration-300 ${isMobileMenuActive ? 'rotate-45 translate-y-1.5' : ''}`}
                    />
                    <div
                      className={`w-5 h-0.5 bg-gray-600 mt-1 transition-all duration-300 ${isMobileMenuActive ? 'opacity-0' : ''}`}
                    />
                    <div
                      className={`w-5 h-0.5 bg-gray-600 mt-1 transition-all duration-300 ${isMobileMenuActive ? '-rotate-45 -translate-y-1.5' : ''}`}
                    />
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Dropdown */}
      {isMobileMenuActive && (
        <div
          className="md:hidden fixed left-0 right-0 bg-white border-b border-gray-200 shadow-lg z-40 animate-in slide-in-from-top duration-200"
          style={{ top: CONFIG.layout.headerHeight }}
          ref={mobileMenuRef}
        >
          <div className="max-w-screen-xl mx-auto px-4 py-2">
            {menuItems.map(item => (
              <Link
                key={item.title}
                href={item.href}
                className={`flex items-center justify-start px-4 py-3 transition-all duration-200 relative rounded-md ${
                  isPathActive(item.href)
                    ? 'text-[#448600] bg-green-50'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
                aria-label={`Navigate to ${item.title}`}
                onClick={() => setIsMobileMenuActive(false)}
              >
                {React.cloneElement(item.icon, { size: 20, className: 'mr-3' })}
                <span className="text-sm font-medium">{item.title}</span>
              </Link>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
