'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import type { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import type { Control } from 'react-hook-form';
import { apiCall } from '@/lib/api';

const fetchSkills = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  const query = {
    search: params.search && params.search.trim() !== '' ? params.search : 'a',
    page: Number(params.page) || 0,
    pageSize: 10,
  };
  const response = await apiCall(
    '/backend/api/v1/company/skill/options',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return response as ApiSelectResponseI;
};

export type SkillSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (item: any) => void;
};

const SkillSelect = ({
  control,
  name,
  label = 'Skill',
  placeholder = 'Select skill',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: SkillSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search skills..."
      apiCall={fetchSkills}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage="Please select a skill"
    />
  );
};

export default SkillSelect;
