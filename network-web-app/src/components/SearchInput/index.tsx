'use client';

import React from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { Input } from '../Input';

export type SearchInputPropsI = {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  className?: string;
  containerClassName?: string;
  onSubmit?: () => void;
  rightSlot?: React.ReactNode;
  disabled?: boolean;
};

const SearchInput: React.FC<SearchInputPropsI> = ({
  value,
  onChange,
  placeholder = 'Search',
  className,
  containerClassName,
  onSubmit,
  rightSlot,
  disabled,
}) => {
  return (
    <div className={`flex items-center gap-3 ${containerClassName || ''}`}>
      <div className="flex-1">
        <Input
          placeholder={placeholder}
          value={value}
          inputSize="lg"
          onChange={onChange}
          onKeyDown={e => {
            if (e.key === 'Enter' && onSubmit) onSubmit();
          }}
          leftIcon={<MagnifyingGlassIcon className="h-5 w-5 text-gray-500" />}
          variant="filled"
          className={`bg-gray-100 ${className || ''}`}
          disabled={disabled}
        />
      </div>
      {rightSlot ? <div className="shrink-0">{rightSlot}</div> : null}
    </div>
  );
};

export default SearchInput;
