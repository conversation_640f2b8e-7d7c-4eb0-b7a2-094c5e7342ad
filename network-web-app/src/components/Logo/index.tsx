import React from 'react';
import Image from 'next/image';
import Navicater from '@assets/images/logo.png';

const Logo = ({
  compact = true,
  width = 32,
  height = 32,
}: {
  compact?: boolean;
  width?: number;
  height?: number;
}) => {
  return (
    <div className={'flex flex-row items-center gap-2'}>
      <Image
        src={Navicater}
        alt="Navicater Logo"
        width={width}
        height={height}
        className="w-8 h-8 object-contain"
      />
      {!compact && (
        <div className="flex-row text-black">
          <span className="text-xl tracking-widest font-manrope">NAVI</span>
          <span className="text-xl tracking-widest font-manrope-bold font-bold">
            CATER
          </span>
        </div>
      )}
    </div>
  );
};

export default Logo;
