import { useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { debounce } from '@/utils/debounce';
import { getPopularShips, searchShips } from '@/networks/search/ships';
import { getPopularPorts, searchPorts } from '@/networks/search/ports';
import { SearchState } from './types';
import { SearchTab } from '@/networks/search/types';
import { ShipSearchResult, PortSearchResult } from '@/networks/search/types';

export const useSearchDropdown = () => {
  const router = useRouter();
  const [state, setState] = useState<SearchState>({
    isOpen: false,
    activeTab: 'ships',
    searchQuery: '',
    isLoading: false,
    shipResults: [],
    portResults: [],
    showingPopular: true,
  });

  const cacheRef = useRef({
    popularShipsLoaded: false,
    popularPortsLoaded: false,
    lastShipSearch: '',
    lastPortSearch: '',
  });

  const loadPopularItems = useCallback(async (tab: SearchTab) => {
    if (tab === 'ships' && cacheRef.current.popularShipsLoaded) return;
    if (tab === 'ports' && cacheRef.current.popularPortsLoaded) return;

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      if (tab === 'ships') {
        const response = await getPopularShips();
        setState(prev => ({
          ...prev,
          shipResults: response.results || [],
          isLoading: false,
        }));
        cacheRef.current.popularShipsLoaded = true;
      } else {
        const response = await getPopularPorts();
        setState(prev => ({
          ...prev,
          portResults: response.results || [],
          isLoading: false,
        }));
        cacheRef.current.popularPortsLoaded = true;
      }
    } catch (error) {
      console.error('Error loading popular items:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        // Set empty arrays on error
        ...(tab === 'ships' ? { shipResults: [] } : { portResults: [] }),
      }));
    }
  }, []);

  const performSearch = useCallback(async (query: string, tab: SearchTab) => {
    // Check if we've already searched this query for this tab
    const cacheKey = tab === 'ships' ? 'lastShipSearch' : 'lastPortSearch';
    if (cacheRef.current[cacheKey] === query) return;

    setState(prev => ({ ...prev, isLoading: true, showingPopular: false }));

    try {
      if (tab === 'ships') {
        const response = await searchShips(query);
        setState(prev => ({
          ...prev,
          shipResults: response.results || [],
          isLoading: false,
        }));
        cacheRef.current.lastShipSearch = query;
      } else {
        const response = await searchPorts(query);
        setState(prev => ({
          ...prev,
          portResults: response.results || [],
          isLoading: false,
        }));
        cacheRef.current.lastPortSearch = query;
      }
    } catch (error) {
      console.error('Error performing search:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        // Set empty arrays on error
        ...(tab === 'ships' ? { shipResults: [] } : { portResults: [] }),
      }));
    }
  }, []);

  const debouncedSearch = useCallback(
    debounce(async (query: string, tab: SearchTab) => {
      if (!query.trim()) {
        setState(prev => ({ ...prev, showingPopular: true }));
        await loadPopularItems(tab);
        return;
      }
      await performSearch(query, tab);
    }, 300),
    [loadPopularItems, performSearch]
  );

  const handleOpen = useCallback(() => {
    setState(prev => ({ ...prev, isOpen: true }));
    // Load popular items for current tab if not loaded
    if (!state.searchQuery.trim()) {
      loadPopularItems(state.activeTab);
    }
  }, [state.activeTab, state.searchQuery, loadPopularItems]);

  const handleClose = useCallback(() => {
    setState(prev => ({ ...prev, isOpen: false }));
  }, []);

  const handleTabChange = useCallback(
    (tab: SearchTab) => {
      setState(prev => ({ ...prev, activeTab: tab }));

      // Only make API call if we don't have data for this tab
      if (!state.searchQuery.trim()) {
        // Switching to popular view
        const hasData =
          tab === 'ships'
            ? cacheRef.current.popularShipsLoaded
            : cacheRef.current.popularPortsLoaded;

        if (!hasData) {
          loadPopularItems(tab);
        }
      } else {
        // Switching tabs with active search
        const lastSearch =
          tab === 'ships'
            ? cacheRef.current.lastShipSearch
            : cacheRef.current.lastPortSearch;

        if (lastSearch !== state.searchQuery) {
          performSearch(state.searchQuery, tab);
        }
      }
    },
    [state.searchQuery, loadPopularItems, performSearch]
  );

  const handleSearchChange = useCallback(
    (query: string) => {
      setState(prev => ({ ...prev, searchQuery: query }));

      // Reset cache for both tabs when search changes
      cacheRef.current.lastShipSearch = '';
      cacheRef.current.lastPortSearch = '';

      debouncedSearch(query, state.activeTab);
    },
    [state.activeTab, debouncedSearch]
  );

  const handleResultClick = useCallback(
    (result: ShipSearchResult | PortSearchResult) => {
      handleClose();
      if (state.activeTab === 'ships') {
        router.push(`/ships/${(result as ShipSearchResult).imo}`);
      } else {
        router.push(`/ports/${(result as PortSearchResult).unLocode}`);
      }
    },
    [state.activeTab, router, handleClose]
  );

  const handleShowAll = useCallback(() => {
    handleClose();
    const searchParams = state.searchQuery
      ? `?search=${encodeURIComponent(state.searchQuery)}`
      : '';
    router.push(`/${state.activeTab}${searchParams}`);
  }, [state.activeTab, state.searchQuery, router, handleClose]);

  return {
    state,
    handleOpen,
    handleClose,
    handleTabChange,
    handleSearchChange,
    handleResultClick,
    handleShowAll,
  };
};
