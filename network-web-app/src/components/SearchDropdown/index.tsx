'use client';
import React, { useRef, useEffect } from 'react';
import { useSearchDropdown } from './useHook';
import SearchInput from './components/SearchInput';
import SearchTabs from './components/SearchTabs';
import SearchResults from './components/SearchResults';
import LoadingSkeleton from './components/LoadingSkeleton';

interface SearchDropdownProps {
  className?: string;
}

const SearchDropdown = ({ className }: SearchDropdownProps) => {
  const {
    state,
    handleOpen,
    handleClose,
    handleTabChange,
    handleSearchChange,
    handleResultClick,
    handleShowAll,
  } = useSearchDropdown();

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    if (state.isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [state.isOpen, handleClose]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleClose();
    }
  };

  return (
    <div className={`relative ${className ?? ''}`} ref={dropdownRef}>
      <SearchInput
        isOpen={state.isOpen}
        searchQuery={state.searchQuery}
        onFocus={handleOpen}
        onChange={handleSearchChange}
        onKeyDown={handleKeyDown}
      />

      {state.isOpen && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-b-lg shadow-lg z-50 max-h-96 overflow-hidden">
          <SearchTabs
            activeTab={state.activeTab}
            onTabChange={handleTabChange}
          />

          {state.isLoading &&
          !state.shipResults?.length &&
          !state.portResults?.length ? (
            <LoadingSkeleton count={5} />
          ) : (
            <SearchResults
              activeTab={state.activeTab}
              isLoading={state.isLoading}
              shipResults={state.shipResults || []}
              portResults={state.portResults || []}
              showingPopular={state.showingPopular}
              onResultClick={handleResultClick}
              _onShowAll={handleShowAll}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SearchDropdown;
