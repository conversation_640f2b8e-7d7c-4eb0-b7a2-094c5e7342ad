import React from 'react';

interface LoadingSkeletonProps {
  count?: number;
}

const LoadingSkeleton = ({ count = 5 }: LoadingSkeletonProps) => {
  return (
    <div className="p-2">
      <div className="px-2 py-1 mb-2">
        <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
      </div>

      {[...Array(count)].map((_, i) => (
        <div key={i} className="flex items-center p-3 animate-pulse">
          <div className="w-12 h-12 bg-gray-200 rounded mr-3"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      ))}

      <div className="mt-2 px-4 py-2">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
};

export default LoadingSkeleton;
