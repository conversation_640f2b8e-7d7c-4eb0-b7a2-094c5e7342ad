import React from 'react';
import {
  SearchTab,
  ShipSearchResult,
  PortSearchResult,
} from '@/networks/search/types';
import ResultItem from '../ResultItem';
import LoadingSkeleton from '../LoadingSkeleton';

interface SearchResultsProps {
  activeTab: SearchTab;
  isLoading: boolean;
  shipResults: ShipSearchResult[];
  portResults: PortSearchResult[];
  showingPopular: boolean;
  onResultClick: (result: ShipSearchResult | PortSearchResult) => void;
  _onShowAll: () => void;
}

const SearchResults = ({
  activeTab,
  isLoading,
  shipResults = [],
  portResults = [],
  showingPopular,
  onResultClick,
  _onShowAll,
}: SearchResultsProps) => {
  const currentResults =
    activeTab === 'ships' ? shipResults || [] : portResults || [];

  if (isLoading) {
    return <LoadingSkeleton count={5} />;
  }

  if (!currentResults || currentResults.length === 0) {
    return (
      <div className="p-8 text-center w-full">
        <div className="text-gray-400 mb-2">
          <svg
            className="w-12 h-12 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <div className="text-gray-500 text-sm">
          {showingPopular
            ? `No popular ${activeTab} found`
            : `No ${activeTab} found`}
        </div>
      </div>
    );
  }

  return (
    <div className="max-h-80 overflow-y-auto">
      <div className="p-2">
        {showingPopular && (
          <div className="px-2 py-1 text-xs text-gray-500 font-medium uppercase tracking-wide">
            Popular {activeTab}
          </div>
        )}

        <div className="space-y-1">
          {currentResults.map(result => (
            <ResultItem
              key={
                activeTab === 'ships'
                  ? (result as ShipSearchResult).imo
                  : (result as PortSearchResult).unLocode
              }
              result={result}
              type={activeTab}
              onClick={() => onResultClick(result)}
            />
          ))}
        </div>

        {/* <div className="mt-2 px-2">
            <button
              onClick={_onShowAll}
              className="w-full px-4 py-2 text-sm text-[#448600] hover:bg-green-50 rounded transition-colors border border-transparent hover:border-green-200"
            >
              Show all {activeTab} →
            </button>
          </div> */}
      </div>
    </div>
  );
};

export default SearchResults;
