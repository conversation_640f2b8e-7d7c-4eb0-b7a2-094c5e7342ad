import React from 'react';
import { SearchTab } from '@/networks/search/types';

interface SearchTabsProps {
  activeTab: SearchTab;
  onTabChange: (tab: SearchTab) => void;
}

const SearchTabs = ({ activeTab, onTabChange }: SearchTabsProps) => {
  const tabs: Array<{ id: SearchTab; label: string }> = [
    { id: 'ships', label: 'Ships' },
    { id: 'ports', label: 'Ports' },
  ];

  return (
    <div className="border-b border-gray-200">
      <nav className="flex px-4 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        {tabs.map(tab => (
          <span
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`py-3 px-4 border-b-2 font-medium text-sm transition-colors cursor-pointer  ${
              activeTab === tab.id
                ? 'border-[#448600] text-[#448600]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {tab.label}
          </span>
        ))}
      </nav>
    </div>
  );
};

export default SearchTabs;
