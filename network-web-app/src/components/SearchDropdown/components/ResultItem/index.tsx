import React from 'react';
import Image from 'next/image';
import {
  SearchTab,
  ShipSearchResult,
  PortSearchResult,
} from '@/networks/search/types';

interface ResultItemProps {
  result: ShipSearchResult | PortSearchResult;
  type: SearchTab;
  onClick: () => void;
}

const ResultItem = ({ result, type, onClick }: ResultItemProps) => {
  if (type === 'ships') {
    const ship = result as ShipSearchResult;

    return (
      <div
        onClick={onClick}
        className="flex items-center p-3 hover:bg-gray-50 cursor-pointer rounded transition-colors"
      >
        <div className="w-12 h-12 rounded mr-3 overflow-hidden bg-blue-50 flex items-center justify-center relative">
          <Image
            src="https://b2c-space.tor1.cdn.digitaloceanspaces.com/PLACEHOLDERS/ship.png"
            alt={ship.name}
            fill
            className="object-cover"
            onError={() => {}}
            sizes="48px"
          />
        </div>
        <div className="flex-1">
          <div className="font-medium text-gray-900">{ship.matchedName}</div>
          <div className="text-sm text-gray-500">IMO: {ship.imo}</div>
        </div>
      </div>
    );
  }

  const port = result as PortSearchResult;
  return (
    <div
      onClick={onClick}
      className="flex items-center p-3 hover:bg-gray-50 cursor-pointer rounded transition-colors"
    >
      <div className="w-12 h-12 bg-amber-50 rounded flex items-center justify-center mr-3">
        <Image
          src="https://b2c-space.tor1.cdn.digitaloceanspaces.com/PLACEHOLDERS/port.png"
          alt={port.name}
          width={48}
          height={48}
          className="object-cover"
          onError={() => {}}
        />
      </div>
      <div className="flex-1">
        <div className="font-medium text-gray-900">{port.name}</div>
        <div className="text-sm text-gray-500">
          {port.city.name} • {port.unLocode}
        </div>
      </div>
    </div>
  );
};

export default ResultItem;
