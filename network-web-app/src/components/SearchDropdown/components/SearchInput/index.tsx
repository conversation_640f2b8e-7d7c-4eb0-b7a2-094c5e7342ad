import React from 'react';

interface SearchInputProps {
  isOpen: boolean;
  searchQuery: string;
  onFocus: () => void;
  onChange: (value: string) => void;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

const SearchInput = ({
  isOpen,
  searchQuery,
  onFocus,
  onChange,
  onKeyDown,
}: SearchInputProps) => {
  return (
    <div className="relative w-full">
      <input
        type="text"
        placeholder="Search ships, ports..."
        value={searchQuery}
        onChange={e => onChange(e.target.value)}
        onFocus={onFocus}
        onKeyDown={onKeyDown}
        className={`w-full px-4 py-2 border border-gray-300 bg-white text-gray-900 placeholder-gray-500 transition-all duration-200 ${
          isOpen
            ? 'rounded-t-lg border-b-0 focus:ring-2 focus:ring-[#448600] focus:border-[#448600]'
            : 'rounded-lg focus:ring-2 focus:ring-[#448600] focus:border-[#448600]'
        } focus:outline-none`}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
        <svg
          className="h-4 w-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
    </div>
  );
};

export default SearchInput;
