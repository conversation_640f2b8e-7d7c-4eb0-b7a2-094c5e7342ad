'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import type { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import type { Control } from 'react-hook-form';
import { apiCall } from '@/lib/api';

const fetchDepartments = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  if (params.search === '' || params.search === undefined) {
    params.search = 'a';
  }
  const response = await apiCall(
    '/backend/api/v1/company/department/options',
    'GET',
    {
      isAuth: true,
      query: { ...params, page: String(params.page) },
    }
  );
  return response as ApiSelectResponseI;
};

export type DepartmentSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (item: any) => void;
};

const DepartmentSelect = ({
  control,
  name,
  label = 'Department',
  placeholder = 'Select your department',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: DepartmentSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search departments..."
      apiCall={fetchDepartments}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage={`Please select ${label ?? 'department'}`}
    />
  );
};

export default DepartmentSelect;
