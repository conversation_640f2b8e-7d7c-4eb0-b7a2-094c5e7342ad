'use client';

import React from 'react';
import { Control } from 'react-hook-form';
import { ApiSelect } from '@/components';
import { fetchCurrencies } from '@/networks/data/currency';

export type CurrencySelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
};

const CurrencySelect = ({
  control,
  name,
  label = 'Currency',
  placeholder = 'Select',
  isRequired = false,
  disabled = false,
  className = 'w-full',
}: CurrencySelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search currency..."
      apiCall={fetchCurrencies as any}
      optionLabelKey="code"
      optionValueKey="code"
      uniqueKey="code"
      requiredMessage="Please select a currency"
    />
  );
};

export default CurrencySelect;
