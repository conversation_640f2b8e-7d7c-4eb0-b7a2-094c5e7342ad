'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import { apiCall } from '@/lib/api';
import type { Control } from 'react-hook-form';

const fetchShips = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  const query = {
    search: params.search && params.search.trim() !== '' ? params.search : 'a',
    page: Number(params.page) || 0,
    pageSize: 10,
  };
  const response = await apiCall('/backend/api/v1/ship/search', 'GET', {
    isAuth: true,
    query,
  });
  return response as ApiSelectResponseI;
};

export type ShipSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (item: any) => void;
};

const ShipSelect = ({
  control,
  name,
  label = 'Ship IMO',
  placeholder = 'Search ship by name/IMO',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: ShipSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search ships..."
      apiCall={fetchShips}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="imo"
      uniqueKey="imo"
      requiredMessage="Please select a ship"
    />
  );
};

export default ShipSelect;
