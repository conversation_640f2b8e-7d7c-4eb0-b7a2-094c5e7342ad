'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

export type ConfirmModalProps = {
  isOpen: boolean;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
};

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  title = 'Are you sure?',
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  loading = false,
  disabled = false,
  className = '',
}) => {
  const [mounted, setMounted] = useState(false);
  useEffect(() => setMounted(true), []);

  if (!isOpen || !mounted) return null;

  const handleBackdrop = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) onCancel();
  };

  const modal = (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center ${className}`}
      onClick={handleBackdrop}
      role="dialog"
      aria-modal="true"
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 z-0" />

      {/* Modal content */}
      <div className="relative z-10 bg-white rounded-2xl shadow-lg w-full max-w-sm mx-4 p-5 border border-gray-200">
        {title && (
          <h3 className="text-base font-semibold text-gray-900 mb-1">
            {title}
          </h3>
        )}
        {description && (
          <p className="text-sm text-gray-600 mb-4 leading-relaxed">
            {description}
          </p>
        )}
        <div className="flex items-center justify-end space-x-3 mt-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 rounded-md text-sm border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            {cancelText}
          </button>
          <button
            type="button"
            onClick={onConfirm}
            disabled={loading || disabled}
            className={`px-4 py-2 rounded-md text-sm text-white bg-[#448600] hover:bg-[#357000] disabled:opacity-60 disabled:cursor-not-allowed`}
          >
            {loading ? 'Processing...' : confirmText}
          </button>
        </div>
      </div>
    </div>
  );

  return typeof window !== 'undefined'
    ? createPortal(modal, document.body)
    : null;
};

export default ConfirmModal;
