'use client';

import React, { forwardRef } from 'react';
import { cn } from '@/utilities';

export type TextareaPropsI =
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
    label?: string;
    error?: string;
    containerClassName?: string;
    labelClassName?: string;
    errorClassName?: string;
  };

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaPropsI>(
  (
    {
      className,
      label,
      error,
      containerClassName,
      labelClassName,
      errorClassName,
      id,
      ...props
    },
    ref
  ) => {
    const textareaId = id || `textarea-${Math.random().toString(36).slice(2)}`;

    return (
      <div className={cn('w-full', containerClassName)}>
        {label && (
          <label
            htmlFor={textareaId}
            className={cn(
              'block text-sm font-medium text-gray-700 mb-2',
              labelClassName
            )}
          >
            {label}
          </label>
        )}
        <textarea
          id={textareaId}
          ref={ref}
          className={cn(
            'block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-primary focus:ring-1 focus:ring-primary disabled:bg-gray-50',
            error && 'border-red-300',
            className
          )}
          {...props}
        />
        {error && (
          <p className={cn('mt-1 text-sm text-red-600', errorClassName)}>
            {error}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Textarea;
