'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import { apiCall } from '@/lib/api';
import type { Control } from 'react-hook-form';

const fetchShipTypes = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  const query = {
    search: params.search && params.search.trim() !== '' ? params.search : 'a',
    page: Number(params.page) || 0,
    pageSize: 10,
  };
  const response = await apiCall(
    '/backend/api/v1/ship/sub-vessel-type/options',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return response as ApiSelectResponseI;
};

export type ShipTypeSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (item: any) => void;
};

const ShipTypeSelect = ({
  control,
  name,
  label = 'Ship type',
  placeholder = 'Select ship type',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: ShipTypeSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search ship types..."
      apiCall={fetchShipTypes}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage="Please select a ship type"
    />
  );
};

export default ShipTypeSelect;
