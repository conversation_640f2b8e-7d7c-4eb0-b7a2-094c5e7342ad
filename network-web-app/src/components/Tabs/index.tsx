'use client';

import React from 'react';

export type TabItemI = {
  id: string;
  label: string;
};

export type TabsPropsI = {
  items: TabItemI[];
  activeId: string;
  onChange: (id: string) => void;
  className?: string;
};

const Tabs: React.FC<TabsPropsI> = ({
  items,
  activeId,
  onChange,
  className,
}) => {
  return (
    <div
      className={`flex items-center space-x-6 border-b border-gray-200 ${className || ''}`}
    >
      {items.map(tab => {
        const isActive = tab.id === activeId;
        return (
          <button
            key={tab.id}
            type="button"
            onClick={() => onChange(tab.id)}
            className={`pb-3 text-sm font-medium transition-colors relative outline-none focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0 ${
              isActive ? 'text-[#448600]' : 'text-gray-500 hover:text-gray-700'
            }`}
            aria-current={isActive ? 'page' : undefined}
          >
            {tab.label}
            {isActive && (
              <span className="absolute -bottom-px left-0 right-0 h-0.5 bg-[#448600] rounded-t-sm" />
            )}
          </button>
        );
      })}
    </div>
  );
};

export default Tabs;
