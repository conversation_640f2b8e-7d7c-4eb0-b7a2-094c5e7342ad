'use client';

import React from 'react';
import { Controller } from 'react-hook-form';
import { Select } from '@/components';

export type JobTypeSelectPropsI = {
  control: any;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
};

const options = [
  { value: 'SAILING', label: 'Sailing' },
  { value: 'NON_SAILING', label: 'Non-sailing' },
];

const JobTypeSelect = ({
  control,
  name,
  label = 'Job type',
  placeholder = 'Select',
  isRequired = false,
  disabled = false,
  className = 'w-full',
}: JobTypeSelectPropsI) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={isRequired ? { required: 'Please select a job type' } : undefined}
      render={({ field, fieldState }) => (
        <Select
          label={label}
          placeholder={placeholder}
          options={options}
          value={field.value}
          onValueChange={field.onChange}
          error={fieldState.error?.message}
          disabled={disabled}
          className={className}
        />
      )}
    />
  );
};

export default JobTypeSelect;
