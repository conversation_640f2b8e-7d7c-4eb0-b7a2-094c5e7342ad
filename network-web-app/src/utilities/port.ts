export const parsePortSlug = (
  slug: string
): {
  name: string;
  unLocode: string;
} => {
  const parts = slug.split('-');
  const unLocode = parts[parts.length - 1];
  const name = parts.slice(0, -1).join('-');
  return {
    name,
    unLocode,
  };
};

export const createPortSlug = (name: string, unLocode: string): string => {
  const cleanName = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
  return `${cleanName}-${unLocode}`;
};

export const validatePortSlug = (
  slug: string,
  actualPortName: string,
  unLocode: string
): boolean => {
  const expectedSlug = createPortSlug(actualPortName, unLocode);
  return slug === expectedSlug;
};
