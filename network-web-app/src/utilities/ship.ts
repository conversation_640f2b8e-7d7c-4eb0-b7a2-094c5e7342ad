export const parseShipSlug = (
  slug: string
): {
  name: string;
  imo: string;
} => {
  const parts = slug.split('-');
  const imo = parts[parts.length - 1];
  const name = parts.slice(0, -1).join('-');
  return {
    name,
    imo,
  };
};

export const formatShipMMSI = (mmsi: number | null): string => {
  return mmsi ? mmsi.toString() : '-';
};

export const formatShipYear = (year: number | null): string => {
  return year ? year.toString() : '-';
};

export const formatShipDimensions = (
  length?: number,
  beam?: number
): string => {
  if (length && beam) return `${length}m x ${beam}m`;
  if (length) return `${length}m length`;
  if (beam) return `- x ${beam}m beam`;
  return '-';
};
