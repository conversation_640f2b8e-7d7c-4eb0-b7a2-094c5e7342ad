import { MetadataRoute } from 'next';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      // AI Bots - Block all content
      {
        userAgent: 'GPTBot',
        disallow: '/',
      },
      {
        userAgent: 'ChatGPT-User',
        disallow: '/',
      },
      {
        userAgent: 'CCBot',
        disallow: '/',
      },
      {
        userAgent: 'anthropic-ai',
        disallow: '/',
      },
      {
        userAgent: 'Claude-Web',
        disallow: '/',
      },
      {
        userAgent: 'PerplexityBot',
        disallow: '/',
      },
      {
        userAgent: 'Applebot-Extended',
        disallow: '/',
      },
      {
        userAgent: 'FacebookBot',
        disallow: '/',
      },
      {
        userAgent: 'Google-Extended',
        disallow: '/',
      },
      {
        userAgent: 'OAI-SearchBot',
        disallow: '/',
      },
      {
        userAgent: 'Omgilibot',
        disallow: '/',
      },
      {
        userAgent: 'YouBot',
        disallow: '/',
      },
      {
        userAgent: 'Diffbot',
        disallow: '/',
      },
      {
        userAgent: 'img2dataset',
        disallow: '/',
      },
      // General crawlers - Allow specific content
      {
        userAgent: '*',
        allow: [
          '/',
          '/forums',
          '/forums/question/*',
          '/jobs',
          '/privacy-policy',
          '/terms',
          '/api/og',
        ],
        disallow: [
          '/api/auth/*',
          '/admin/*',
          '/_next/*',
          '/private/*',
          '/dashboard/*',
          '/user/*',
          '/profile/*',
          '/settings/*',
          '/auth/*',
          '/login',
          '/signup',
          '/otp',
          '/forgot-password',
          '/reset-password',
          '/fill-user-details',
          '/ships/*',
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/forums',
          '/forums/question/*',
          '/jobs',
          '/ships/*',
          '/privacy-policy',
          '/terms',
          '/api/og',
        ],
        disallow: [
          '/api/auth/*',
          '/admin/*',
          '/_next/*',
          '/private/*',
          '/dashboard/*',
          '/user/*',
          '/profile/*',
          '/settings/*',
          '/auth/*',
          '/login',
          '/signup',
          '/otp',
          '/forgot-password',
          '/reset-password',
          '/fill-user-details',
        ],
      },
      {
        userAgent: 'Bingbot',
        allow: [
          '/',
          '/forums',
          '/forums/question/*',
          '/jobs',
          '/ships/*',
          '/privacy-policy',
          '/terms',
          '/api/og',
        ],
        disallow: [
          '/api/auth/*',
          '/admin/*',
          '/_next/*',
          '/private/*',
          '/dashboard/*',
          '/user/*',
          '/profile/*',
          '/settings/*',
          '/auth/*',
          '/login',
          '/signup',
          '/otp',
          '/forgot-password',
          '/reset-password',
          '/fill-user-details',
        ],
      },
    ],
    sitemap: `${BASE_URL}/sitemap.xml`,
    host: BASE_URL,
  };
}
