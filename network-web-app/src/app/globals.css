@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --primary: #448600;
  --primary-hover: #3a7300;
  --secondary: #666666;
  --text: #000000;
  --text-light: #666666;
  --border: #d0d7de;
  --background: #ffffff;
  --background-light: #f8f9fa;
  --ring-primary: #448600;
}

@theme {
  --color-primary: #448600;
  --color--ring-primary: #448600;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #ffff;
    --foreground: #000000;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue',
    Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles - disable global green outline to let component styles control focus */
*:focus {
  outline: none;
  outline-offset: 0;
}

/* Button focus styles - disabled (handled per-component) */
button:focus {
  outline: none;
  outline-offset: 0;
}

/* Custom animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Layout shift prevention */
.prevent-layout-shift {
  contain: layout style;
}

/* Ensure consistent sizing for elements that might cause shifts */
.w-full.bg-white.border-t.border-gray-200 {
  min-height: 80px;
}

/* Prevent shifts from loading states */
.loading-placeholder {
  min-height: 40px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Responsive utilities */
@media (max-width: 1024px) {
  .lg\:col-span-3 {
    display: none;
  }

  .lg\:col-span-6 {
    grid-column: span 12;
  }
}

@media (max-width: 768px) {
  .sticky {
    position: relative !important;
    top: auto !important;
  }
}

input[type='range'] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  outline: none;
}

/* Webkit browsers (Chrome, Safari, Edge) */
input[type='range']::-webkit-slider-runnable-track {
  width: 100%;
  height: 8px;
  background: linear-gradient(
    to right,
    var(--primary) 0%,
    var(--primary) calc(var(--slider-value, 50) * 100%),
    #e5e7eb calc(var(--slider-value, 50) * 100%),
    #e5e7eb 100%
  );
  border-radius: 4px;
  border: none;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: white;
  border: 3px solid var(--primary);
  border-radius: 50%;
  cursor: pointer;
  margin-top: -6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

input[type='range']::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* Firefox */
input[type='range']::-moz-range-track {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  border: none;
}

input[type='range']::-moz-range-progress {
  height: 8px;
  background: var(--primary);
  border-radius: 4px;
}

input[type='range']::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: white;
  border: 3px solid var(--primary);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

input[type='range']::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* Focus styles for accessibility */
input[type='range']:focus::-webkit-slider-thumb {
  box-shadow:
    0 0 0 3px rgba(68, 134, 0, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

input[type='range']:focus::-moz-range-thumb {
  box-shadow:
    0 0 0 3px rgba(68, 134, 0, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Custom slider utility class */
.custom-slider {
  height: 8px;
  background: transparent;
}

/* For dynamic fill updates via JavaScript */
.custom-slider[style*='--slider-value'] {
  /* This will be updated via inline styles in your component */
}
