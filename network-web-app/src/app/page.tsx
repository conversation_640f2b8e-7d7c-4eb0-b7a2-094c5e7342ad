'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';

type ActiveProfileType = 'USER' | 'ENTITY';

const PROFILE_ROUTES: Record<ActiveProfileType, string> = {
  USER: '/forums',
  ENTITY: '/jobs',
} as const;

export default function Home() {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (pathname !== '/') return;

    try {
      const storedType = localStorage.getItem('activeProfileType');
      const isValidType = (value: string | null): value is ActiveProfileType =>
        value === 'USER' || value === 'ENTITY';
      const profileType = isValidType(storedType) ? storedType : 'USER';
      const targetRoute = PROFILE_ROUTES[profileType];

      router.replace(targetRoute);
    } catch (error) {
      console.error('Failed to redirect from home page:', error);
      router.replace('/forums');
    }
  }, [pathname, router]);

  return null;
}
