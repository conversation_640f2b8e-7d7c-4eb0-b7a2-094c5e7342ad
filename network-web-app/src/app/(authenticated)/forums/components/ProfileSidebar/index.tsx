'use client';
import React, { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { ProfileSidebarPropsI, SwitchProfileItemI } from './types';
import { logout } from '@/app/actions';
import { fetchEntityProfilesForUserAPI } from '@/networks/entityProfile';
import { updateProfileTypeAPI } from '@/networks/session';
import { showToast } from '@/utils/toast';
import { ConfirmModal } from '@/components';
import { useProfileSession } from '@/context/ProfileSessionContext';

const ProfileSidebar = ({ user, isMobile = false }: ProfileSidebarPropsI) => {
  const router = useRouter();
  const pathname = usePathname();

  const [profiles, setProfiles] = useState<SwitchProfileItemI[]>([]);
  const [loading, setLoading] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [pendingItem, setPendingItem] = useState<SwitchProfileItemI | null>(
    null
  );
  const { activeProfileType, entityProfileId, setActiveProfileTypeLocal } =
    useProfileSession();
  const [activeEntityId, setActiveEntityId] = useState<string | null>(
    entityProfileId
  );

  const selectedId = useMemo(() => {
    return activeProfileType === 'ENTITY' && activeEntityId
      ? activeEntityId
      : user.id;
  }, [activeProfileType, activeEntityId, user.id]);

  const selectedEntity = useMemo(
    () =>
      profiles.find(p => p.type === 'ENTITY' && p.id === activeEntityId) ||
      null,
    [profiles, activeEntityId]
  );

  const displayAvatar =
    activeProfileType === 'ENTITY' ? selectedEntity?.avatar : user.avatar;

  const displayName =
    activeProfileType === 'ENTITY'
      ? selectedEntity?.name || user.name
      : user.name;

  const handleLogout = async () => {
    localStorage.clear();
    await logout();
  };

  const openConfirm = (item: SwitchProfileItemI) => {
    setPendingItem(item);
    setIsConfirmOpen(true);
  };

  const closeConfirm = () => {
    setIsConfirmOpen(false);
    setPendingItem(null);
  };

  const handleConfirmSwitch = async () => {
    if (!pendingItem) return;
    try {
      setLoading(true);
      if (pendingItem.type === 'USER') {
        await updateProfileTypeAPI({ type: 'USER' });
        if (typeof window !== 'undefined') {
          localStorage.setItem('activeProfileType', 'USER');
          localStorage.removeItem('entityProfileId');
        }
        setActiveProfileTypeLocal('USER', null);
        setActiveEntityId(null);
      } else {
        await updateProfileTypeAPI({ type: 'ENTITY' });
        if (typeof window !== 'undefined') {
          localStorage.setItem('activeProfileType', 'ENTITY');
          localStorage.setItem('entityProfileId', pendingItem.id);
        }
        setActiveProfileTypeLocal('ENTITY', pendingItem.id);
        setActiveEntityId(pendingItem.id);
        if (pathname?.startsWith && pathname.startsWith('/forums')) {
          try {
            router.replace('/jobs');
          } catch {}
        }
      }
      showToast({ type: 'success', message: 'Profile switched' });
    } catch (e) {
      console.log(e);
      showToast({
        type: 'error',
        message: 'Could not switch profile. Try again later.',
      });
    } finally {
      setLoading(false);
      closeConfirm();
    }
  };

  useEffect(() => {
    let mounted = true;
    const load = async () => {
      try {
        setLoading(true);
        const entities = await fetchEntityProfilesForUserAPI();
        if (!mounted) return;
        const list: SwitchProfileItemI[] = [
          { id: user.id, name: user.name, avatar: user.avatar, type: 'USER' },
          ...entities.map(e => ({
            id: e.id,
            name: e.name,
            avatar: e.avatar,
            type: 'ENTITY' as const,
          })),
        ];
        setProfiles(list);
      } catch (_e) {
        // ignore silently
        console.error(_e);
      } finally {
        if (mounted) setLoading(false);
      }
    };
    load();
    return () => {
      mounted = false;
    };
  }, []);

  const renderSwitcherSkeleton = () => (
    <div className="mt-2">
      <h3 className="text-sm font-semibold text-gray-800 mb-2">
        Switch profile
      </h3>
      <div className="space-y-1">
        {Array.from({ length: 3 }).map((_, idx) => (
          <div key={idx} className="w-full flex items-center px-2 py-2 rounded">
            <div className="w-6 h-6 rounded-full bg-gray-200 animate-pulse border border-gray-200 mr-2" />
            <div className="flex-1 h-4 bg-gray-200 rounded animate-pulse" />
          </div>
        ))}
      </div>
    </div>
  );

  const renderSwitcher = () => {
    if (loading) return renderSwitcherSkeleton();
    if (profiles.length <= 1) return null;
    return (
      <div className="mt-2">
        <h3 className="text-sm font-semibold text-gray-800 mb-2">
          Switch profile
        </h3>
        <div className="space-y-1">
          {profiles.map(item => {
            const selected = item.id === selectedId;
            return (
              <button
                key={`${item.type}-${item.id}`}
                onClick={() => openConfirm(item)}
                disabled={loading}
                className={`w-full flex items-center px-2 py-2 rounded hover:bg-gray-50 text-left ${
                  selected ? 'bg-green-50' : ''
                }`}
              >
                {item.avatar ? (
                  <Image
                    src={item.avatar ?? ''}
                    alt={item.name}
                    width={24}
                    height={24}
                    className="w-6 h-6 rounded-full object-cover border border-gray-200 mr-2"
                  />
                ) : (
                  <div className="w-6 h-6 rounded-full  bg-gray-200 mr-2"></div>
                )}
                <span className="flex-1 text-sm text-gray-800 truncate">
                  {item.name}
                </span>
                {selected && (
                  <span className="w-2 h-2 rounded-full bg-green-500" />
                )}
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <>
      {isMobile ? (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <div className="p-4">
            <div className="flex items-center justify-between space-x-4">
              {/* Avatar */}
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {displayAvatar ? (
                    <Image
                      src={displayAvatar}
                      alt={displayName}
                      width={56}
                      height={56}
                      className="w-14 h-14 rounded-full border-2 border-gray-200 object-cover"
                    />
                  ) : (
                    <div className="w-14 h-14 rounded-full border-2 border-gray-200 bg-gray-200" />
                  )}
                </div>
                <div>
                  <h2 className="text-ellipsis font-medium ">{displayName}</h2>
                  {activeProfileType === 'USER' && (
                    <h2 className="text-ellipsis font-normal text-gray-600">
                      {user.title
                        ? user.title
                        : user.company
                          ? user.company
                          : ''}
                    </h2>
                  )}
                </div>
              </div>

              <div className="flex-shrink-0">
                <button
                  onClick={handleLogout}
                  className="p-2 text-gray-400 hover:text-gray-600 bg-gray-200 rounded-full transition-colors"
                  aria-label="Logout"
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
            <div className="mt-4">{renderSwitcher()}</div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <div className="h-16 bg-gradient-to-r from-gray-900 to-[#448600] relative">
            {user.coverImage && (
              <Image
                src={user.coverImage}
                alt="Cover"
                fill
                className="object-cover"
              />
            )}
          </div>

          <div className="px-4 pb-4 -mt-8 relative">
            <div className="flex justify-center mb-4">
              <div className="relative">
                {displayAvatar ? (
                  <Image
                    src={displayAvatar}
                    alt={displayName}
                    width={72}
                    height={72}
                    className="w-18 h-18 rounded-full border-4 border-white object-cover"
                  />
                ) : (
                  <div className="w-18 h-18 rounded-full border-4 border-white bg-gray-200" />
                )}
              </div>
            </div>

            <div className="text-center ">
              <h2 className="text-lg font-semibold text-gray-900 mb-1">
                {displayName}
              </h2>
              {activeProfileType === 'USER' && user.title && user.company && (
                <h2 className="text-sm text-ellipsis font-normal text-gray-600 mb-1">
                  {user.title} at {user.company}
                </h2>
              )}
            </div>

            {profiles.length > 1 && (
              <div className="border-t border-gray-200 my-4"></div>
            )}

            {renderSwitcher()}

            <div className="border-t border-gray-200 my-4"></div>

            <div className="space-y-2">
              <div
                onClick={handleLogout}
                className="flex items-center text-sm text-gray-600 hover:text-gray-900 cursor-pointer"
              >
                <svg
                  className="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                Logout
              </div>
            </div>
          </div>
        </div>
      )}

      <ConfirmModal
        isOpen={isConfirmOpen}
        title="Switch profile?"
        description={`You are about to switch to "${pendingItem?.name ?? ''}". Continue?`}
        confirmText="Switch"
        cancelText="Cancel"
        loading={loading}
        onConfirm={handleConfirmSwitch}
        onCancel={closeConfirm}
      />
    </>
  );
};

export default ProfileSidebar;
