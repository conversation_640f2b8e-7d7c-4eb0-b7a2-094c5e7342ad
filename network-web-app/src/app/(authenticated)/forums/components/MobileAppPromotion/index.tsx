'use client';

import { Logo } from '@/components';
import React from 'react';
import { QRCodeSVG } from 'qrcode.react';

const MobileAppPromotion = () => {
  const handleApple = () => {
    window.open(
      'https://apps.apple.com/ca/app/navicater/id6744237033',
      '_blank'
    );
  };

  const handleGoogle = () => {
    window.open(
      'https://play.google.com/store/apps/details?id=com.navicater',
      '_blank'
    );
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Get the mobile app
        </h3>
        <p className="text-sm text-gray-600">
          Stay connected and get answers on the go with our mobile app.
        </p>
      </div>

      <div className="relative mb-6">
        <div className="relative rounded-2xl p-6 text-white overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 animate-pulse"></div>
          <div
            className="absolute inset-0 opacity-70"
            style={{
              background: `linear-gradient(45deg, 
              rgba(59, 130, 246, 0.8) 0%, 
              rgba(147, 51, 234, 0.9) 25%, 
              rgba(79, 70, 229, 0.8) 50%, 
              rgba(168, 85, 247, 0.9) 75%, 
              rgba(99, 102, 241, 0.8) 100%)`,
              backgroundSize: '400% 400%',
              animation: 'gradientMove 8s ease infinite',
            }}
          ></div>
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white/20 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 2}s`,
                }}
              ></div>
            ))}
          </div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/30 hover:bg-white/30 transition-all duration-300">
                  <Logo width={16} height={16} />
                </div>
                <div>
                  <span className="font-bold text-lg">Navicater</span>
                  <div className="text-xs opacity-75">Mobile App</div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleApple}
                className="w-full bg-black/80 backdrop-blur-sm text-white rounded-xl p-4 flex items-center justify-center space-x-3 hover:bg-black/90 transition-all duration-300 hover:scale-105 hover:shadow-lg border border-white/10"
              >
                <svg
                  className="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                </svg>
                <div className="text-left">
                  <div className="text-xs opacity-75">Download on the</div>
                  <div className="text-sm font-semibold">App Store</div>
                </div>
              </button>

              <button
                onClick={handleGoogle}
                className="w-full bg-black/80 backdrop-blur-sm text-white rounded-xl p-4 flex items-center justify-center space-x-3 hover:bg-black/90 transition-all duration-300 hover:scale-105 hover:shadow-lg border border-white/10"
              >
                <svg
                  className="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                </svg>
                <div className="text-left">
                  <div className="text-xs opacity-75">Get it on</div>
                  <div className="text-sm font-semibold">Google Play</div>
                </div>
              </button>
            </div>
          </div>

          <style jsx>{`
            @keyframes gradientMove {
              0% {
                background-position: 0% 50%;
              }
              50% {
                background-position: 100% 50%;
              }
              100% {
                background-position: 0% 50%;
              }
            }
            @keyframes float {
              0%,
              100% {
                transform: translateY(0px) rotate(0deg);
              }
              50% {
                transform: translateY(-20px) rotate(180deg);
              }
            }
          `}</style>
        </div>
      </div>

      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="text-center">
          <p className="text-xs text-gray-500 mb-3">Scan to download</p>
          <div className="inline-block p-2 bg-gray-100 rounded-lg">
            <div className="w-24 h-24 bg-gray-300 rounded flex items-center justify-center">
              <QRCodeSVG
                value="https://network.navicater.com"
                size={96}
                bgColor="#FFFFFF"
                fgColor="#000000"
                includeMargin={true}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileAppPromotion;
