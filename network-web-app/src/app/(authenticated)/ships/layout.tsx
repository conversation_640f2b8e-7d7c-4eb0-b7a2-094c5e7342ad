import { Metadata } from 'next';
import { generateMetadata } from '@/lib/seo';
import { BreadcrumbStructuredData } from '@/components/StructuredData';

export const metadata: Metadata = generateMetadata({
  title: 'Ships - Navicater | Maritime Ship Details',
  description:
    'Search and explore maritime vessels, view ship details, specifications and crew information.',
  keywords: [
    'ship details',
    'ship imo',
    'imo search',
    'imo details',
    'ship specifications',
    'ship information',
    'vessel information',
    'vessel details',
    'vessel specifications',
  ],
  canonical: '/ships',
  openGraph: {
    title: 'Ship Details - Find detailed information about vessels.',
    description:
      'Search through the largest database of vessel information in India and find detailed information about ships.',
    type: 'website',
    url: '/ships',
    image:
      'https://b2c-space.tor1.cdn.digitaloceanspaces.com/WEBSITE/og_image.png',
    imageAlt: 'Ship Details - Find detailed information about vessels.',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ship Details - Find detailed information about vessels.',
    description:
      'Search through the largest database of vessel information in India and find detailed information about ships.',
    image:
      'https://b2c-space.tor1.cdn.digitaloceanspaces.com/WEBSITE/og_image.png',
    imageAlt: 'Ship Details - Find detailed information about vessels.',
  },
});

export default function ShipsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Ships', url: '/ships' },
  ];

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />
      {children}
    </>
  );
}
