'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  Header,
  MobileAppPromotion,
  ProfileSidebar,
  AnonymousProfileSidebar,
  ProfileSidebarSkeleton,
} from '@/components';
import { ShipLayoutPropsI } from './types';
import ShipTabs from '../ShipTabs';
import ShipDetails from '../ShipDetails';
import ShipPeople from '../ShipPeople';
import useAuth from '@/hooks/useAuth';
import useShipDetails from '../ShipDetails/useHook';

const ShipLayout = ({ imo }: ShipLayoutPropsI) => {
  const [activeTab, setActiveTab] = useState<'details' | 'people'>('details');
  const [isMounted, setIsMounted] = useState(false);
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();

  // Fetch ship details
  const {
    ship,
    isLoading: shipLoading,
    error,
  } = useShipDetails({ imo, dataType: 'master' });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Always call hooks at the top
  const shipSubtitle = useMemo(() => {
    if (!ship) return '';

    const { country, yearBuilt, imo: imoNum, name } = ship;

    const formatName = (str: string) => {
      return str
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    const formattedName = name ? formatName(name) : '-';

    return `${formattedName} is a vessel under the flag of ${country?.name || '-'} built in the year ${yearBuilt || '-'} with an IMO number of ${imoNum || '-'}.`;
  }, [ship]);

  const renderTabContent = useCallback(() => {
    switch (activeTab) {
      case 'details':
        return (
          <ShipDetails
            imo={imo}
            ship={ship}
            isLoading={shipLoading}
            error={error}
          />
        );
      case 'people':
        return <ShipPeople imo={imo} />;
      default:
        return (
          <ShipDetails
            imo={imo}
            ship={ship}
            isLoading={shipLoading}
            error={error}
          />
        );
    }
  }, [activeTab, ship, shipLoading, error, imo]);

  const renderProfileSidebar = (isMobile: boolean) => {
    if (authLoading) {
      return <ProfileSidebarSkeleton isMobile={isMobile} />;
    }
    if (isAuthenticated && user) {
      return <ProfileSidebar user={user} isMobile={isMobile} />;
    }
    return <AnonymousProfileSidebar isMobile={isMobile} />;
  };

  // ❌ no hooks after this point
  if (!isMounted) return null;

  return (
    <>
      <Header />
      <main className="py-20">
        <div className="max-w-screen-xl mx-auto px-4">
          <div className="lg:hidden mb-6">{renderProfileSidebar(true)}</div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <div className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20">{renderProfileSidebar(false)}</div>
            </div>

            <div className="lg:col-span-6">
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                {ship && !shipLoading && !error && (
                  <div className="px-6 pt-6 ">
                    <h2 className="text-2xl font-bold">{ship.name}</h2>
                    <p className="text-gray-600 text-sm mt-1">{shipSubtitle}</p>
                  </div>
                )}

                <div className="border-b border-gray-200 px-6 pt-4">
                  <ShipTabs activeTab={activeTab} onTabChange={setActiveTab} />
                </div>

                <div className="p-6">{renderTabContent()}</div>
              </div>
            </div>

            <aside className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20 space-y-6">
                <MobileAppPromotion />
              </div>
            </aside>
          </div>
        </div>
      </main>
    </>
  );
};

export default ShipLayout;
