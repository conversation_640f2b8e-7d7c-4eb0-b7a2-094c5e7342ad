'use client';

import { useState, useEffect, useCallback } from 'react';
import { ShipVisitorI } from '@/networks/ship/types';
import { fetchShipVisitorsAPI } from '@/networks/ship/visitors';

interface UseShipPeopleProps {
  imo: string;
  dataType?: string;
  pageSize?: number;
  isAuthenticated?: boolean;
  isAuthLoading?: boolean;
}

const useShipPeople = ({
  imo,
  dataType = 'master',
  pageSize = 10,
  isAuthenticated,
  isAuthLoading,
}: UseShipPeopleProps) => {
  const [visitors, setVisitors] = useState<ShipVisitorI[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const fetchVisitors = useCallback(
    async (page: number, resetData = false) => {
      if (!imo || !isAuthenticated) return;
      if (page === 1) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      try {
        const response = await fetchShipVisitorsAPI({
          imo,
          dataType,
          page,
          pageSize,
        });

        setTotal(response.total);

        if (resetData || page === 1) {
          setVisitors(response.data);
        } else {
          setVisitors(prev => [...prev, ...response.data]);
        }

        setHasMore(response.data.length === pageSize);
        setCurrentPage(page);
      } catch (err) {
        console.error('Error fetching ship visitors:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to fetch ship visitors'
        );
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [imo, dataType, pageSize, isAuthenticated]
  );

  useEffect(() => {
    if (isAuthLoading) return;

    if (isAuthenticated) {
      fetchVisitors(1, true);
    } else {
      setIsLoading(false);
    }
  }, [fetchVisitors, isAuthenticated, isAuthLoading]);

  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore && isAuthenticated) {
      fetchVisitors(currentPage + 1);
    }
  }, [fetchVisitors, currentPage, isLoadingMore, hasMore, isAuthenticated]);

  const refresh = useCallback(() => {
    if (!isAuthenticated) return;
    setCurrentPage(1);
    fetchVisitors(1, true);
  }, [fetchVisitors, isAuthenticated]);

  return {
    visitors,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    total,
    loadMore,
    refresh,
  };
};

export default useShipPeople;
