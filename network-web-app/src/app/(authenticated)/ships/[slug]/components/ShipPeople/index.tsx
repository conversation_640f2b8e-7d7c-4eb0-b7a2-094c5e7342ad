'use client';

import { ShipPeoplePropsI } from './types';
import ShipPeopleSkeleton from '../ShipPeopleSkeleton';
import useShipPeople from './useHook';
import useAuth from '@/hooks/useAuth';
import { AuthOverlay } from '@/components';
import Image from 'next/image';

const ShipPeople = ({ imo }: ShipPeoplePropsI) => {
  const { isAuthenticated, isLoading: isAuthLoading } = useAuth();
  const {
    visitors,
    isLoading,
    error,
    hasMore,
    loadMore,
    isLoadingMore,
    total,
  } = useShipPeople({
    imo,
    dataType: 'master',
    pageSize: 10,
    isAuthenticated,
    isAuthLoading,
  });

  if (isLoading) {
    return <ShipPeopleSkeleton />;
  }

  const content = (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">People ({total})</h3>

        {error ? (
          <div className="text-center text-red-600">
            <p>Error loading ship visitors</p>
            <p className="text-sm text-gray-600 mt-1">{error}</p>
          </div>
        ) : visitors.length === 0 ? (
          <div className="text-center text-gray-600 py-8">
            <p>No visitors found for this ship</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {visitors.map(person => (
                <div
                  key={person.id}
                  className="border border-gray-200 rounded-lg p-4 text-center"
                >
                  <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-3 flex items-center justify-center overflow-hidden">
                    {person.avatar ? (
                      <Image
                        src={person.avatar}
                        alt={person.name}
                        width={64} // same as w-16
                        height={64} // same as h-16
                        className="object-cover rounded-full"
                      />
                    ) : (
                      <span className="text-gray-600 text-xs">No Photo</span>
                    )}
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1">
                    {person.name}
                  </h4>
                  <p className="text-sm text-gray-600 mb-1">
                    {person.designation?.name || 'No designation'}
                  </p>
                  {person.entity && (
                    <p className="text-xs text-gray-500 mb-3">
                      {person.entity.name}
                    </p>
                  )}

                  {person.isConnected === null ? (
                    <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-lg">
                      You
                    </span>
                  ) : person.isConnected ? (
                    <button className="bg-blue-500 text-white px-4 py-1.5 rounded-lg text-sm hover:bg-blue-600">
                      Connected
                    </button>
                  ) : person.requestStatus === 'PENDING' ? (
                    <button className="bg-yellow-500 text-white px-4 py-1.5 rounded-lg text-sm hover:bg-yellow-600">
                      Pending
                    </button>
                  ) : (
                    <button className="bg-[#448600] text-white px-4 py-1.5 rounded-lg text-sm hover:bg-[#357000]">
                      Connect
                    </button>
                  )}
                </div>
              ))}
            </div>

            {hasMore && (
              <div className="mt-6 text-center">
                <button
                  onClick={loadMore}
                  disabled={isLoadingMore}
                  className="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 disabled:opacity-50"
                >
                  {isLoadingMore ? 'Loading...' : 'Load More'}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );

  if (!isAuthenticated) {
    return (
      <AuthOverlay
        isAuthenticated={false}
        maxHeight="300px"
        showGradient={true}
        compact={false}
      >
        <div className="filter blur-sm pointer-events-none">
          <ShipPeopleSkeleton />
        </div>
      </AuthOverlay>
    );
  }

  return content;
};

export default ShipPeople;
