// 'use client';

// import { ShipDetailsPropsI } from './types';
// import ShipDetailsSkeleton from '../ShipDetailsSkeleton';
// import useShipDetails from './useHook';
// import { formatShipMMSI, formatShipYear } from '@/utilities/ship';

// const ShipDetails = ({ imo }: ShipDetailsPropsI) => {
//   const { ship, isLoading, error } = useShipDetails({
//     imo, dataType:
//       'master'
//   });

//   if (isLoading) {
//     return <ShipDetailsSkeleton />;
//   }

//   if (error) {
//     return (
//       <div className="text-center text-red-600">
//         <p>Error loading ship details</p>
//         <p className="text-sm text-gray-600 mt-1">{error}</p>
//       </div>
//     );
//   }

//   if (!ship) {
//     return (
//       <div className="text-center text-gray-600">
//         <p>Ship not found</p>
//       </div>
//     );
//   }

//   const shipDetails = [
//     { label: 'Name', value: ship.name },
//     { label: 'Flag', value: ship.country?.name || '-' },
//     { label: 'IMO', value: ship.imo },
//     { label: 'MMSI', value: formatShipMMSI(ship.mmsi) },
//     { label: 'Call sign', value: ship.callSign || '-' },
//     { label: 'Main vessel type', value: ship.mainVesselType.name },
//     { label: 'Sub vessel type', value: ship.subVesselType.name },
//     { label: 'Year built', value: formatShipYear(ship.yearBuilt) },
//   ];

//   return (
//     <div className="space-y-6">
//       <div>
//         <h3 className="text-lg font-semibold mb-4">Ship Details</h3>

//         <div className="space-y-3">
//           {shipDetails.map((item, index) => (
//             <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
//               <span className="text-gray-600 font-medium">{item.label}</span>
//               <span className="text-gray-900">{item.value}</span>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ShipDetails;

'use client';

import { ShipDetailsComponentProps } from './types';
import ShipDetailsSkeleton from '../ShipDetailsSkeleton';
import { formatShipMMSI, formatShipYear } from '@/utilities/ship';

const ShipDetails = ({ ship, isLoading, error }: ShipDetailsComponentProps) => {
  if (isLoading) {
    return <ShipDetailsSkeleton />;
  }

  if (error) {
    return (
      <div className="text-center text-red-600">
        <p>Error loading ship details</p>
        <p className="text-sm text-gray-600 mt-1">{error}</p>
      </div>
    );
  }

  if (!ship) {
    return (
      <div className="text-center text-gray-600">
        <p>Ship not found</p>
      </div>
    );
  }

  const shipDetails = [
    { label: 'Name', value: ship.name },
    { label: 'Flag', value: ship.country?.name || '-' },
    { label: 'IMO', value: ship.imo },
    { label: 'MMSI', value: formatShipMMSI(ship.mmsi) },
    { label: 'Length', value: `${ship.length} Meter` || '-' },
    { label: 'Beam', value: `${ship.beam} Meter` || '-' },
    { label: 'GT', value: ship.gt || '-' },
    { label: 'Call sign', value: ship.callSign || '-' },
    { label: 'Main vessel type', value: ship.mainVesselType.name },
    { label: 'Sub vessel type', value: ship.subVesselType.name },
    { label: 'Year built', value: formatShipYear(ship.yearBuilt) },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Ship Details</h3>

        <div className="space-y-3 flex flex-col">
          {shipDetails.map((item, index) => (
            <div
              key={index}
              className="flex md:gap-28 items-center py-2 border-b border-gray-100 last:border-b-0"
            >
              <div className="text-gray-600 font-medium max-w-[150px] min-w-[150px]">
                {item.label}
              </div>
              <div className="text-gray-900 ml-4 text-left">{item.value}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ShipDetails;
