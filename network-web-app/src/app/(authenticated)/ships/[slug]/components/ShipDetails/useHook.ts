'use client';

import { useState, useEffect } from 'react';
import { ShipDetailI } from '@/networks/ship/types';
import { fetchShipDetailByImoNoAuthApi } from '@/networks/ship/shipDetail';

interface UseShipDetailsProps {
  imo: string;
  dataType?: string;
}

const useShipDetails = ({ imo, dataType = 'master' }: UseShipDetailsProps) => {
  const [ship, setShip] = useState<ShipDetailI | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchShip = async () => {
      if (!imo) return;

      setIsLoading(true);
      setError(null);

      try {
        const shipData = await fetchShipDetailByImoNoAuthApi({
          imo,
          dataType,
        });
        setShip(shipData);
      } catch (err) {
        console.error('Error fetching ship details:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to fetch ship details'
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchShip();
  }, [imo, dataType]);

  return {
    ship,
    isLoading,
    error,
  };
};

export default useShipDetails;
