import { ShipTabsPropsI } from './types';

const ShipTabs = ({ activeTab, onTabChange }: ShipTabsPropsI) => {
  const tabs: Array<{ id: 'details' | 'people'; label: string }> = [
    {
      id: 'details',
      label: 'Ship Details',
    },
    {
      id: 'people',
      label: 'People',
    },
  ];

  return (
    <div className="my-4">
      <div>
        <nav className="flex space-x-8">
          {tabs.map(tab => (
            <span
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                                py-2 px-1 border-b-2 font-medium text-sm
                                focus:outline-none focus-visible:outline-none
                                focus:ring-0 focus-visible:ring-0
                                active:outline-none
                                outline-none ring-0
                                transition-colors
                                cursor-pointer
                                ${
                                  activeTab === tab.id
                                    ? 'border-[#448600] text-[#448600]'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }
                            `}
            >
              {tab.label}
            </span>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default ShipTabs;
