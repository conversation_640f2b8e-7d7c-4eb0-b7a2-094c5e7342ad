'use client';

import { ShipSidebarPropsI } from './types';

const ShipSidebar = ({ imo, isMobile = false }: ShipSidebarPropsI) => {
  return (
    <div
      className={`bg-white rounded-lg border
  border-gray-200 overflow-hidden ${isMobile ? 'mb-6' : ''}`}
    >
      {/* Placeholder Ship Image */}
      <div
        className="aspect-video bg-gray-100 relative
  flex items-center justify-center"
      >
        <span className="text-gray-400">Ship Image</span>
      </div>

      {/* Ship Info */}
      <div className="p-4">
        <h2
          className="text-lg font-semibold text-gray-900
   mb-2"
        >
          Ship Name (Loading...)
        </h2>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">IMO:</span>
            <span className="font-medium">{imo}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-500">MMSI:</span>
            <span className="font-medium">Loading...</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-500">Flag:</span>
            <span className="font-medium">Loading...</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-500">Year Built:</span>
            <span className="font-medium">Loading...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShipSidebar;
