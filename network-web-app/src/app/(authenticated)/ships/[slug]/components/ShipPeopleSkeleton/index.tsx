'use client';

const ShipPeopleSkeleton = () => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="h-6 bg-gray-200 rounded w-24 mb-4 animate-pulse"></div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-3 animate-pulse"></div>

            <div className="h-4 bg-gray-200 rounded w-24 mx-auto mb-2 animate-pulse"></div>

            <div className="h-3 bg-gray-200 rounded w-20 mx-auto mb-1 animate-pulse"></div>

            <div className="h-3 bg-gray-200 rounded w-20 mx-auto mb-3 animate-pulse"></div>

            <div className="h-8 bg-gray-200 rounded w-20 mx-auto animate-pulse"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ShipPeopleSkeleton;
