import { Metadata } from 'next';
import { parseShipSlug } from '@/utilities/ship';

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { name, imo } = parseShipSlug(resolvedParams.slug);

  return {
    title: `${name.replace(/-/g, ' ')} (IMO: ${imo}) - Ship Details | Navicater`,
    description: `View details and crew information for ${name.replace(/-/g, ' ')}, IMO number ${imo}.`,
  };
}

export default function ShipLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
