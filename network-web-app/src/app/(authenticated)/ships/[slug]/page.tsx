'use client';

import { use } from 'react';
import { parseShipSlug } from '@/utilities/ship';
import ShipLayout from './components/ShipLayout';

interface PageProps {
  params: Promise<{ slug: string }>;
}

const ShipPage = ({ params }: PageProps) => {
  const resolvedParams = use(params);
  const { imo } = parseShipSlug(resolvedParams.slug);

  return <ShipLayout imo={imo} />;
};

export default ShipPage;
