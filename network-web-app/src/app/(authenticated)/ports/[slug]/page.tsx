'use client';

import { use } from 'react';
import { parsePortSlug } from '@/utilities/port';
import PortLayout from './components/PortLayout';

interface PageProps {
  params: Promise<{ slug: string }>;
}

const PortPage = ({ params }: PageProps) => {
  const resolvedParams = use(params);
  const { unLocode } = parsePortSlug(resolvedParams.slug);

  return <PortLayout unLocode={unLocode} slug={resolvedParams.slug} />;
};

export default PortPage;
