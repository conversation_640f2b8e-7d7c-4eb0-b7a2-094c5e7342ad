import { Metadata } from 'next';
import { parsePortSlug } from '@/utilities/port';

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const { name, unLocode } = parsePortSlug(resolvedParams.slug);

  return {
    title: `${name.replace(/-/g, ' ')} (UN/LOCODE: ${unLocode}) - Port Details | Navicater`,
    description: `View detailed information of port ${name.replace(/-/g, ' ')}, UN/LOCODE ${unLocode} at Navicater, community made for maritime enthusiasts.`,
  };
}

export default function PortLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
