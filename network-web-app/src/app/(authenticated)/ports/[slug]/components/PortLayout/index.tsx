'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  Header,
  MobileAppPromotion,
  ProfileSidebar,
  AnonymousProfileSidebar,
  ProfileSidebarSkeleton,
} from '@/components';
import { PortLayoutPropsI } from './types';
import PortTabs from '../PortTabs';
import PortDetails from '../PortDetails';
import PortVisitors from '../PortVisitors';
import PortScrapbook from '../PortScrapbook';
import useAuth from '@/hooks/useAuth';
import usePortDetails from '../PortDetails/useHook';
import { validatePortSlug, createPortSlug } from '@/utilities/port';

const PortLayout = ({ unLocode, slug }: PortLayoutPropsI) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<
    'about' | 'visitors' | 'scrapbook'
  >('about');
  const [isMounted, setIsMounted] = useState(false);
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();

  const {
    port,
    isLoading: portLoading,
    error,
  } = usePortDetails({ unLocode, dataType: 'master' });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (port && !portLoading && !error) {
      const isValidSlug = validatePortSlug(slug, port.name, port.unLocode);
      if (!isValidSlug) {
        const correctSlug = createPortSlug(port.name, port.unLocode);
        router.replace(`/ports/${correctSlug}`);
      }
    }
  }, [port, portLoading, error, slug, router]);

  const portSubtitle = useMemo(() => {
    if (!port) return '';

    const { country, city, unLocode: portUnLocode, name } = port;

    const formatName = (str: string) => {
      return str
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    const formattedName = name ? formatName(name) : '-';
    const location =
      [city?.name, country?.name].filter(Boolean).join(', ') || '-';

    return `${formattedName} is a port located in ${location} with UN/LOCODE ${portUnLocode}.`;
  }, [port]);

  const renderTabContent = useCallback(() => {
    if (
      !isAuthenticated &&
      (activeTab === 'visitors' || activeTab === 'scrapbook')
    ) {
      return (
        <div className="p-6 text-center">
          <p className="text-gray-500">Please log in to view this content.</p>
        </div>
      );
    }

    switch (activeTab) {
      case 'about':
        return (
          <PortDetails
            unLocode={unLocode}
            port={port || undefined}
            isLoading={portLoading}
            error={error}
          />
        );
      case 'visitors':
        return <PortVisitors unLocode={unLocode} />;
      case 'scrapbook':
        return <PortScrapbook unLocode={unLocode} />;
      default:
        return (
          <PortDetails
            unLocode={unLocode}
            port={port}
            isLoading={portLoading}
            error={error}
          />
        );
    }
  }, [activeTab, port, portLoading, error, unLocode, isAuthenticated]);

  const renderProfileSidebar = (isMobile: boolean) => {
    if (authLoading) {
      return <ProfileSidebarSkeleton isMobile={isMobile} />;
    }
    if (isAuthenticated && user) {
      return <ProfileSidebar user={user} isMobile={isMobile} />;
    }
    return <AnonymousProfileSidebar isMobile={isMobile} />;
  };

  if (!isMounted) return null;

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Port Not Found
          </h1>
          <p className="text-gray-600">
            The requested port could not be found.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Header />
      <main className="py-20">
        <div className="max-w-screen-xl mx-auto px-4">
          <div className="lg:hidden mb-6">{renderProfileSidebar(true)}</div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <div className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20">{renderProfileSidebar(false)}</div>
            </div>

            <div className="lg:col-span-6">
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                {port && !portLoading && !error && (
                  <div className="px-6 pt-6">
                    <h2 className="text-2xl font-bold">{port.name} Port</h2>
                    <p className="text-gray-600 text-sm mt-1">{portSubtitle}</p>
                  </div>
                )}

                <div className="border-b border-gray-200 px-6 pt-4">
                  <PortTabs
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                    isAuthenticated={isAuthenticated}
                  />
                </div>

                <div className="p-6">{renderTabContent()}</div>
              </div>
            </div>

            <aside className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20 space-y-6">
                <MobileAppPromotion />
              </div>
            </aside>
          </div>
        </div>
      </main>
    </>
  );
};

export default PortLayout;
