import { useState, useEffect, useCallback } from 'react';
import { PortScrapbookPostI } from '@/networks/port/types';
import { fetchPortScrapbookApi } from '@/networks/port/scrapbook';

interface UsePortScrapbookProps {
  unLocode: string;
}

interface UsePortScrapbookReturn {
  posts: PortScrapbookPostI[];
  isLoading: boolean;
  error: Error | null;
  hasMore: boolean;
  isLoadingMore: boolean;
  handleLoadMore: () => void;
  handleRefresh: () => void;
}

const usePortScrapbook = ({
  unLocode,
}: UsePortScrapbookProps): UsePortScrapbookReturn => {
  const [posts, setPosts] = useState<PortScrapbookPostI[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchPosts = useCallback(
    async (pageNum: number, reset = false) => {
      try {
        if (pageNum === 1) {
          setIsLoading(true);
        } else {
          setIsLoadingMore(true);
        }
        setError(null);

        const result = await fetchPortScrapbookApi({
          portUnLocode: unLocode,
          page: pageNum,
          pageSize: 10,
        });

        if (reset || pageNum === 1) {
          setPosts(result.data);
        } else {
          setPosts(prev => [...prev, ...result.data]);
        }

        setHasMore(result.pagination.hasMore);
        setPage(pageNum);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('Failed to fetch scrapbook')
        );
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [unLocode]
  );

  useEffect(() => {
    if (unLocode) {
      fetchPosts(1, true);
    }
  }, [unLocode, fetchPosts]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      fetchPosts(page + 1);
    }
  }, [fetchPosts, page, isLoadingMore, hasMore]);

  const handleRefresh = useCallback(() => {
    fetchPosts(1, true);
  }, [fetchPosts]);

  return {
    posts,
    isLoading,
    error,
    hasMore,
    isLoadingMore,
    handleLoadMore,
    handleRefresh,
  };
};

export default usePortScrapbook;
