import React from 'react';
import { PortScrapbookPropsI } from './types';
import usePortScrapbook from './useHook';
import Image from 'next/image';

const PortScrapbook = ({ unLocode }: PortScrapbookPropsI) => {
  const {
    posts,
    isLoading,
    error,
    hasMore,
    isLoadingMore,
    handleLoadMore,
    handleRefresh,
  } = usePortScrapbook({ unLocode });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#448600]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Error loading scrapbook</p>
        <button
          onClick={handleRefresh}
          className="mt-2 text-[#448600] hover:underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (!posts.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No scrapbook posts yet</p>
        <p className="text-gray-400 text-sm mt-1">
          Be the first to share something!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {posts.map(post => (
        <div
          key={post.id}
          className="border border-gray-200 hover:border-gray-400 rounded-lg p-4"
        >
          <div className="flex items-start space-x-3">
            {post.profile?.avatar ? (
              <div className="w-10 h-10 rounded-full overflow-hidden relative">
                <Image
                  src={post.profile.avatar}
                  alt={post.profile.name}
                  layout="intrinsic"
                  width={40} // Equivalent to w-10 (10 * 4px = 40px)
                  height={40} // Equivalent to h-10 (10 * 4px = 40px)
                  className="object-cover"
                />
              </div>
            ) : (
              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-gray-600 text-sm">
                  {post.profile?.name?.charAt(0).toUpperCase() || '?'}
                </span>
              </div>
            )}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h4 className="font-medium text-gray-900">
                  {post.profile?.name || 'Unknown'}
                </h4>
                <span className="text-gray-500 text-sm">
                  {new Date(post.createdAt).toLocaleDateString()}
                </span>
              </div>
              {post.textPreview && (
                <p className="text-gray-700 mb-3">{post.textPreview}</p>
              )}
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center space-x-1">
                  <span>👍</span>
                  <span>{post.reactionCount || 0}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <span>💬</span>
                  <span>{post.commentCount || 0}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}

      {hasMore && (
        <div className="text-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoadingMore}
            className="px-4 py-2 text-[#448600] hover:bg-green-50 rounded-lg disabled:opacity-50"
          >
            {isLoadingMore ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  );
};

export default PortScrapbook;
