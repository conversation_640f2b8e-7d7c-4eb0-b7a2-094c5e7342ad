import { useState, useEffect } from 'react';
import { PortDetailI, PortDetailQueryI } from '@/networks/port/types';
import {
  fetchPortDetailByUnLocodeApi,
  fetchPortDetailByUnLocodeNoAuthApi,
} from '@/networks/port/portDetail';
import useAuth from '@/hooks/useAuth';

interface UsePortDetailsProps {
  unLocode: string;
  dataType?: string;
}

interface UsePortDetailsReturn {
  port: PortDetailI | null;
  isLoading: boolean;
  error: Error | null;
}

const usePortDetails = ({
  unLocode,
  dataType = 'master',
}: UsePortDetailsProps): UsePortDetailsReturn => {
  const [port, setPort] = useState<PortDetailI | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (!unLocode) return;

    const fetchPortDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const query: PortDetailQueryI = { unLocode, dataType };

        const result = isAuthenticated
          ? await fetchPortDetailByUnLocodeApi(query)
          : await fetchPortDetailByUnLocodeNoAuthApi(query);

        setPort(result);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('Failed to fetch port details')
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchPortDetails();
  }, [unLocode, dataType, isAuthenticated]);

  return { port, isLoading, error };
};

export default usePortDetails;
