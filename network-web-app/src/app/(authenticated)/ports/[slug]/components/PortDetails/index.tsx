import React from 'react';
import { PortDetailsPropsI } from './types';
import Image from 'next/image';

const PortDetails = ({ port, isLoading, error }: PortDetailsPropsI) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Error loading port details</p>
      </div>
    );
  }

  if (!port) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Port not found</p>
      </div>
    );
  }

  const details = [
    { label: 'UN/LOCODE', value: port.unLocode },
    { label: 'Country', value: port.country?.name || '-' },
    { label: 'City', value: port.city?.name || '-' },
    { label: 'Latitude', value: port.latitude || '-' },
    { label: 'Longitude', value: port.longitude || '-' },
    { label: 'Number of Berths', value: port.noOfBerths?.toString() || '-' },
    {
      label: 'Number of Terminals',
      value: port.noOfTerminals?.toString() || '-',
    },
    {
      label: 'Max Draught',
      value: port.maxDraught ? `${port.maxDraught}m` : '-',
    },
    {
      label: 'Max Deadweight',
      value: port.maxDeadweight ? `${port.maxDeadweight} DWT` : '-',
    },
    { label: 'Max Length', value: port.maxLength ? `${port.maxLength}m` : '-' },
    {
      label: 'Max Air Draught',
      value: port.maxAirDraught ? `${port.maxAirDraught}m` : '-',
    },
    { label: 'Timezone', value: port.timezone?.timezone || '-' },
  ];

  return (
    <div className="space-y-6">
      {port.imageUrl && (
        <div className="w-full h-48 rounded-lg overflow-hidden relative">
          <Image
            src={port.imageUrl}
            alt={port.name}
            layout="fill"
            objectFit="cover"
            className="w-full h-full"
          />
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {details.map((detail, index) => (
          <div key={index} className="border-b border-gray-100 pb-2">
            <dt className="text-sm font-medium text-gray-500">
              {detail.label}
            </dt>
            <dd className="mt-1 text-sm text-gray-900">{detail.value}</dd>
          </div>
        ))}
      </div>

      {port.timezone && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">
            Timezone Information
          </h3>
          <div className="space-y-1 text-sm text-gray-600">
            <p>Timezone: {port.timezone.timezone}</p>
            <p>UTC Offset: {port.timezone.utcOffset} minutes</p>
            <p>DST Offset: {port.timezone.dstOffset} minutes</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PortDetails;
