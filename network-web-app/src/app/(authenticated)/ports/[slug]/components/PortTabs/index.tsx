import { PortTabsPropsI } from './types';

const PortTabs = ({
  activeTab,
  onTabChange,
  isAuthenticated,
}: PortTabsPropsI) => {
  const tabs: Array<{
    id: 'about' | 'visitors' | 'scrapbook';
    label: string;
    requiresAuth: boolean;
  }> = [
    {
      id: 'about',
      label: 'About',
      requiresAuth: false,
    },
    {
      id: 'visitors',
      label: 'Visitors',
      requiresAuth: true,
    },
    {
      id: 'scrapbook',
      label: 'Scrapbook',
      requiresAuth: true,
    },
  ];

  return (
    <div className="my-4">
      <div>
        <nav className="flex space-x-8">
          {tabs.map(tab => (
            <span
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                  py-2 px-1 border-b-2 font-medium text-sm
                  focus:outline-none focus-visible:outline-none
                  focus:ring-0 focus-visible:ring-0
                  active:outline-none
                  outline-none ring-0
                  transition-colors
                  cursor-pointer
                  ${
                    activeTab === tab.id
                      ? 'border-[#448600] text-[#448600]'
                      : `border-transparent ${
                          tab.requiresAuth && !isAuthenticated
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`
                  }
                  ${tab.requiresAuth && !isAuthenticated ? 'opacity-50' : ''}
                `}
            >
              {tab.label}
              {tab.requiresAuth && !isAuthenticated && (
                <span className="ml-1 text-xs">🔒</span>
              )}
            </span>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default PortTabs;
