import { useState, useEffect, useCallback } from 'react';
import { PortVisitorI } from '@/networks/port/types';
import { fetchPortVisitorsApi } from '@/networks/port/visitors';

interface UsePortVisitorsProps {
  unLocode: string;
  dataType?: string;
}

interface UsePortVisitorsReturn {
  visitors: PortVisitorI[];
  isLoading: boolean;
  error: Error | null;
  hasMore: boolean;
  isLoadingMore: boolean;
  handleLoadMore: () => void;
  handleRefresh: () => void;
}

const usePortVisitors = ({
  unLocode,
  dataType = 'master',
}: UsePortVisitorsProps): UsePortVisitorsReturn => {
  const [visitorsResult, setVisitorsResult] = useState<{
    total: number;
    data: PortVisitorI[];
  }>({
    total: 0,
    data: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const fetchVisitors = useCallback(
    async (pageNum: number, reset = false) => {
      try {
        if (pageNum === 0) {
          setIsLoading(true);
        } else {
          setIsLoadingMore(true);
        }
        setError(null);

        const result = await fetchPortVisitorsApi({
          unLocode,
          dataType,
          page: pageNum,
          pageSize: 10,
        });

        if (reset || pageNum === 0) {
          setVisitorsResult({
            total: result.total,
            data: result.data,
          });
        } else {
          setVisitorsResult(prev => ({
            total: result.total,
            data: [...prev.data, ...result.data],
          }));
        }

        setHasMore(result.data.length === 10);
        setPage(pageNum);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('Failed to fetch visitors')
        );
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [unLocode, dataType]
  );

  useEffect(() => {
    if (unLocode && dataType) {
      fetchVisitors(0, true);
    }
  }, [unLocode, dataType, fetchVisitors]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && !isLoading && hasMore) {
      fetchVisitors(page + 1);
    }
  }, [fetchVisitors, page, isLoadingMore, isLoading, hasMore]);

  const handleRefresh = useCallback(() => {
    fetchVisitors(0, true);
  }, [fetchVisitors]);

  return {
    visitors: visitorsResult.data,
    isLoading,
    error,
    hasMore,
    isLoadingMore,
    handleLoadMore,
    handleRefresh,
  };
};

export default usePortVisitors;
