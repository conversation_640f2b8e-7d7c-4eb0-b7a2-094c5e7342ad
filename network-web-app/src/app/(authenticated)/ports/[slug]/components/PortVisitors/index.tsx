import React, { useState } from 'react';
import { PortVisitorsPropsI } from './types';
import usePortVisitors from './useHook';
import Image from 'next/image';

const PortVisitors = ({
  unLocode,
  dataType = 'master',
}: PortVisitorsPropsI) => {
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  const {
    visitors,
    isLoading,
    error,
    hasMore,
    isLoadingMore,
    handleLoadMore,
    handleRefresh,
  } = usePortVisitors({ unLocode, dataType });

  const handleImageError = (visitorId: string) => {
    setImageErrors(prev => ({ ...prev, [visitorId]: true }));
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#448600]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Error loading visitors</p>
        <button
          onClick={handleRefresh}
          className="mt-2 text-[#448600] hover:underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (!visitors.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No visitors found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {visitors.map(visitor => {
          const hasImageError = imageErrors[visitor.id];
          const showAvatar = visitor.avatar && !hasImageError;

          return (
            <div
              key={visitor.id}
              className="border border-gray-200 hover:border-gray-400 rounded-lg p-4"
            >
              <div className="flex items-center space-x-3">
                {showAvatar ? (
                  <div className="w-12 h-12 rounded-full overflow-hidden relative">
                    <Image
                      src={visitor.avatar || '/default-avatar.png'}
                      alt={visitor.name}
                      layout="intrinsic"
                      width={48}
                      height={48}
                      className="object-cover"
                      referrerPolicy="no-referrer"
                      crossOrigin="anonymous"
                      onError={() => handleImageError(visitor.id)}
                    />
                  </div>
                ) : (
                  <div className="w-12 h-12 bg-gradient-to-br from-[#448600] to-green-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-semibold">
                      {getInitials(visitor.name)}
                    </span>
                  </div>
                )}
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{visitor.name}</h3>
                  {visitor.designation && (
                    <p className="text-sm text-gray-500">
                      {visitor.designation.name}
                    </p>
                  )}
                  {visitor.designationText && !visitor.designation && (
                    <p className="text-sm text-gray-500">
                      {visitor.designationText}
                    </p>
                  )}
                </div>
                {visitor.isConnected && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Connected
                  </span>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="text-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoadingMore}
            className="px-4 py-2 text-[#448600] hover:bg-green-50 rounded-lg disabled:opacity-50"
          >
            {isLoadingMore ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  );
};

export default PortVisitors;
