'use client';

import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import {
  Button,
  Input,
  Select,
  Toggle,
  Textarea,
  CurrencySelect,
} from '@/components';
import type { JobFormDataI } from '../PostJobForm/types';
import TrashIcon from '@assets/svg/Trash';

const salaryTypeOptions = [
  { value: 'CONTRACTUAL', label: 'Contractual' },
  { value: 'ROUND_THE_YEAR', label: 'Round the Year' },
  { value: 'OTHER', label: 'Other' },
];
const insuranceTypeOptions = [
  { value: 'SELF', label: 'Self' },
  { value: 'FAMILY', label: 'Family' },
  { value: 'OTHER', label: 'Other' },
];
const yesNoOptions = [
  { value: 'YES', label: 'Yes' },
  { value: 'NO', label: 'No' },
];
const itfTypeOptions = [
  { value: 'ITF', label: 'ITF' },
  { value: 'NON_ITF', label: 'Non-ITF' },
];
const contractUnitOptions = [
  { value: 'months', label: 'Months' },
  { value: 'days', label: 'Days' },
];

const JobBenefits: React.FC<{ onSubmit: () => void; onBack: () => void }> = ({
  onSubmit,
  onBack,
}) => {
  const { control } = useFormContext<JobFormDataI>();
  const benefitFA = useFieldArray({ control, name: 'benefitItems' as const });

  return (
    <div className="space-y-8">
      <p className="text-[#959090] font-medium mb-3">Step 3/3 : Benefits</p>

      {/* Salary row */}
      <section className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end">
        <div className="md:col-span-3">
          <Controller
            control={control}
            name="salaryType"
            render={({ field }) => (
              <Select
                label="Salary type"
                placeholder="Select salary type"
                options={salaryTypeOptions}
                value={field.value}
                onValueChange={field.onChange}
              />
            )}
          />
        </div>
        <div className="md:col-span-3">
          <CurrencySelect
            control={control as any}
            name="salaryCurrency"
            label="Currency"
            placeholder="Select"
          />
        </div>
        <div className="md:col-span-2">
          <Controller
            control={control}
            name="salaryMin"
            render={({ field }) => (
              <Input {...field} type="number" label="Min" placeholder="Value" />
            )}
          />
        </div>
        <div className="md:col-span-2">
          <Controller
            control={control}
            name="salaryMax"
            render={({ field }) => (
              <Input {...field} type="number" label="Max" placeholder="Value" />
            )}
          />
        </div>
        <div className="md:col-span-2 flex items-center md:justify-around justify-between">
          <Controller
            control={control}
            name="salaryMandatory"
            render={({ field }) => (
              <Toggle
                checked={!!field.value}
                onChange={field.onChange}
                label="Mandatory"
              />
            )}
          />
        </div>
      </section>

      {/* Contract duration */}
      <section className="space-y-3">
        <h3 className="font-semibold text-gray-900">Contract duration</h3>
        <div className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end">
          <div className="md:col-span-3">
            <Controller
              control={control}
              name="contractUnit"
              render={({ field }) => (
                <Select
                  label="Months/days"
                  placeholder="Select"
                  options={contractUnitOptions}
                  value={field.value}
                  onValueChange={field.onChange}
                />
              )}
            />
          </div>
          <div className="md:col-span-3">
            <Controller
              control={control}
              name="contractDuration"
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  label="Duration"
                  placeholder="Enter"
                />
              )}
            />
          </div>
        </div>
      </section>

      {/* Internet */}
      <section className="space-y-3">
        <h3 className="font-semibold text-gray-900">Internet</h3>
        <div className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end">
          <div className="md:col-span-4">
            <Controller
              control={control}
              name="internetAvailability"
              render={({ field }) => (
                <Select
                  label="Internet"
                  placeholder="Select"
                  options={yesNoOptions}
                  value={field.value}
                  onValueChange={field.onChange}
                />
              )}
            />
          </div>
          <div className="md:col-span-4">
            <Controller
              control={control}
              name="internetSpeed"
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  label="Speed(Mbps)"
                  placeholder="Enter speed"
                />
              )}
            />
          </div>
          <div className="md:col-span-4">
            <Controller
              control={control}
              name="internetLimitPerDay"
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  label="Limit per day(MB)"
                  placeholder="Enter limit"
                />
              )}
            />
          </div>
          <div className="md:col-span-12">
            <Controller
              control={control}
              name="internetDetails"
              render={({ field }) => (
                <Textarea
                  {...field}
                  label="Type in More Details"
                  placeholder="Type in More Details"
                />
              )}
            />
          </div>
        </div>
      </section>

      {/* Insurance / Family onboard */}
      <section className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end">
        <div className="md:col-span-4">
          <Controller
            control={control}
            name="insuranceType"
            render={({ field }) => (
              <Select
                label="Insurance type"
                placeholder="Select insurance type"
                options={insuranceTypeOptions}
                value={field.value}
                onValueChange={field.onChange}
              />
            )}
          />
        </div>
        <div className="md:col-span-4">
          <Controller
            control={control}
            name="itfType"
            render={({ field }) => (
              <Select
                label="ITF/Non-ITF"
                placeholder="Select"
                options={itfTypeOptions}
                value={field.value}
                onValueChange={field.onChange}
              />
            )}
          />
        </div>
        <div className="md:col-span-4">
          <Controller
            control={control}
            name="familyOnboard"
            render={({ field }) => (
              <Select
                label="Family onboard"
                placeholder="Select"
                options={yesNoOptions}
                value={field.value}
                onValueChange={field.onChange}
              />
            )}
          />
        </div>
      </section>

      {/* Benefits dynamic rows */}
      <section className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Benefits</h3>
          <button
            type="button"
            onClick={() => benefitFA.append({ details: '' })}
            className="text-primary hover:underline font-medium"
          >
            Add new benefits
          </button>
        </div>
        <div className="space-y-3">
          {benefitFA.fields.map((f, idx) => (
            <div
              key={f.id}
              className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
            >
              <div className="md:col-span-2">
                <Input
                  label="No"
                  placeholder="Auto"
                  disabled
                  value={String(idx + 1)}
                />
              </div>
              <div className="md:col-span-9">
                <Controller
                  control={control}
                  name={`benefitItems.${idx}.details` as const}
                  render={({ field }) => (
                    <Input
                      {...field}
                      label="Details"
                      placeholder="Enter details"
                    />
                  )}
                />
              </div>
              <div className="md:col-span-1 flex items-center md:justify-end justify-between">
                <div
                  onClick={() => benefitFA.remove(idx)}
                  className="cursor-pointer"
                >
                  <TrashIcon size={24} />
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      <div className="flex items-center gap-3 pt-2">
        <Button
          type="button"
          className="bg-gray-200 text-gray-800 hover:bg-gray-300 text-white"
          onClick={onBack}
        >
          Previous step
        </Button>
        <Button
          type="button"
          className="bg-green-700 hover:bg-green-800 text-white"
          onClick={onSubmit}
        >
          Generate benefits
        </Button>
      </div>
    </div>
  );
};

export default JobBenefits;
