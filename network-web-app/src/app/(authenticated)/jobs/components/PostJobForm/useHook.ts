import {
  BasicJobDataPayloadI,
  JobBenefitsPayloadI,
  JobFormDataI,
  JobRequirementsPayloadI,
} from './types';
import showToast from '@/utils/toast';
import { useForm } from 'react-hook-form';
import { useState } from 'react';
import {
  createJobPost,
  updateBenefits,
  updateRequirements,
} from '@/networks/jobs/submitJob';
import { useRouter } from 'next/navigation';

const defaultValues: JobFormDataI = {
  entityId: '',
  designationId: '',
  departmentId: '',
  shipImo: '',
  jobType: '',
  locationCountryIso2: '',
  genderDiversityIndex: 0.0,
  expiryDate: '',
  isOfficial: false,
  minYears: 0,
  minSalary: 0,
  shipType: undefined,
  about: '',
  roles: '',
  benefits: '',
  certifications: [
    {
      certification: '',
      country: '',
      mandatory: false,
    },
  ],
  experiences: [
    {
      designation: '',
      shipType: '',
      months: 0,
      mandatory: false,
    },
  ],
  equipments: [
    {
      type: '',
      make: '',
      model: '',
      fuelType: false,
      moe: 0,
      mandatory: false,
    },
  ],
  cargos: [
    {
      name: '',
      code: '',
      months: 0,
      mandatory: false,
    },
  ],
  skills: [
    {
      name: '',
      mandatory: false,
    },
  ],
  otherRequirements: [
    {
      no: 1,
      details: '',
      mandatory: false,
    },
  ],
  salaryType: 'CONTRACTUAL',
  salaryCurrency: 'USD',
  salaryMin: 0,
  salaryMax: 0,
  salaryMandatory: true,
  contractUnit: 'months',
  contractDuration: 0,
  internetAvailability: 'NO',
  internetSpeed: 0,
  internetLimitPerDay: 0,
  internetDetails: '',
  insuranceType: 'SELF',
  itfType: 'ITF',
  familyOnboard: 'NO',
  benefitItems: [
    {
      no: 1,
      details: '',
    },
  ],
};

const usePostJobForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmittingRequirements, setIsSubmittingRequirements] =
    useState(false);
  const [isSubmittingBenefits, setIsSubmittingBenefits] = useState(false);
  const [step, setStep] = useState<number>(1);
  const methods = useForm<JobFormDataI>({
    defaultValues,
    mode: 'onChange',
  });

  const router = useRouter();

  const handleBack = () => {
    switch (step) {
      case 2:
        setStep(1);
        break;
      case 3:
        setStep(2);
      default:
    }
  };

  const onSubmit = async (data: JobFormDataI) => {
    try {
      setIsSubmitting(true);
      if (data.applicationType === 'link' && !data.applicationLink) {
        showToast({ type: 'error', message: 'Application Link is required' });
        return;
      }
      if (data.applicationType === 'email' && !data.applicationEmail) {
        showToast({ type: 'error', message: 'Application Email is required' });
        return;
      }

      const payload: BasicJobDataPayloadI = {
        entity: data.entity!,
        designation: data.designation!,
        department: data.department!,
        expiryDate: new Date(data.expiryDate),
        isOfficial: data.isOfficial,
        minYears: data.minYears,
        minSalary: data.minSalary,
      };

      if (data.jobType)
        payload.jobType = data.jobType as 'SAILING' | 'NON_SAILING';
      if (data.locationCountryIso2)
        payload.countryIso2 = data.locationCountryIso2;
      if (typeof data.genderDiversityIndex === 'number')
        payload.genderDiversityIndex = data.genderDiversityIndex;
      if (typeof data.maxYears === 'number') payload.maxYears = data.maxYears;
      if (typeof data.maxSalary === 'number')
        payload.maxSalary = data.maxSalary;

      if (data.ship && data.ship.imo && data.ship.dataType)
        payload.ship = data.ship;

      if (data.applicationType === 'link')
        payload.applicationMethod = 'EXTERNAL_LINK';
      else if (data.applicationType === 'email')
        payload.applicationMethod = 'EMAIL';
      else payload.applicationMethod = 'IN_APP';

      if (data.applicationLink) payload.applicationUrl = data.applicationLink;
      if (data.applicationEmail)
        payload.applicationEmail = data.applicationEmail;

      console.log('Submitting Stage 1 payload', payload);
      const result = await createJobPost(payload);

      const draftId = (result as { id?: string } | null)?.id;
      if (draftId) {
        methods.setValue('draftJobId', draftId);
      }

      showToast({
        type: 'success',
        message: 'Step 1 saved',
        description: 'Proceeding to step 2...',
      });
      setStep(2);
    } catch (e) {
      console.error(e);
      showToast({ type: 'error', message: 'Error submitting form' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitRequirements = async () => {
    const draftId = methods.getValues('draftJobId');
    if (!draftId) {
      showToast({ type: 'error', message: 'Draft job not created yet' });
      return;
    }
    try {
      setIsSubmittingRequirements(true);
      const data = methods.getValues();
      const payload: JobRequirementsPayloadI = {
        stage: '2',
        about: data.about || undefined,
        rolesResponsibilities: data.roles || undefined,
        requirementType: 'ADVANCED',
        benefits: data.benefits || undefined,
      };
      await updateRequirements({ draftId, payload });
      showToast({ type: 'success', message: 'Requirements saved' });
      setStep(3);
    } catch (e) {
      console.error(e);
      showToast({ type: 'error', message: 'Error saving requirements' });
    } finally {
      setIsSubmittingRequirements(false);
    }
  };

  const submitBenefits = async () => {
    const draftId = methods.getValues('draftJobId');
    if (!draftId) {
      showToast({ type: 'error', message: 'Draft job not created yet' });
      return;
    }
    try {
      setIsSubmittingBenefits(true);
      const data = methods.getValues();

      const internetAvailable = data.internetAvailability
        ? data.internetAvailability === 'YES'
        : undefined;
      const familyOnboard = data.familyOnboard
        ? data.familyOnboard === 'YES'
        : undefined;

      const payload: JobBenefitsPayloadI = {
        stage: '3',
        benefitType:
          (data.benefitItems && data.benefitItems.length > 0) || data.salaryType
            ? 'ADVANCED'
            : 'BASIC',
        benefits: data.benefits || undefined,
        salaryType: data.salaryType || undefined,
        currencyCode: data.salaryCurrency || undefined,
        minSalary:
          typeof data.salaryMin === 'number' ? data.salaryMin : undefined,
        maxSalary:
          typeof data.salaryMax === 'number' ? data.salaryMax : undefined,
        showSalary: true,
        contractMonths:
          data.contractUnit === 'months' &&
          typeof data.contractDuration === 'number'
            ? data.contractDuration
            : undefined,
        contractDays:
          data.contractUnit === 'days' &&
          typeof data.contractDuration === 'number'
            ? data.contractDuration
            : undefined,
        internetAvailable,
        internetSpeed:
          typeof data.internetSpeed === 'number'
            ? data.internetSpeed
            : undefined,
        internetLimitPerDay: data.internetLimitPerDay,
        internetDetails: data.internetDetails,
        insuranceType: data.insuranceType,
        familyOnboard,
        itfType: data.itfType || undefined,
        benefitDetails: (data.benefitItems || [])
          .filter(i => i && i.details && i.details.trim() !== '')
          .map(i => ({ details: i.details!.trim() })),
      };

      await updateBenefits({ draftId, payload });
      showToast({ type: 'success', message: 'Benefits saved' });
      router.push('/jobs');
    } catch (e) {
      console.error(e);
      showToast({ type: 'error', message: 'Error saving benefits' });
    } finally {
      setIsSubmittingBenefits(false);
    }
  };

  return {
    methods,
    onSubmit,
    isSubmitting,
    isSubmittingRequirements,
    step,
    setStep,
    submitRequirements,
    submitBenefits,
    isSubmittingBenefits,
    handleBack,
  };
};

export default usePostJobForm;
