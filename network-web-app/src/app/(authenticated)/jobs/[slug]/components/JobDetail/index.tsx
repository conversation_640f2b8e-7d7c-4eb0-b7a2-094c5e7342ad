import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components';
import { closeJobAPI, fetchJobDetailAPI } from '@/networks/jobs';
import type { JobCandidateFetchOneResultI } from '@/networks/jobs/types';

export type JobDetailPropsI = {
  slug: string;
};

const JobDetail: React.FC<JobDetailPropsI> = ({ slug }) => {
  const router = useRouter();
  const [job, setJob] = useState<JobCandidateFetchOneResultI | null>(null);
  const [loading, setLoading] = useState(false);
  const [closing, setClosing] = useState(false);

  const load = useCallback(async () => {
    try {
      setLoading(true);
      const res = await fetchJobDetailAPI(slug);
      setJob(res);
    } catch (e) {
      console.error('Failed to load job', e);
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    load();
  }, [load]);

  const onCloseJob = async () => {
    try {
      if (!job?.id) return;
      setClosing(true);
      await closeJobAPI(job.id);
      await load();
    } catch (e) {
      console.error('Failed to close job', e);
    } finally {
      setClosing(false);
    }
  };

  const title = job?.designation?.name || 'Job';
  const companyName = job?.entity?.name || '—';
  const createdAt = job?.createdAt
    ? new Date(job.createdAt).toLocaleDateString()
    : '';

  return (
    <section className="lg:col-span-6 col-span-1" data-slug={slug}>
      <div className="bg-white border border-gray-200 rounded-2xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-start justify-between gap-4">
            <div>
              <div className="flex items-center text-gray-600 text-sm">
                <span className="inline-block h-4 w-4 rounded-full bg-gray-200 mr-2" />
                {companyName}
              </div>
              <h1 className="mt-1 text-2xl font-semibold text-gray-900">
                {title}
              </h1>
              <div className="mt-1 text-gray-600 text-sm">{createdAt}</div>
            </div>
            <button
              type="button"
              aria-label="More options"
              className="text-gray-400 hover:text-gray-600"
            >
              ···
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-5">
            <Button
              variant="outline"
              className="w-full h-10"
              onClick={() => router.push(`/jobs/${slug}/applicants`)}
              disabled={!job}
            >
              View applicants
            </Button>
            <Button
              variant="outline"
              className="w-full h-10"
              onClick={onCloseJob}
              disabled={!job || closing}
            >
              {closing ? 'Closing…' : 'Close job'}
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-8">
          {loading ? (
            <div className="text-center text-gray-500">Loading…</div>
          ) : (
            <>
              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  About
                </h3>
                <p className="text-[15px] leading-6 text-gray-700">
                  {/* Placeholder for description until backend exposes details */}
                  The organisation is hiring for {title}.
                </p>
              </section>

              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  Roles and responsibilities
                </h3>
                <ul className="list-disc pl-5 text-[15px] leading-6 text-gray-700 space-y-1">
                  <li>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  </li>
                  <li>Pellentesque scelerisque diam eget fringilla tempor</li>
                  <li>Sed facilisis porttitor purus venenatis ultrices</li>
                  <li>Donec nunc quam, vestibulum eget faucibus sit amet,</li>
                </ul>
              </section>
            </>
          )}
        </div>
      </div>
    </section>
  );
};

export default JobDetail;
