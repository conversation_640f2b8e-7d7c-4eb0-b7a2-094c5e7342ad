'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useParams } from 'next/navigation';
import DashboardLayout from '../../../forums/components/DashboardLayout';
import { SearchInput, Tabs } from '@/components';
import Filter from '@assets/svg/Filter';
import { fetchApplicantsForJobAPI } from '@/networks/jobs';
import type {
  ApplicantsStatusI,
  FetchApplicantsResultItemI,
} from '@/networks/jobs/types';

const ApplicantsPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const tabs = [
    { id: 'PENDING', label: 'Applied' },
    { id: 'SHORTLISTED', label: 'Shortlisted' },
    { id: 'ACCEPTED_BY_APPLICANT', label: 'Accepted' },
    { id: 'REJECTED_BY_ENTITY', label: 'Rejected' },
    { id: 'OFFERED', label: 'Offered' },
  ];
  const [activeTab, setActiveTab] = useState<ApplicantsStatusI>('PENDING');
  const [search, setSearch] = useState('');

  const [items, setItems] = useState<FetchApplicantsResultItemI[]>([]);
  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const fetchApplicants = useCallback(
    async (isLoadMore = false) => {
      try {
        if (!slug) return;
        if (isLoadMore) {
          if (!hasMore || !nextCursorId) return;
          setLoadingMore(true);
        } else {
          setLoading(true);
        }
        const res = await fetchApplicantsForJobAPI({
          jobId: slug as string,
          status: activeTab,
          cursorId: isLoadMore && nextCursorId ? String(nextCursorId) : null,
          pageSize: 10,
        });
        setItems(prev => (isLoadMore ? [...prev, ...res.data] : res.data));
        setNextCursorId(res.nextCursorId);
        setHasMore(!!res.nextCursorId && res.data.length > 0);
      } catch (e) {
        console.error('Failed to fetch applicants', e);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [slug, activeTab, nextCursorId, hasMore]
  );

  useEffect(() => {
    setItems([]);
    setNextCursorId(null);
    setHasMore(true);
    fetchApplicants(false);
  }, [activeTab, fetchApplicants]);

  useEffect(() => {
    const el = loadMoreRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
          fetchApplicants(true);
        }
      },
      { root: null, rootMargin: '100px', threshold: 0.1 }
    );
    observer.observe(el);
    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore]);

  const filtered = useMemo(() => {
    const s = search.trim().toLowerCase();
    const source = items.map(a => ({
      id: a.id,
      name: a.ApplicantProfile?.name || '—',
      title: a.DecisionMakerProfile?.name || '',
      avatar: a.ApplicantProfile?.avatar || null,
    }));
    if (!s) return source;
    return source.filter(
      a => a.name.toLowerCase().includes(s) || a.title.toLowerCase().includes(s)
    );
  }, [items, search]);

  return (
    <DashboardLayout>
      <section className="lg:col-span-6 col-span-1">
        <div className="bg-white border border-gray-200 rounded-2xl shadow-sm overflow-hidden">
          {/* Tabs */}
          <div className="px-4 pt-4">
            <Tabs
              items={tabs}
              activeId={activeTab}
              onChange={id => setActiveTab(id as ApplicantsStatusI)}
            />
          </div>

          {/* Search + Filter */}
          <div className="px-4 py-3">
            <SearchInput
              value={search}
              onChange={e => setSearch(e.target.value)}
              rightSlot={<Filter />}
            />
          </div>

          {/* List */}
          {loading && items.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">Loading…</div>
          ) : filtered.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">
              No results found
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {filtered.map(a => (
                <li key={`${activeTab}-${a.id}`} className="px-4">
                  <div className="flex gap-4 py-4">
                    <div className="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-100" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900">
                        {a.name}
                      </p>
                      <p className="text-sm text-gray-700">{a.title}</p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}

          <div ref={loadMoreRef} />
        </div>
      </section>
    </DashboardLayout>
  );
};

export default ApplicantsPage;
