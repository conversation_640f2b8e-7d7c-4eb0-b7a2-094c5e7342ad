'use client';

import { useForm } from 'react-hook-form';
import { DesignationSelect, OrganizationSelect, Button } from '@/components';
import { onboardingWorkAPI } from '@/networks/user/onboarding';
import { useState } from 'react';

type WorkDetailsFormData = {
  designation: string;
  entity: string;
};

type WorkDetailsStepProps = {
  onBack: () => void;
};

const WorkDetailsStep = ({ onBack }: WorkDetailsStepProps) => {
  const [designationItem, setDesignationItem] = useState<{
    id: string;
    dataType: string;
    name?: string;
  } | null>(null);

  const [entityItem, setEntityItem] = useState<{
    id: string;
    dataType: string;
  } | null>(null);

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
    setError,
  } = useForm<WorkDetailsFormData>({
    defaultValues: {
      designation: '',
      entity: '',
    },
  });

  const onSubmit = async (data: WorkDetailsFormData) => {
    try {
      if (!data.designation) {
        throw new Error('Please select a designation');
      }

      const payload: {
        designation: { id: string; dataType: string };
        entity?: { id: string; dataType: string };
      } = {
        designation: {
          id: designationItem!.id,
          dataType: designationItem!.dataType,
        },
      };

      if (entityItem?.id && entityItem?.dataType) {
        payload.entity = {
          id: entityItem!.id,
          dataType: entityItem!.dataType,
        };
      }

      await onboardingWorkAPI(payload);
      localStorage.removeItem('username');
      localStorage.removeItem('userPersonalData');
      // Navigate to main app after completion
      window.location.href = '/forums';
    } catch (error) {
      console.error('Error saving work details:', error);
      setError('designation', {
        type: 'manual',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to save work details. Please try again.',
      });
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">Work Details</h1>
        <p className="text-gray-600">
          Share your professional background and current role.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <DesignationSelect
          name="designation"
          control={control}
          onChange={item => {
            setDesignationItem(item);
          }}
          isRequired={true}
        />

        {designationItem && designationItem.name !== 'Aspirant' && (
          <OrganizationSelect
            name="entity"
            control={control}
            isRequired={false}
            onChange={item => {
              setEntityItem(item);
            }}
          />
        )}

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            onClick={onBack}
            className="flex-1 rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Completing...' : 'Complete'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default WorkDetailsStep;
