export type CountriesI = {
  iso2: string;
  name: string;
};

export type FetchCountryResponseI = {
  data: CountriesI[];
  total: number;
};

export type FetchCountryParamsI = {
  page: string;
  search?: string;
};

export type CurrencyI = {
  code: string;
  name: string;
};

export type FetchCurrencyResponseI = {
  data: CurrencyI[];
  total: number;
};

export type FetchCurrencyParamsI = {
  page: string;
  search?: string;
};
