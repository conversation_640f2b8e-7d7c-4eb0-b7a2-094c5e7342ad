import { apiCall } from '@/lib/api';
import { FetchCurrencyParamsI, FetchCurrencyResponseI } from './types';

export const fetchCurrencies = async (
  params: FetchCurrencyParamsI
): Promise<FetchCurrencyResponseI> => {
  return await apiCall<FetchCurrencyParamsI, FetchCurrencyResponseI>(
    '/backend/api/v1/master/currency/options',
    'GET',
    {
      isAuth: true,
      query: { ...params, pageSize: 10 },
    }
  );
};
