import {
  BasicJobDataPayloadI,
  JobBenefitsPayloadI,
  JobRequirementsPayloadI,
} from '@/app/(authenticated)/jobs/components/PostJobForm/types';
import { apiCall } from '@/lib/api';

export const createJobPost = async (payload: BasicJobDataPayloadI) => {
  return await apiCall('/backend/api/v1/company/job/details', 'POST', {
    isAuth: true,
    payload,
  });
};

export const updateRequirements = async ({
  draftId,
  payload,
}: {
  draftId: string;
  payload: JobRequirementsPayloadI;
}): Promise<void> => {
  return await apiCall(
    `/backend/api/v1/company/job/${draftId}/requirements`,
    'PATCH',
    {
      isAuth: true,
      payload,
    }
  );
};

export const updateBenefits = async ({
  draftId,
  payload,
}: {
  draftId: string;
  payload: JobBenefitsPayloadI;
}): Promise<void> => {
  return await apiCall(
    `/backend/api/v1/company/job/${draftId}/benefits`,
    'PATCH',
    {
      isAuth: true,
      payload,
    }
  );
};
