import { apiCall } from '@/lib/api';
import { PortDetailI, PortDetailQueryI } from './types';

export const fetchPortDetailByUnLocodeApi = async (
  query: PortDetailQueryI
): Promise<PortDetailI> => {
  const result = await apiCall<null, PortDetailI>(
    '/backend/api/v1/port/port/single',
    'GET',
    {
      isAuth: true,
      query: {
        unLocode: query.unLocode,
        dataType: query.dataType || 'master',
      },
    }
  );
  return result;
};

export const fetchPortDetailByUnLocodeNoAuthApi = async (
  query: PortDetailQueryI
): Promise<PortDetailI> => {
  const result = await apiCall<null, PortDetailI>(
    '/backend/api/v1/unsecure/port/port/single/noauth',
    'GET',
    {
      isAuth: false,
      query: {
        unLocode: query.unLocode,
        dataType: query.dataType || 'master',
      },
    }
  );
  return result;
};
