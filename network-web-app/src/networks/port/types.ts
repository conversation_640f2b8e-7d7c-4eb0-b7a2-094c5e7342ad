export interface PortDetailI {
  unLocode: string;
  name: string;
  imageUrl: string | null;
  city: {
    name: string;
  };
  country: {
    name: string;
  };
  latitude: string;
  longitude: string;
  noOfTerminals: number | null;
  noOfBerths: number;
  maxDraught: number | null;
  maxDeadweight: number | null;
  maxLength: number | null;
  maxAirDraught: number | null;
  dataType: string;
  isVisited: boolean;
  timezone: {
    timezone: string;
    utcOffset: number;
    dstOffset: number;
  };
}

export interface PortDetailQueryI {
  unLocode: string;
  dataType?: string;
}

export interface PortVisitorsQueryI {
  unLocode: string;
  dataType?: string;
  page: number;
  pageSize: number;
}

export interface PortVisitorI {
  id: string;
  name: string;
  avatar: string | null;
  designation?: {
    id: string;
    name: string;
    dataType: string;
  } | null;
  designationText?: string;
  isConnected: boolean;
  requestStatus?: 'PENDING' | 'ACCEPTED' | 'REJECTED' | null;
}

export interface PortVisitorsResultI {
  data: PortVisitorI[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
}

export interface PortScrapbookQueryI {
  portUnLocode: string;
  page?: number;
  pageSize?: number;
}

export interface PortScrapbookPostI {
  id: string;
  text: string;
  textPreview: string | null;
  isCaptionTruncated: boolean;
  reactionCount: number;
  commentCount: number;
  isLiked: boolean;
  createdAt: string;
  profile?: {
    id: string;
    name: string;
    avatar: string | null;
    designationText?: string | null;
  };
}

export interface PortScrapbookResultI {
  data: PortScrapbookPostI[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
}

export interface ScrapbookReactionListQueryI {
  scrapBookPostId: string;
  page?: number;
  pageSize?: number;
}

export interface ScrapbookReactionUpsertBodyI {
  scrapBookPostId: string;
  reactionType: 'LIKE' | 'LOVE' | 'DISLIKE';
}

export interface ScrapbookReactionDeleteBodyI {
  scrapBookPostId: string;
}

export interface ScrapbookReactionResultI {
  data: Array<{
    id: string;
    reactionType: string;
    profile: {
      id: string;
      name: string;
      avatar: string | null;
    };
  }>;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
}

export interface ScrapbookPostCreateBodyI {
  portUnLocode: string;
  caption: string;
  mediaUrls?: string[];
}

export interface ScrapBookPostDeletePayloadI {
  id: string;
}

export interface ScrapBookCommentFetchManyQueryI {
  scrapBookPostId: string;
  page?: number;
  pageSize?: number;
}

export interface ScrapBookCommentFetchRepliesQueryI {
  parentCommentId: string;
  page?: number;
  pageSize?: number;
}

export interface ScrapBookCommentCreateBodyI {
  scrapBookPostId: string;
  text: string;
  parentCommentId?: string;
}

export interface ScrapBookCommentI {
  id: string;
  text: string;
  createdAt: string;
  repliesCount: number;
  profile: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

export interface ScrapBookCommentFetchManyResultI {
  data: ScrapBookCommentI[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
}

export interface ScrapBookCommentCreateOneResultI {
  id: string;
  text: string;
  createdAt: string;
  repliesCount: number;
  profile: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

export interface ScrapBookPostCaptionResponse {
  caption: string;
}

export interface AddPortVisitorPayloadI {
  portUnLocode: string;
}

export interface FetchPortVisitorQueryI {
  unLocode: string;
  dataType?: string;
  page?: number;
  pageSize?: number;
}

export interface FetchVisitorsResult {
  data: PortVisitorI[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
}

export interface FetchPortProfileQueryI {
  unLocode: string;
  dataType?: string;
}

export interface PortProfileData {
  unLocode: string;
  name: string;
  imageUrl: string | null;
  city: {
    name: string;
  };
  country: {
    name: string;
  };
  latitude: string;
  longitude: string;
  noOfTerminals: number | null;
  noOfBerths: number;
  maxDraught: number | null;
  maxDeadweight: number | null;
  maxLength: number | null;
  maxAirDraught: number | null;
  dataType: string;
  isVisited: boolean;
  timezone: {
    timezone: string;
    utcOffset: number;
    dstOffset: number;
  };
}
