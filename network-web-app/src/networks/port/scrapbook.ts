import { apiCall } from '@/lib/api';
import {
  PortScrapbookQueryI,
  PortScrapbookResultI,
  PortScrapbookPostI,
} from './types';

export const fetchPortScrapbookApi = async (
  query: PortScrapbookQueryI
): Promise<PortScrapbookResultI> => {
  const result = await apiCall<
    null,
    {
      total: number;
      data: PortScrapbookPostI[];
    }
  >('/backend/api/v1/port/scrap-book/posts', 'GET', {
    isAuth: true,
    query,
  });

  return {
    data: result.data,
    pagination: {
      page: query.page || 1,
      pageSize: query.pageSize || 10,
      total: result.total,
      hasMore:
        result.data.length === (query.pageSize || 10) && result.data.length > 0,
    },
  };
};
