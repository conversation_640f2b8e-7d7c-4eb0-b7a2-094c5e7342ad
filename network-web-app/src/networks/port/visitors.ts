import { apiCall } from '@/lib/api';
import { PortVisitorI, PortVisitorsQueryI } from './types';

export const fetchPortVisitorsApi = async (
  query: PortVisitorsQueryI
): Promise<{ total: number; data: PortVisitorI[] }> => {
  const result = await apiCall<null, { total: number; data: PortVisitorI[] }>(
    '/backend/api/v1/port/scrap-book/visitors',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};
