import { apiCall } from '@/lib/api';

export type EntityProfileForUserI = {
  id: string;
  name: string;
  avatar: string | null;
  isVerified: boolean;
  role: string;
};

export const fetchEntityProfilesForUserAPI = async (): Promise<
  EntityProfileForUserI[]
> => {
  const result = await apiCall<void, EntityProfileForUserI[]>(
    '/backend/api/v1/company/entity-profiles',
    'GET',
    { isAuth: true }
  );
  return result;
};
