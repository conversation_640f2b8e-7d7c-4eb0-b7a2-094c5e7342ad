import { apiCall } from '@/lib/api';
import { ShipDetailI, ShipDetailQueryI } from './types';

export const fetchShipDetailByImoApi = async (
  query: ShipDetailQueryI
): Promise<ShipDetailI> => {
  const result = await apiCall<null, ShipDetailI>(
    '/backend/api/v1/ship/single',
    'GET',
    {
      isAuth: true,
      query: {
        imo: query.imo,
        dataType: query.dataType || 'master',
      },
    }
  );
  return result;
};

export const fetchShipDetailByImoNoAuthApi = async (
  query: ShipDetailQueryI
): Promise<ShipDetailI> => {
  const result = await apiCall<null, ShipDetailI>(
    '/backend/api/v1/unsecure/ship/single/noauth',
    'GET',
    {
      isAuth: false,
      query: {
        imo: query.imo,
        dataType: query.dataType || 'master',
      },
    }
  );
  return result;
};
