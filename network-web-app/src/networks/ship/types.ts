export interface ShipDetailI {
  imo: string;
  mmsi: number;
  callSign?: string;
  name: string;
  imageUrl: string;
  yearBuilt: number;
  length?: number;
  beam?: number;
  gt?: number;
  country?: {
    name: string;
  };
  mainVesselType: {
    name: string;
  };
  subVesselType: {
    name: string;
  };
  dataType: string;
  shipNames?: Array<{
    name: string;
    fromDate: string | null;
    toDate: string | null;
  }>;
}

export interface ShipVisitorI {
  id: string;
  name: string;
  avatar: string | null;
  designation: {
    id: string;
    name: string;
    dataType: 'master' | 'raw';
  } | null;
  entity: {
    id: string;
    name: string;
    dataType: 'master' | 'raw';
  } | null;
  isConnected: boolean | null;
  requestStatus: string | null;
}

export interface ShipVisitorsResponseI {
  data: ShipVisitorI[];
  total: number;
}

export interface ShipDetailQueryI {
  imo: string;
  dataType: string;
}

export interface ShipVisitorsQueryI {
  imo: string;
  dataType: string;
  page?: number;
  pageSize?: number;
}
