import { apiCall } from '@/lib/api';
import { ShipVisitorsQueryI, ShipVisitorsResponseI } from './types';

export const fetchShipVisitorsAPI = async (
  query: ShipVisitorsQueryI
): Promise<ShipVisitorsResponseI> => {
  const result = await apiCall<null, ShipVisitorsResponseI>(
    '/backend/api/v1/ship/visitors',
    'GET',
    {
      isAuth: true,
      query: {
        imo: query.imo,
        dataType: query.dataType || 'master',
        page: (query.page || 1) - 1,
        pageSize: query.pageSize || 10,
      },
    }
  );
  return result;
};
