import { apiCall } from '@/lib/api';
import { SearchResponse, ShipSearchResult } from './types';

export const getPopularShips = async (): Promise<
  SearchResponse<ShipSearchResult>
> => {
  const response = await apiCall<
    never,
    {
      total: number;
      data: ShipSearchResult[];
    }
  >('/backend/api/v1/unsecure/ship/popular', 'GET', { isAuth: false });

  return {
    results: response.data,
    total: response.total,
  };
};

export const searchShips = async (
  search: string
): Promise<SearchResponse<ShipSearchResult>> => {
  const response = await apiCall<
    never,
    {
      total: number;
      data: ShipSearchResult[];
    }
  >('/backend/api/v1/unsecure/ship/search', 'GET', {
    isAuth: false,
    query: { search, pageSize: 5, page: 0 },
  });

  return {
    results: response.data,
    total: response.total,
  };
};
