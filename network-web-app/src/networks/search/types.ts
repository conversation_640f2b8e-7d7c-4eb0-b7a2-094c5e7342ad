export interface OptionSearchBodyI {
  search?: string;
  page?: string;
  type?: string;
}

export interface OptionSearchResultI {
  id: string;
  name: string;
  dataType: string;
}

export interface CountryOptionSearchResultI {
  iso2: string;
  name: string;
}

export interface EntitySearchResponse {
  data: OptionSearchResultI[];
  total: number;
}

export interface SearchResultI {
  id: string;
  name: string;
  dataType: string;
}

export interface ShipSearchResult {
  imo: string;
  name: string;
  dataType: string;
  imageUrl: string;
  matchedName: string;
}

export interface PortSearchResult {
  unLocode: string;
  name: string;
  city: {
    id: string;
    name: string;
    dataType: string;
  };
  dataType: string;
}

export interface SearchResponse<T> {
  results: T[];
  total: number;
}

export type SearchTab = 'ships' | 'ports';
