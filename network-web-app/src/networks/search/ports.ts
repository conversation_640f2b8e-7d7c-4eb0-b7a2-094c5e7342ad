import { apiCall } from '@/lib/api';
import { SearchResponse, PortSearchResult } from './types';

export const getPopularPorts = async (): Promise<
  SearchResponse<PortSearchResult>
> => {
  const response = await apiCall<
    never,
    {
      total: number;
      data: PortSearchResult[];
    }
  >('/backend/api/v1/unsecure/port/port/popular', 'GET', { isAuth: false });

  return {
    results: response.data,
    total: response.total,
  };
};

export const searchPorts = async (
  search: string
): Promise<SearchResponse<PortSearchResult>> => {
  const response = await apiCall<
    never,
    {
      total: number;
      data: PortSearchResult[];
    }
  >('/backend/api/v1/unsecure/port/port/search', 'GET', {
    isAuth: false,
    query: { search },
  });

  return {
    results: response.data,
    total: response.total,
  };
};
