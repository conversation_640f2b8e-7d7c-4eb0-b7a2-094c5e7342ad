import React from 'react'

const TrashIcon = ({ size = 24, color, className = '' }: { size?: number; color?: string; className?: string }) => {
    return (
        <svg className={className} width={size ?? "32"} height={size ?? "32"} viewBox="0 0 32 32" fill={color ?? "none"} xmlns="http://www.w3.org/2000/svg">
            <path d="M13.25 16V22.6666" stroke="black" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M18.75 16V22.6666" stroke="black" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M5 9.3335H27" stroke="black" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M7.75 13.333V23.9996C7.75 26.2088 9.59683 27.9996 11.875 27.9996H20.125C22.4032 27.9996 24.25 26.2088 24.25 23.9996V13.333" stroke="black" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M11.875 6.66665C11.875 5.1939 13.1062 4 14.625 4H17.375C18.8938 4 20.125 5.1939 20.125 6.66665V9.3333H11.875V6.66665Z" stroke="black" strokeLinecap="round" strokeLinejoin="round" />
        </svg>

    )
}

export default TrashIcon