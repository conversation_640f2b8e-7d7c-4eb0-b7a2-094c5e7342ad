import React from 'react';

type ForumIconPropsI = {
  size?: number;
  color?: string;
  className?: string;
}

const ForumIcon: React.FC<ForumIconPropsI> = ({
  size = 22,
  color = 'black',
  className = '',
}) => {
  return (
    <svg className={className} width={size} height={size} viewBox="0 0 40 40" fill={color ?? "none"} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_6078_8352)">
        <path d="M10 7.75H35C35.6932 7.75 36.2514 7.97995 36.6357 8.36426C37.02 8.74856 37.25 9.30681 37.25 10V27.5C37.25 28.1932 37.02 28.7514 36.6357 29.1357C36.2514 29.52 35.6932 29.75 35 29.75H27.1201L27.0449 29.8564L21.9424 37.1436L20.3438 35.9453L26.1299 27.75H35.25V9.75H9.75V27.75H19.75V29.75H10C9.30681 29.75 8.74856 29.52 8.36426 29.1357C7.97995 28.7514 7.75 28.1932 7.75 27.5V10C7.75 9.30681 7.97995 8.74856 8.36426 8.36426C8.74856 7.97995 9.30681 7.75 10 7.75Z" fill="black" stroke="#F8F8F8" strokeWidth="0.5" />
        <path d="M6.25 2.75H22.25V4.75H6.25C5.81819 4.75 5.43894 4.89505 5.16699 5.16699C4.89505 5.43894 4.75 5.81819 4.75 6.25V22.25H2.75V6.25C2.75 4.26307 4.26307 2.75 6.25 2.75Z" fill="black" stroke="#F8F8F8" strokeWidth="0.5" />
      </g>
      <defs>
        <clipPath id="clip0_6078_8352">
          <rect width="40" height="40" fill="white" />
        </clipPath>
      </defs>
    </svg>

  );
};

export default ForumIcon;