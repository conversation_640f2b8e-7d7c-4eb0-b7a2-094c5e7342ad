import React from 'react';

interface SuitcaseIconProps {
  size?: number;
  color?: string;
  className?: string;
  onClick?: () => void;
}

const SuitCase: React.FC<SuitcaseIconProps> = ({
  size = 18,
  className = '',
}) => {
  return (
    <svg width={`${size}px`} height={`${size}px`} className={`suitcase-icon ${className}`} viewBox="0 0 40 40" fill={"none"} xmlns="http://www.w3.org/2000/svg">
      <path d="M15 11.6667H8.33333C6.49238 11.6667 5 13.159 5 15V30C5 31.841 6.49238 33.3333 8.33333 33.3333H31.6667C33.5077 33.3333 35 31.841 35 30V15C35 13.159 33.5077 11.6667 31.6667 11.6667H25M15 11.6667V8.33333C15 6.49238 16.4924 5 18.3333 5H21.6667C23.5077 5 25 6.49238 25 8.33333V11.6667M15 11.6667H25" stroke="black" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

  );
};

export default SuitCase