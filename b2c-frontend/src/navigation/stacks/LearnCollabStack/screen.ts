import CommunityScreen from '@/src/screens/Community';
import CommunityBlockedScreen from '@/src/screens/CommunityBlocked';
import CommunityMembersScreen from '@/src/screens/CommunityMembers';
import CommunityQuestionScreen from '@/src/screens/CommunityQuestion';
import CommunityRequestScreen from '@/src/screens/CommunityRequests';
import CommunityRestrictionScreen from '@/src/screens/CommunityRestrictions';
import CreateCommunityScreen from '@/src/screens/CreateCommunity';
import CreateQuestion from '@/src/screens/CreateQuestion';
import EditDetailScreen from '@/src/screens/EditDetail';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import ExploreScreen from '@/src/screens/Explore';
import ExploreForumScreen from '@/src/screens/ExploreForum';
import ExploreQnaScreen from '@/src/screens/ExploreQna';
import ExploreTroubleShootScreen from '@/src/screens/ExploreTroubleshoot';
import ForumScreen from '@/src/screens/Forum';
import ForumAnswersScreen from '@/src/screens/ForumAnswers';
import ForumCommentsScreen from '@/src/screens/ForumComments';
import ForumFilterScreen from '@/src/screens/ForumFilter';
import ForumSearchScreen from '@/src/screens/ForumSearch';
import ForumSettingScreen from '@/src/screens/ForumSetting';
import MyCommunitiesScreen from '@/src/screens/MyCommunities';
import PeopleScreen from '@/src/screens/People';
import UserProfileScreen from '@/src/screens/UserProfile';
import VotesScreen from '@/src/screens/Votes';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';
import { LearnCollabStackParamsListI, StackScreenI } from '../../types';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'Forum',
    component: ForumScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading forum. Please try again later.',
  },
  {
    name: 'ForumAnswers',
    component: ForumAnswersScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading answers. Please try again later.',
  },
  {
    name: 'ForumComments',
    component: ForumCommentsScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading comments. Please try again later.',
  },
  {
    name: 'ForumSearch',
    component: ForumSearchScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading search screen. Please try again later.',
  },
  {
    name: 'ForumFilter',
    component: ForumFilterScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading forum filter. Please try again later.',
  },
  {
    name: 'CreateCommunity',
    component: CreateCommunityScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading create community screen. Please try again later.',
  },
  {
    name: 'CommunityRestrictions',
    component: CommunityRestrictionScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading community restriction screen. Please try again later.',
  },
  {
    name: 'People',
    component: PeopleScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading people screen. Please try again later.',
  },
  {
    name: 'CommunityQuestion',
    component: CommunityQuestionScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading community question screen. Please try again later.',
  },
  {
    name: 'SearchScreen',
    component: EntitySearchScreen,
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  },
  {
    name: 'CreateQuestion',
    component: CreateQuestion,
    title: 'Create Question Error',
    subtitle: 'Something went wrong during posting question. Please try again.',
  },
  {
    name: 'ForumSetting',
    component: ForumSettingScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading forum settings. Please try again later.',
  },
  {
    name: 'CommunityMembers',
    component: CommunityMembersScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading member list. Please try again later.',
  },
  {
    name: 'CommunityBlocked',
    component: CommunityBlockedScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading blocked list. Please try again later.',
  },
  {
    name: 'CommunityRequests',
    component: CommunityRequestScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading user requests. Please try again later.',
  },
  {
    name: 'Votes',
    component: VotesScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading votes. Please try again later.',
  },
  {
    name: 'Community',
    component: CommunityScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading community. Please try again later.',
  },
  {
    name: 'MyCommunities',
    component: MyCommunitiesScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading my communities. Please try again later.',
  },
  {
    name: 'Explore',
    component: ExploreScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading explore. Please try again later.',
  },
  {
    name: 'ExploreQna',
    component: ExploreQnaScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading explore QnA. Please try again later.',
  },
  {
    name: 'ExploreTroubleShoot',
    component: ExploreTroubleShootScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading explore trouble shoot. Please try again later.',
  },
  {
    name: 'ExploreForum',
    component: ExploreForumScreen,
    title: 'Error',
    subtitle: 'Something went wrong loading explore forum. Please try again later.',
  },
  {
    name: 'EditDetail',
    component: EditDetailScreen,
    title: 'Edit Details Error',
    subtitle: 'Something went wrong while editing details. Please try again.',
  },
  {
    name: 'OtherUserProfile',
    component: UserProfileScreen,
    title: 'User Profile Error',
    subtitle: 'Something went wrong loading the user profile. Please try again.',
  },
];

export const screens: StackScreenI<LearnCollabStackParamsListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof LearnCollabStackParamsListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
