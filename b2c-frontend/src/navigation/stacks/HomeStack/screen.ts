import type { HomeStackParamListI, StackScreenI } from '@/src/navigation/types';
import AIChatScreen from '@/src/screens/AIChat';
import ChatScreen from '@/src/screens/Chat';
import ChatsScreen from '@/src/screens/Chats';
import CommentScreen from '@/src/screens/Comment';
import ConnectionScreen from '@/src/screens/Connection';
import CreateEntityProfileScreen from '@/src/screens/CreateEntityProfile';
import CreateEntityProfileSuccessScreen from '@/src/screens/CreateEntityProfileSuccess';
import EditCertificationListScreen from '@/src/screens/EditCertificationList';
import EditDocumentsListScreen from '@/src/screens/EditDocumentsList';
import EditEducationListScreen from '@/src/screens/EditEducationList';
import EditEntityProfilecreen from '@/src/screens/EditEntityProfile';
import EditExperienceListScreen from '@/src/screens/EditExperienceList';
import EditShipItemScreen from '@/src/screens/EditShipItem';
import EditSkillsListScreen from '@/src/screens/EditSkillsList';
import EntityProfileScreen from '@/src/screens/EntityProfile';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import Follows from '@/src/screens/Follows';
import GlobalSearchScreen from '@/src/screens/GlobalSearch';
import HomeScreen from '@/src/screens/Home';
import LeaderBoardScreen from '@/src/screens/LeaderBoard';
import LikesScreen from '@/src/screens/Likes';
import NotFoundScreen from '@/src/screens/NotFound';
import PortProfileScreen from '@/src/screens/PortProfile';
import PortsVisitedScreen from '@/src/screens/PortsVisited';
import ReferralDetailsScreen from '@/src/screens/ReferralDetails';
import ShipProfileScreen from '@/src/screens/ShipProfile';
import OtherUserProfileScreen from '@/src/screens/UserProfile';
import UserSettingScreen from '@/src/screens/UserSettings';
import VerificationReviewSentSuccessScreen from '@/src/screens/VerificationReviewSentSuccess';
import VerifyEntityEmailScreen from '@/src/screens/VerifyEntityEmail';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'Home',
    component: HomeScreen,
    title: 'Home Feed Error',
    subtitle: 'Something went wrong loading the home feed. Please try again.',
  },
  {
    name: 'Comment',
    component: CommentScreen,
    title: 'Comments Error',
    subtitle: 'Something went wrong loading comments. Please try again.',
  },
  {
    name: 'Likes',
    component: LikesScreen,
    title: 'Likes Error',
    subtitle: 'Something went wrong loading likes. Please try again.',
  },
  {
    name: 'ShipProfile',
    component: ShipProfileScreen,
    title: 'Ship Profile Error',
    subtitle: 'Something went wrong loading the ship profile. Please try again.',
  },
  {
    name: 'PortProfile',
    component: PortProfileScreen,
    title: 'Port Profile Error',
    subtitle: 'Something went wrong loading the port profile. Please try again.',
  },
  {
    name: 'OtherUserProfile',
    component: OtherUserProfileScreen,
    title: 'User Profile Error',
    subtitle: 'Something went wrong loading the user profile. Please try again.',
  },
  {
    name: 'Connection',
    component: ConnectionScreen,
    title: 'Connections Error',
    subtitle: 'Something went wrong loading connections. Please try again.',
  },
  {
    name: 'Chats',
    component: ChatsScreen,
    title: 'Chats Error',
    subtitle: 'Something went wrong loading your chats. Please try again.',
  },
  {
    name: 'Chat',
    component: ChatScreen,
    title: 'Chat Error',
    subtitle: 'Something went wrong in the chat. Please try again.',
  },
  {
    name: 'GlobalSearch',
    component: GlobalSearchScreen,
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  },
  {
    name: 'NotFound',
    component: NotFoundScreen,
    title: 'Page Not Found Error',
    subtitle: 'Something went wrong loading this page. Please try again.',
  },
  {
    name: 'AIChat',
    component: AIChatScreen,
    title: 'AI Chat Error',
    subtitle: 'Something went wrong with the AI chat. Please try again.',
  },
  {
    name: 'EditEducationList',
    component: EditEducationListScreen,
    title: 'Education View Error',
    subtitle: 'Something went wrong loading your education. Please try again.',
  },
  {
    name: 'EditCertificationList',
    component: EditCertificationListScreen,
    title: 'Certifications View Error',
    subtitle: 'Something went wrong loading your certifications. Please try again.',
  },
  {
    name: 'EditDocumentList',
    component: EditDocumentsListScreen,
    title: 'Documents View Error',
    subtitle: 'Something went wrong loading your documents. Please try again.',
  },
  {
    name: 'EditSkillsList',
    component: EditSkillsListScreen,
    title: 'Skills Error',
    subtitle: 'Something went wrong loading your skills. Please try again.',
  },
  {
    name: 'EditExperienceList',
    component: EditExperienceListScreen,
    title: 'Experience View Error',
    subtitle: 'Something went wrong loading your experience. Please try again.',
  },
  {
    name: 'EditShipItem',
    component: EditShipItemScreen,
    title: 'Ship Details View Error',
    subtitle: 'Something went wrong loading the ship details. Please try again.',
  },
  {
    name: 'PortsVisited',
    component: PortsVisitedScreen,
    title: 'Ports Visited Error',
    subtitle: 'Something went wrong loading ports visited. Please try again.',
  },
  {
    name: 'SearchScreen',
    component: EntitySearchScreen,
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  },
  {
    name: 'UserSettings',
    component: UserSettingScreen,
    title: 'Settings Error',
    subtitle: 'Something went wrong loading your settings. Please try again.',
  },
  {
    name: 'ReferralDetails',
    component: ReferralDetailsScreen,
    title: 'Referral Details Error',
    subtitle: 'Something went wrong loading Referral Details. Please try again.',
  },
  {
    name: 'Leaderboard',
    component: LeaderBoardScreen,
    title: 'Leaderboard Error',
    subtitle: 'Something went wrong loading leaderboard. Please try again.',
  },
  {
    name: 'CreateEntityProfile',
    component: CreateEntityProfileScreen,
    title: 'Creation of Entity Profile Error',
    subtitle: 'Something went wrong loading EntityProfile. Please try again.',
  },
  {
    name: 'VerifyEntityEmail',
    component: VerifyEntityEmailScreen,
    title: 'Verification Error',
    subtitle: 'Something went wrong loading verification. Please try again.',
  },
  {
    name: 'CreateEntityProfileSuccess',
    component: CreateEntityProfileSuccessScreen,
    title: 'Error',
    subtitle: 'Something went wrong. Please try again.',
  },
  {
    name: 'VerificationReviewSentSuccess',
    component: VerificationReviewSentSuccessScreen,
    title: 'Error',
    subtitle: 'Something went wrong. Please try again.',
  },
  {
    name: 'EntityProfile',
    component: EntityProfileScreen,
    title: 'Error',
    subtitle: 'Something went wrong. Please try again.',
  },
  {
    name: 'EditEntityProfile',
    component: EditEntityProfilecreen,
    title: 'Error',
    subtitle: 'Something went wrong. Please try again.',
  },
  {
    name: 'Follows',
    component: Follows,
    title: 'Error',
    subtitle: 'Something went wrong. Please try again.',
  },
];

export const screens: StackScreenI<HomeStackParamListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof HomeStackParamListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
