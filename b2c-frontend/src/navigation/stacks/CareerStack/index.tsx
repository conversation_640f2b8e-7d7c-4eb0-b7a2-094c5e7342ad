import { createStackNavigator } from '@react-navigation/stack';
import type { CareerStackParamListI } from '@/src/navigation/types';
import { screens } from './screen';

const CareerStack = createStackNavigator<CareerStackParamListI>();

const CareerStackNavigator = () => (
  <CareerStack.Navigator
    screenOptions={{
      headerShown: false,
      animation: 'slide_from_right',
      cardStyle: { backgroundColor: 'white' },
      cardOverlayEnabled: false,
      cardShadowEnabled: false,
    }}
    initialRouteName="Careers"
  >
    {screens.map(({ name, component }) => (
      <CareerStack.Screen key={name} name={name} component={component} />
    ))}
  </CareerStack.Navigator>
);

export default CareerStackNavigator;
