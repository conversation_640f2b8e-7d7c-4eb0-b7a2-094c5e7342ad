import { useState } from 'react';
import { Text, View, ScrollView, Pressable } from 'react-native';
import { createDrawerNavigator, DrawerItemList, useDrawerStatus } from '@react-navigation/drawer';
import { CommonActions } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import SwitchProfileModal from '@/src/components/SwitchProfile';
import { SwitchProfileTypeI } from '@/src/components/SwitchProfile/types';
import TextView from '@/src/components/TextView';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { EntityProfileI } from '@/src/redux/slices/entityprofile/types';
import { showToast } from '@/src/utilities/toast';
import ProfileStackNavigator from '@/src/navigation/stacks/ProfileStack';
import BottomTabNavigator from '@/src/navigation/tabs/TabNavigation';
import ScoreboardScreen from '@/src/screens/Scoreboard';
import AddItem from '@/src/assets/svgs/AddItem';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import Invite from '@/src/assets/svgs/Invite';
import { fetchEntityProfilesForUserAPI } from '@/src/networks/entityProfile/fetchEntityProfilesForUser';
import HomeStackNavigator from '../stacks/HomeStack';
import CreateStackNavigator from '../stacks/CreateStack';

const Drawer = createDrawerNavigator();

const CustomDrawerContent = (props: any) => {
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const isUserActive = currentUser.isActive;
  const userDesignation = currentUser?.designation?.name || 'Not specified';
  const userOrganisation = currentUser?.organisation?.name;
  const isDrawerOpen = useDrawerStatus() === 'open';
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [profiles, setProfiles] = useState<SwitchProfileTypeI[]>([]);

  const handleSwitchProfilePress = async () => {
    try {
      setIsModalVisible(true);
      const userProfile = {
        id: currentUser.profileId,
        name: currentUser.fullName,
        avatar: currentUser.avatar,
        type: 'USER',
        role: undefined,
        isVerified: undefined,
      };
      const entityProfiles = (await fetchEntityProfilesForUserAPI()).map((profile) => ({
        id: profile.id,
        name: profile.name,
        avatar: profile.avatar,
        type: 'ENTITY',
        role: profile.role,
        isVerified: profile.isVerified,
      }));
      setProfiles([userProfile, ...entityProfiles]);
      props.navigation.closeDrawer();
    } catch (e) {
      showToast({
        type: 'error',
        message: 'Cannot Switch Profiles Now. Try Again later',
      });
    }
  };

  const handleInviteFriendPress = () => {
    props.navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: { screen: 'ReferralDetails' },
    });
  };

  const handleProfilePress = () => {
    props.navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: 'ProfileStack',
            state: {
              routes: [
                {
                  name: 'UserProfile',
                  params: { profileId: undefined, fromTabPress: true },
                },
              ],
            },
          },
        ],
      }),
    );
  };

  const handleEntityProfilePress = () => {
    props.navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: {
        screen: 'EntityProfile',
        params: { entityProfileId: undefined },
      },
    });
  };

  const modalNavigation = props.navigation;

  const UserDrawer = () => {
    return (
      <>
        <View className="px-6 pt-10 items-start border-b border-gray-100">
          <Pressable android_ripple={null} onPress={handleProfilePress}>
            <UserAvatar
              avatarUri={currentUser?.avatar}
              name={currentUser?.fullName}
              width={80}
              height={80}
              className="border-4 border-green-800/20 rounded-full -ml-3"
            />
          </Pressable>
          <Text className="text-2xl font-extrabold mt-4 text-gray-900">
            {currentUser?.fullName || 'Current'}
          </Text>
          <Text
            className="text-sm text-gray-500 mt-1"
            numberOfLines={2}
          >{`${userDesignation} ${userOrganisation ? ` at ${userOrganisation}` : ``}`}</Text>
        </View>
        <View className="flex-1 mt-2">
          <DrawerItemList {...props} isUserActive={true} />
        </View>

        <LinearGradient
          colors={['#FFD400', '#FFD49F']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          className="mx-4 my-4 rounded-2xl shadow-xl overflow-hidden" // 👈 round + shadow here
        >
          <Pressable
            android_ripple={null}
            className="p-5 flex-row items-center gap-4"
            onPress={handleInviteFriendPress}
          >
            <View className="flex-row gap-2">
              <Invite />
              <View className="ml-2 flex-1">
                <Text className="text-xl font-bold text-gray-900 tracking-tight leading-5">
                  Invite a friend!
                </Text>
                <Text className="text-base text-gray-800 opacity-90 mt-1 leading-5">
                  Share Navicater, earn rewards
                </Text>
                <Text className="text-sm text-gray-700 opacity-80 mt-1 leading-4">
                  10 points per referral.
                </Text>
              </View>
            </View>
          </Pressable>
        </LinearGradient>
      </>
    );
  };

  const EntityDrawer = () => {
    return (
      <>
        <View className="px-6 pt-10 items-start border-b border-gray-100">
          <Pressable android_ripple={null} onPress={handleEntityProfilePress}>
            <UserAvatar
              avatarUri={currentEntity?.avatar}
              name={currentEntity?.name}
              width={80}
              height={80}
              className="border-4 border-green-800/20 rounded-full -ml-3"
            />
          </Pressable>
          <Text className="text-2xl font-extrabold mt-4 text-gray-900">
            {currentEntity?.name || 'Current Company'}
          </Text>
        </View>
        <View className="flex-1 mt-2">
          <DrawerItemList {...props} isUserActive={false} />
        </View>
      </>
    );
  };

  return (
    <SafeArea>
      <View className="flex-1 bg-white shadow-xl">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {isUserActive ? <UserDrawer /> : <EntityDrawer />}
        </ScrollView>

        <View className="border-t border-gray-200 bg-white">
          <Pressable
            android_ripple={null}
            onPress={handleSwitchProfilePress}
            className="flex-row items-center px-6 pb-12 pt-6 active:bg-gray-100 justify-between"
          >
            <Text className="text-base font-semibold text-gray-700 tracking-wide ml-2">
              Switch Profile
            </Text>
            <ChevronRight width={3.5} height={3.5} color="#000000" />
          </Pressable>
        </View>
      </View>
      <SwitchProfileModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        profiles={profiles}
        modalNavigation={modalNavigation}
      />
    </SafeArea>
  );
};

const DrawerNavigator = () => {
  const isUserActive = useSelector(selectCurrentUser).isActive;
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: '80%',
          backgroundColor: '#ffffff',
        },
        drawerLabelStyle: {
          fontSize: 17,
          color: '#1F2937',
          fontWeight: '600',
          marginLeft: -16,
        },
        drawerActiveBackgroundColor: 'transparent',
        drawerInactiveBackgroundColor: 'transparent',
        drawerItemStyle: {
          marginHorizontal: 16,
          marginVertical: 0,
        },
      }}
      drawerContent={(props) => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen
        name="MainTabs"
        component={BottomTabNavigator}
        options={{
          title: 'Home',
          drawerItemStyle: { display: 'none' },
        }}
      />
      {isUserActive ? (
        <>
          <Drawer.Screen
            name="ProfileStack"
            component={ProfileStackNavigator}
            options={{
              title: 'My Profile',
            }}
            initialParams={{
              screen: 'UserProfile',
              params: { profileId: undefined, fromTabPress: true },
            }}
          />
          <Drawer.Screen
            name="Settings"
            component={HomeStackNavigator}
            options={{
              title: 'Settings',
            }}
            initialParams={{
              screen: 'UserSettings',
            }}
          />
          <Drawer.Screen name="My Scoreboard" component={ScoreboardScreen} />

          <Drawer.Screen
            name="CreateStack"
            component={CreateStackNavigator}
            options={{
              drawerItemStyle: { display: 'none' },
            }}
          />
        </>
      ) : (
        <>
          <Drawer.Screen
            name="Profile"
            component={HomeStackNavigator}
            options={{
              title: 'Organization Profile',
            }}
            initialParams={{
              screen: 'EntityProfile',
              params: { entityProfileId: undefined },
            }}
          />
        </>
      )}
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
