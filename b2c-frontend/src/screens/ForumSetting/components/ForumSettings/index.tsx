import { useEffect, useState } from 'react';
import { Pressable, Text, View, ActivityIndicator } from 'react-native';
import { RouteProp, useIsFocused, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import UserAvatar from '@/src/components/UserAvatar';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import { fetchCommunityMembersAPI } from '@/src/networks/community/members';
import { ForumSettingOptionI } from './types';

const renderSettingOption = (option: ForumSettingOptionI) => (
  <Pressable
    key={option.id}
    onPress={option.onPress}
    className="flex-row items-center justify-between px-4 py-4 bg-white border border-borderGrayLight rounded-xl mb-6"
    android_ripple={{ color: '#F3F4F6' }}
  >
    <View className="flex-row items-center flex-1">
      <View className="flex-1">
        <Text className="text-sm">{option.title}</Text>
      </View>
    </View>
    {option.showChevron && <ChevronRight width={2.5} height={2.5} />}
  </Pressable>
);

type SimpleUser = { id: string; name: string; avatar: string | null };

const renderUserSection = (
  title: string,
  users: SimpleUser[],
  onPress: () => void,
  loading: boolean,
) => (
  <View className="bg-white rounded-lg mb-4">
    <Pressable onPress={onPress} className="p-4">
      <Text className="text-base font-normal leading-4 mb-2">{title}</Text>
      <View className="flex-row flex-wrap border border-borderGrayLight rounded-xl px-4 py-2 items-center min-h-12">
        {loading ? (
          <View className="w-full items-center py-2">
            <ActivityIndicator size="small" color="#448600" />
          </View>
        ) : users.length === 0 ? (
          <Text className="text-sm text-gray-500 py-3">None yet</Text>
        ) : (
          users.map((user) => (
            <View
              key={user.id}
              className={`flex-row items-center border border-borderGrayExtraLight p-2 rounded-full gap-2 mb-2 mr-2`}
            >
              <UserAvatar avatarUri={user.avatar} name={user.name} width={20} height={20} />
              <Text className="text-sm font-medium text-gray-700">{user.name}</Text>
            </View>
          ))
        )}
      </View>
    </Pressable>
  </View>
);

const ForumSettings = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'ForumSetting'>>();
  const { communityId } = route.params;
  const [admins, setAdmins] = useState<SimpleUser[]>([]);
  const [moderators, setModerators] = useState<SimpleUser[]>([]);
  const [loadingRoles, setLoadingRoles] = useState<boolean>(false);
  const isFocused = useIsFocused();

  const handleBack = () => {
    navigation.goBack();
  };

  const navigateToAdmins = () => {
    navigation.navigate('People', {
      type: 'add',
      title: 'Add admin',
      btnText: 'Add',
      communityId,
      role: 'ADMIN',
    });
  };

  const navigateToModerators = () => {
    navigation.navigate('People', {
      type: 'add',
      title: 'Add moderator',
      btnText: 'Add',
      communityId,
      role: 'MODERATOR',
    });
  };

  const navigateToMemberList = () => {
    navigation.navigate('CommunityMembers', { communityId });
  };

  useEffect(() => {
    const fetchAllByRole = async (type: 'ADMIN' | 'MODERATOR') => {
      let cursorId: number | null = null;
      const users: SimpleUser[] = [];
      do {
        const res = await fetchCommunityMembersAPI({ communityId, cursorId, pageSize: 10, type });
        users.push(
          ...res.data.map((d) => ({
            id: d.Profile.id,
            name: d.Profile.name,
            avatar: d.Profile.avatar,
          })),
        );
        cursorId = res.nextCursorId ?? null;
      } while (cursorId !== null);
      return users;
    };

    const run = async () => {
      setLoadingRoles(true);
      try {
        const [a, m] = await Promise.all([fetchAllByRole('ADMIN'), fetchAllByRole('MODERATOR')]);
        setAdmins(a);
        setModerators(m);
      } finally {
        setLoadingRoles(false);
      }
    };

    if (isFocused) {
      run();
    }
  }, [communityId, isFocused]);

  // const navigateToBlockedList = () => {
  //   navigation.navigate('CommunityBlocked', {});
  // };

  const navigateToUserRequests = () => {
    navigation.navigate('CommunityRequests', { communityId });
  };

  const settingOptions: ForumSettingOptionI[] = [
    {
      id: 'member-list',
      title: 'Member List',
      onPress: navigateToMemberList,
      showChevron: true,
    },
    // {
    //   id: 'blocked-users',
    //   title: 'Blocked users',
    //   onPress: navigateToBlockedList,
    //   showChevron: true,
    // },
    {
      id: 'user-requests',
      title: 'User requests',
      onPress: navigateToUserRequests,
      showChevron: true,
    },
  ];

  return (
    <View className="flex-1 bg-white">
      <View className="flex-row items-center justify-between px-4 py-3  border-b border-gray-100">
        <View className="flex-row items-center">
          <BackButton onBack={handleBack} label="" />
          <Text className="text-xl font-medium">Forum settings</Text>
        </View>
      </View>

      {renderUserSection('Admin', admins, navigateToAdmins, loadingRoles)}

      {renderUserSection('Moderator', moderators, navigateToModerators, loadingRoles)}

      <View className="mx-4">{settingOptions.map(renderSettingOption)}</View>
    </View>
  );
};

export default ForumSettings;
