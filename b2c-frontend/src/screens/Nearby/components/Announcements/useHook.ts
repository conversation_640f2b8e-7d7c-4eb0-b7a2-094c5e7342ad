import { useState, useEffect } from 'react';
import { Platform, PermissionsAndroid } from 'react-native';
import Config from 'react-native-config';
import Geolocation from 'react-native-geolocation-service';
import { useSelector, useDispatch } from 'react-redux';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setSelfLocation } from '@/src/redux/slices/announcement/announcementSlice';
import { AppDispatch } from '@/src/redux/store';
import { formatLongitude, formatLatitude } from '@/src/utilities/location/coordinates';
import { MapboxSuggestion } from '@/src/screens/NearbySettings/components/NearbySettings/types';
import { fetchAnnoucementsAPI } from '@/src/networks/nearby/announcement';
import { fetchAnnoucementI } from '@/src/networks/nearby/types';

export const useAnnouncements = () => {
  const [announcements, setAnnouncements] = useState<fetchAnnoucementI[]>([]);
  const filters = useSelector(selectNearbyFilters);
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const [loading, setLoading] = useState(false);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const isSelfLocationValid =
    filters.selfLocation?.coords?.latitude !== undefined &&
    filters.selfLocation?.coords?.longitude !== undefined &&
    !(filters.selfLocation.coords.latitude === 0 && filters.selfLocation.coords.longitude === 0);

  const isOtherLocationValid =
    filters.otherLocation?.coords?.latitude !== undefined &&
    filters.otherLocation?.coords?.longitude !== undefined &&
    !(filters.otherLocation.coords.latitude === 0 && filters.otherLocation.coords.longitude === 0);

  const isEntityLocationValid = !currentUser.isActive && isSelfLocationValid;

  const coordinates = (() => {
    const coords = [];

    if (isSelfLocationValid) {
      coords.push({
        longitude: formatLongitude(filters.selfLocation.coords.longitude),
        latitude: formatLatitude(filters.selfLocation.coords.latitude),
      });
    } else {
      coords.push({ longitude: 0, latitude: 0 });
    }

    if (isOtherLocationValid) {
      coords.push({
        longitude: formatLongitude(filters.otherLocation.coords.longitude),
        latitude: formatLatitude(filters.otherLocation.coords.latitude),
      });
    } else {
      coords.push({ longitude: 0, latitude: 0 });
    }

    return coords;
  })();

  const requestLocationPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          return true;
        }
        return false;
      } catch (err) {
        return false;
      }
    } else if (Platform.OS === 'ios') {
      const status = await Geolocation.requestAuthorization('whenInUse');
      return status === 'granted';
    }
    return true;
  };

  const getCurrentLocation = () => {
    return new Promise<void>((resolve) => {
      Geolocation.getCurrentPosition(
        async (position) => {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${position.coords.longitude},${position.coords.latitude}.json?access_token=${Config.MAPBOX_ACCESS_TOKEN}`,
          );
          const data = await response.json();
          const addressItem = data.features?.find((item: MapboxSuggestion) =>
            item.id.startsWith('address.'),
          );
          const placeName =
            addressItem?.place_name || data.features?.[0]?.place_name || 'Current Location';
          dispatch(
            setSelfLocation({
              coords: {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
              },
              name: placeName,
            }),
          );
          resolve();
        },
        (err) => {
          resolve();
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    });
  };

  const fetchAnnoucements = async (loadMore = false) => {
    try {
      if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }
      if (currentUser.isActive) {
        if (!isSelfLocationValid) {
          const hasPermission = await requestLocationPermission();
          if (hasPermission) {
            await getCurrentLocation();
          }
          return;
        }
      }

      const payload = {
        coordinates,
        radius: filters.radius,
        cursorId: loadMore ? cursorId : null,
        pageSize: 10,
      };

      const result = await fetchAnnoucementsAPI(payload);

      if (loadMore) {
        setAnnouncements((prev) => [...prev, ...result.data]);
      } else {
        setAnnouncements(result.data);
      }

      setCursorId(result.nextCursorId);
      setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchAnnoucements();
  }, [filters]);

  const loadMoreAnnouncements = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchAnnoucements(true);
  };

  const deleteAnnouncement = (announcementId: string) => {
    setAnnouncements((prev) => prev.filter((ann) => ann.announcementId !== announcementId));
  };

  const refetch = () => {
    fetchAnnoucements();
  };

  return {
    announcements,
    currentUser,
    loading,
    isLoadingMore,
    hasMore,
    isEntityLocationValid,
    currentEntity,
    loadMoreAnnouncements,
    fetchAnnoucements,
    deleteAnnouncement,
    refetch,
  };
};
