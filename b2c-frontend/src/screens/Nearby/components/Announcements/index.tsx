import { useEffect, useRef, useCallback } from 'react';
import {
  ActivityIndicator,
  Pressable,
  Text,
  View,
  Animated,
  Easing,
  RefreshControl,
} from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Announcement } from '@/src/components/Announcement';
import { EntityProfileStateI } from '@/src/redux/slices/entityprofile/types';
import { UserState } from '@/src/redux/slices/user/types';
import { navigate } from '@/src/utilities/navigation';
import AddItem from '@/src/assets/svgs/AddItem';
import { useAnnouncements } from './useHook';

export const Announcements = () => {
  const {
    currentUser,
    announcements,
    loading,
    isLoadingMore,
    isEntityLocationValid,
    currentEntity,
    fetchAnnoucements,
    loadMoreAnnouncements,
    deleteAnnouncement,
    refetch,
  } = useAnnouncements();

  const insets = useSafeAreaInsets();
  const bottomNavHeight = 60;
  const fabBottom = insets.bottom + bottomNavHeight;

  const scaleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 400,
      easing: Easing.out(Easing.exp),
      useNativeDriver: true,
    }).start();
  }, []);

  const onRefresh = useCallback(() => {
    fetchAnnoucements(false);
  }, []);

  const renderFooter = () => (
    <View className="pb-4">
      {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
    </View>
  );

  const renderEmptyComponent = () => (
    <View className="flex-1 justify-center items-center mt-20">
      <Text className="text-base text-gray-500">No events yet in your location</Text>
    </View>
  );

  if (!currentUser.isActive && !isEntityLocationValid) {
    return (
      <View className="flex-1 justify-center items-center bg-white px-6">
        <View className="rounded-2xl p-8 items-center shadow-lg border border-[#448600]">
          <View className="w-16 h-16 bg-green-100 rounded-full justify-center items-center mb-4">
            <Text className="text-2xl">📍</Text>
          </View>
          <Text className="text-lg font-semibold text-gray-900 text-center mb-2 text-[#448600]">
            Location Required
          </Text>
          <Text className="text-gray-600 text-center text-base">
            Set a location in the filter to see events near you
          </Text>
          <View className="w-12 h-1 bg-[#448600] rounded-full mt-4" />
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 px-4">
      {loading && announcements.length === 0 ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={announcements}
          keyExtractor={(item) => item.announcementId}
          renderItem={({ item }) => (
            <Announcement
              announcement={item}
              isUserActive={currentUser.isActive}
              user={item.profile}
              entity={item.entityProfile}
              currentUser={currentUser as Pick<UserState, 'profileId' | 'fullName' | 'avatar'>}
              currentEntity={
                currentEntity as Pick<EntityProfileStateI, 'entityProfileId' | 'name' | 'avatar'>
              }
              onDelete={deleteAnnouncement}
            />
          )}
          ListHeaderComponent={
            <View className="flex-row justify-between items-center py-2">
              <Text className="text-lg font-medium">{`All (${announcements.length})`}</Text>
            </View>
          }
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={loadMoreAnnouncements}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor="#000" />
          }
          contentContainerStyle={{
            paddingBottom: fabBottom + 20,
          }}
        />
      )}
      {/* <Animated.View
        style={{
          position: 'absolute',
          bottom: fabBottom,
          right: insets.right + 10,
          transform: [{ scale: scaleAnim }],
        }}
      >
        <Pressable
          style={{
            width: 56,
            height: 56,
            borderRadius: 28,
            backgroundColor: '#DDEFC8',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={() => navigate('EditAnnouncement', { refetch })}
        >
          <AddItem width={4} height={4} stroke="#DDEFC8" />
        </Pressable>
      </Animated.View> */}
    </View>
  );
};
