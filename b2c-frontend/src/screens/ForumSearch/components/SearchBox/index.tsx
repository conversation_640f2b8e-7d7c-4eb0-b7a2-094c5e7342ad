/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { TextInput, View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Tabs from '@/src/components/Tabs';
import { addForumSearch } from '@/src/redux/slices/forumsearch/forumSearchSlice';
import type { AppDispatch } from '@/src/redux/store';
import { forumGlobalSearchAPI } from '@/src/networks/forum/search';
import RecentSearches from '../RecentSearches';
import SearchResults from '../SearchResults';
import type { ForumSearchCategoryTabs } from '../SearchResults/types';
import type { ForumSearchCategory, ForumSearchResponse, ForumSearchType } from './types';

const tabs: ForumSearchCategoryTabs[] = [
  { id: 'posts', label: 'Questions' },
  { id: 'communities', label: 'Communities' },
];

const PAGE_SIZE = 10;

const Searchbox = (searchType: ForumSearchType = { searchType: 'general' }) => {
  const [searchData, setSearchData] = useState<string>('');
  const [showRecent, setShowRecent] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<ForumSearchCategory>('posts');
  const [searchResults, setSearchResults] = useState<
    Record<ForumSearchCategory, ForumSearchResponse>
  >({
    posts: { data: [], total: 0 },
    communities: { data: [], total: 0 },
  });
  const [lastSearchQuery, setLastSearchQuery] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<Record<ForumSearchCategory, number>>({
    posts: 0,
    communities: 0,
  });
  const [hasMore, setHasMore] = useState<Record<ForumSearchCategory, boolean>>({
    posts: true,
    communities: true,
  });
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();

  const debouncedSearch = async (
    searchText: string,
    category: ForumSearchCategory,
    isLoadMore = false,
  ) => {
    setLoading(true);
    const pageToFetch = isLoadMore ? currentPage[category] + 1 : 0;

    try {
      if (category === 'posts') {
        const response = await forumGlobalSearchAPI({
          search: searchText,
          page: pageToFetch,
          pageSize: PAGE_SIZE,
          type: 'questions',
        });

        const newData = {
          data: response.questions?.data || [],
          total: response.questions?.total || 0,
        };

        setSearchResults((prev) => ({
          posts: isLoadMore
            ? { data: [...(prev.posts.data as any[]), ...newData.data], total: newData.total }
            : newData,
          communities: prev.communities,
        }));

        setCurrentPage((prev) => ({ ...prev, posts: pageToFetch }));
        setHasMore((prev) => ({ ...prev, posts: (newData.data?.length || 0) === PAGE_SIZE }));
      } else {
        const response = await forumGlobalSearchAPI({
          search: searchText,
          page: pageToFetch,
          pageSize: PAGE_SIZE,
          type: 'communities',
        });

        const newData = {
          data: response.communities?.data || [],
          total: response.communities?.total || 0,
        };

        setSearchResults((prev) => ({
          posts: prev.posts,
          communities: isLoadMore
            ? { data: [...(prev.communities.data as any[]), ...newData.data], total: newData.total }
            : newData,
        }));

        setCurrentPage((prev) => ({ ...prev, communities: pageToFetch }));
        setHasMore((prev) => ({ ...prev, communities: (newData.data?.length || 0) === PAGE_SIZE }));
      }
    } catch (error) {
      console.error('Search error:', error);
      if (!isLoadMore) {
        setSearchResults((prev) => ({
          ...prev,
          [category]: { data: [], total: 0 },
        }));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    const trimmed = searchData.trim();
    if (!trimmed && activeTab !== 'communities') return;

    setLoading(true);
    setLastSearchQuery(trimmed);
    setShowRecent(false);

    if (trimmed) {
      dispatch(addForumSearch({ category: activeTab, searchText: trimmed }));
    }

    try {
      await debouncedSearch(trimmed, activeTab, false);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults({
        posts: { data: [], total: 0 },
        communities: { data: [], total: 0 },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = async (tab: ForumSearchCategory) => {
    setActiveTab(tab);
    if (lastSearchQuery.trim()) {
      setLoading(true);
      try {
        const response = await forumGlobalSearchAPI({
          search: lastSearchQuery,
          page: 0,
          pageSize: 20,
          type: tab === 'posts' ? 'questions' : 'communities',
        });

        setSearchResults((prev) => ({
          ...prev,
          [tab]: {
            data:
              tab === 'posts' ? response.questions?.data || [] : response.communities?.data || [],
            total:
              tab === 'posts' ? response.questions?.total || 0 : response.communities?.total || 0,
          },
        }));
      } catch (error) {
        console.error('Tab change search error:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSearchChange = (text: string) => {
    setSearchData(text);
    if (text.trim() === '') {
      setSearchResults({
        posts: { data: [], total: 0 },
        communities: { data: [], total: 0 },
      });
      setShowRecent(true);
      setLastSearchQuery('');
    }
  };

  const handleRefresh = async () => {
    if (lastSearchQuery.trim()) {
      setRefreshing(true);
      try {
        await debouncedSearch(lastSearchQuery, activeTab, false);
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
        setRefreshing(false);
      }
    }
  };

  const handleLoadMore = async () => {
    if (lastSearchQuery && hasMore[activeTab] && !loading) {
      await debouncedSearch(lastSearchQuery, activeTab, true);
    }
  };

  return (
    <View className="flex-1 bg-white">
      <View className="px-4">
        <View className="flex-row items-center space-x-3">
          <BackButton onBack={() => navigation.goBack()} label="" />
          <View className="flex-1 flex-row items-center bg-gray-100 rounded-xl px-3">
            <TextInput
              autoFocus
              placeholder="Search..."
              placeholderTextColor="#6b7280"
              value={searchData}
              onChangeText={handleSearchChange}
              onSubmitEditing={handleSubmit}
              returnKeyType="search"
              className="flex-1 text-black py-3"
            />

            {searchData.length > 0 && (
              <Pressable
                onPress={() => {
                  setSearchData('');
                  setSearchResults({
                    posts: { data: [], total: 0 },
                    communities: { data: [], total: 0 },
                  });
                  setShowRecent(true);
                  setLastSearchQuery('');
                }}
                className="p-1"
              >
                <Text className="text-gray-500">✕</Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
      <View className="flex-1">
        {showRecent ? (
          <RecentSearches
            setSearchData={setSearchData}
            setActiveTab={setActiveTab}
            setLoading={setLoading}
            setShowRecent={setShowRecent}
            setLastSearchQuery={setLastSearchQuery}
            debouncedSearch={debouncedSearch}
            category={activeTab}
          />
        ) : (
          <>
            {searchType.searchType === 'general' && (
              <View className="pt-4">
                <Tabs
                  tabs={tabs}
                  activeTab={activeTab}
                  onTabChange={(tab) => {
                    handleTabChange(tab as ForumSearchCategory);
                  }}
                  disabled={loading}
                />
              </View>
            )}
            <SearchResults
              activeTab={activeTab}
              searchResults={searchResults[activeTab]}
              loading={loading}
              refreshing={refreshing}
              onRefresh={handleRefresh}
              searchType={searchType.searchType}
              searchText={lastSearchQuery}
              onLoadMore={handleLoadMore}
              hasMore={hasMore[activeTab]}
            />
          </>
        )}
      </View>
    </View>
  );
};

export default Searchbox;
