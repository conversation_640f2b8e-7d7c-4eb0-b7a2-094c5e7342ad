/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useCallback, useState } from 'react';
import { type RouteProp, useFocusEffect, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setAboutData } from '@/src/redux/slices/about/aboutSlice';
import { fetchAndSaveUserProfile } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import APIResError from '@/src/errors/networks/APIResError';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import type { FetchProfileResultI } from '@/src/networks/profile/types';
import { fetchAboutProfileAPI } from '@/src/networks/profile/userAbout';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import ProfileContent from './components/ProfileContent';
import type { AboutProfileI } from './components/ProfileContent/types';
import ProfileSkeleton from './components/ProfileSkeleton';

const UserProfile = () => {
  const [profileData, setProfileData] = useState<FetchProfileResultI | null>(null);
  const [aboutProfile, setAboutProfile] = useState<AboutProfileI | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);

  const currentUser = useSelector(selectCurrentUser);
  const route = useRoute<RouteProp<ProfileStackParamsListI, 'UserProfile'>>();
  const dispatch = useDispatch<AppDispatch>();

  const fromTabPress = route.params?.fromTabPress;
  const profileId = fromTabPress ? currentUser?.profileId : route.params?.profileId;
  const isCurrentUserProfile = currentUser?.profileId === profileId;

  const currentCompany = useSelector(selectCurrentEntityProfile);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const fetchAllData = async () => {
    if (!profileId) return;

    setLoading(true);
    setProfileData(null);
    setAboutProfile(null);
    setError(null);

    try {
      const [profileResult, aboutResult] = await Promise.all([
        fetchProfileAPI(profileId),
        fetchAboutProfileAPI(profileId),
      ]);

      setProfileData(profileResult);
      setAboutProfile(aboutResult);
      dispatch(setAboutData(aboutResult));

      if (isCurrentUserProfile) {
        dispatch(fetchAndSaveUserProfile({ id: profileId }));
      }
    } catch (err) {
      const errorMessage = `Failed to fetch profile data: ${err instanceof APIResError ? err.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchAllData();
    }, []),
  );

  return (
    <SafeArea>
      {loading || profileData === null || aboutProfile === null ? (
        <ProfileSkeleton />
      ) : (
        <ProfileContent
          data={profileData}
          aboutProfile={aboutProfile}
          isUserProfile={isCurrentUserProfile}
        />
      )}
    </SafeArea>
  );
};

export default UserProfile;
