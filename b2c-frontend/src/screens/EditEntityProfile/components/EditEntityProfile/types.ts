import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, IdTypeI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export type EditEntityProfileFormDataI = {
  description: string;
  overview: string;
  website: string;
  foundedAt: string;
};

export type UseEditEntityProfileI = {
  methods: UseFormReturn<EditEntityProfileFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: EditEntityProfileFormDataI) => Promise<void>;
  loading: boolean;
  isSubmitted: boolean;
  setIsSubmitted: Dispatch<SetStateAction<boolean>>;
  hasChanges: boolean;
};

export type EditEntityProfilePropsI = {
  onBack: () => void;
};
