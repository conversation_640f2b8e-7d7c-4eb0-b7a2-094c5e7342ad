import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import {
  fetchEntityProfileBasicDetailsAPI,
  updateEntityProfileBasicDetailsAPI,
} from '@/src/networks/entityProfile/fetchEntityProfileDetails';
import type { EditEntityProfileFormDataI, UseEditEntityProfileI } from './types';

export const useEditEntityProfile = (): UseEditEntityProfileI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const entityProfileId = useSelector(selectCurrentEntityProfile).entityProfileId;

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const methods = useForm<EditEntityProfileFormDataI>({
    mode: 'onChange',
    defaultValues: {
      description: '',
      overview: '',
      foundedAt: '',
      website: '',
    },
  });

  const hasChanges = methods.formState.isDirty;

  useEffect(() => {
    if (entityProfileId) {
      const fetchEntityProfile = async () => {
        try {
          setLoading(true);
          const response = await fetchEntityProfileBasicDetailsAPI({
            entityProfileId,
          });
          const fetchedEntityProfile: EditEntityProfileFormDataI = {
            description: response.description,
            overview: response.overview,
            foundedAt: response.foundedAt,
            website: response.website,
          };
          methods.reset(fetchedEntityProfile);
        } catch (err) {
          triggerErrorBoundary(
            new Error(
              'Failed to load organisation details: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchEntityProfile();
    }
  }, [entityProfileId, methods]);

  const onSubmit = async (data: EditEntityProfileFormDataI) => {
    try {
      setIsSubmitting(true);
      const { website, ...rest } = data;
      await updateEntityProfileBasicDetailsAPI(
        { entityProfileId },
        {
          ...rest,
          website: normalizeWebsiteUrl(website),
        },
      );
      showToast({
        message: 'Success',
        description: 'Organisation Profile Updated',
        type: 'success',
      });
      methods.reset(methods.getValues());
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Edit Profile',
            description: 'Unable to save details',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    loading,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
  };
};

export const normalizeWebsiteUrl = (url: string): string => {
  if (url.startsWith('http')) {
    return url;
  }
  return `https://${url}`;
};
