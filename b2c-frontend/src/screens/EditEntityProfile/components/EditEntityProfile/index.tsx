import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Platform, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import CharacterCounter from '@/src/components/CharacterCounter';
import DatePicker from '@/src/components/DatePicker';
import CustomModal from '@/src/components/Modal';
import TextInput from '@/src/components/TextInput';
import type { EditEntityProfilePropsI } from './types';
import { useEditEntityProfile } from './useHook';

const EditEntityProfile = ({ onBack }: EditEntityProfilePropsI) => {
  const { methods, isSubmitting, onSubmit, loading, isSubmitted, setIsSubmitted, hasChanges } =
    useEditEntityProfile();

  const { control, handleSubmit, watch } = methods;
  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  const foundedAtDate = watch('foundedAt');

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const insets = useSafeAreaInsets();

  return (
    <ScrollView
      className={`bg-white ${Platform.OS === 'android' ? `mb-${insets.bottom}` : ''}`}
      showsVerticalScrollIndicator={false}
    >
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit Profile" />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting || !hasChanges}
          >
            <Text
              className={`text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <Controller
          control={control}
          name="description"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <>
              <TextInput
                label="Description"
                value={value}
                onChangeText={onChange}
                placeholder="Enter description"
                error={error?.message}
                className="py-3"
                type="textarea"
              />
              <CharacterCounter currentLength={value?.length} maxLength={500} />
            </>
          )}
        />
        <Controller
          control={control}
          name="overview"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <>
              <TextInput
                label="Overview"
                value={value}
                onChangeText={onChange}
                placeholder="Enter overview"
                error={error?.message}
                className="py-3"
                type="textarea"
              />
              <CharacterCounter currentLength={value?.length} maxLength={1000} />
            </>
          )}
        />
        <Controller
          control={control}
          name="website"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Website"
              value={value}
              onChangeText={onChange}
              placeholder="Enter website link"
              error={error?.message}
              className="py-3"
              autoCapitalize="none"
            />
          )}
        />
        <Controller
          control={control}
          name="foundedAt"
          render={({ field: { onChange }, fieldState: { error } }) => (
            <>
              <DatePicker
                title="From"
                selectedDate={foundedAtDate}
                onDateChange={(date) => {
                  if (date instanceof Date) {
                    onChange(date.toISOString().split('T')[0]);
                  }
                }}
                showMonthYear={true}
                className={error ? 'border-red-500' : ''}
              />
              {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
            </>
          )}
        />
      </View>
      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        cancelText="Cancel"
        confirmText="Discard"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={handleDiscardCancel}
        onConfirm={handleDiscardConfirm}
        confirmButtonVariant="danger"
      />
    </ScrollView>
  );
};

export default EditEntityProfile;
