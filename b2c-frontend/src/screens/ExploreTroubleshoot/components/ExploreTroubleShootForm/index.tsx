import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const ExploreTroubleShootForm = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch();

  const onBack = () => {
    navigation.goBack();
  };

  const exploreEquipmentCategory = useSelector(selectSelectionByKey('exploreEquipmentCategory'));
  const exploreEquipmentManufacturer = useSelector(
    selectSelectionByKey('exploreEquipmentManufacturer'),
  );
  const exploreEquipmentModel = useSelector(selectSelectionByKey('exploreEquipmentModel'));

  const clearSelections = () => {
    dispatch(clearSelection('exploreEquipmentCategory'));
    dispatch(clearSelection('exploreEquipmentManufacturer'));
    dispatch(clearSelection('exploreEquipmentModel'));
  };

  const handleNext = () => {
    navigation.navigate('ExploreForum', {
      type: 'TROUBLESHOOT',
      fromExploreTroubleshoot: true,
      equipmentCategory: exploreEquipmentCategory,
      equipmentManufacturer: exploreEquipmentManufacturer,
      equipmentModel: exploreEquipmentModel,
    });

    clearSelections();
  };

  return (
    <View className="flex-1 bg-white p-3">
      <View className="flex-row justify-between items-center">
        <BackButton
          label="Explore troubleshooting"
          labelClassname="font-medium text-lg leading-6"
          onBack={onBack}
        />
        <View className="flex-row items-center gap-2">
          <Pressable onPress={clearSelections}>
            <Text className="text-lg font-semibold px-2">Clear</Text>
          </Pressable>
          <Button
            label="Next"
            onPress={handleNext}
            variant={'primary'}
            className="rounded-full bg-primaryGreen w-auto"
          ></Button>
        </View>
      </View>
      <EntitySearch
        title={'Equipment'}
        placeholder={`Enter equipment`}
        selectionKey="exploreEquipmentCategory"
        searchWithoutInput
        data={exploreEquipmentCategory ? exploreEquipmentCategory.name : ''}
      />
      <EntitySearch
        title={'Make'}
        placeholder={`Enter make`}
        selectionKey="exploreEquipmentManufacturer"
        searchWithoutInput
        data={exploreEquipmentManufacturer ? exploreEquipmentManufacturer.name : ''}
      />
      <EntitySearch
        title={'Model'}
        placeholder={`Enter model`}
        selectionKey="exploreEquipmentModel"
        searchWithoutInput
        data={exploreEquipmentModel ? exploreEquipmentModel.name : ''}
      />
    </View>
  );
};

export default ExploreTroubleShootForm;
