import React from 'react';
import { View, FlatList, ActivityIndicator } from 'react-native';
import NotFound from '@/src/components/NotFound';
import ForumPost from '@/src/screens/Forum/components/ForumPost';
import { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';
import ForumPostSkelton from '@/src/screens/Forum/components/ForumPostSkelton';
import CommunityHomeHead from '../CommunityHomeHead';
import { CommunityHomePropsI } from './types';
import useCommunityHome from './useHook';

const CommunityHome: React.FC<CommunityHomePropsI> = ({ id }) => {
  const {
    handleSelectPost,
    community,
    posts,
    handleAddMembersNavigate,
    handleDeleteCommunity,
    handleLoadMore,
    hasMore,
    loading,
    loadingMore,
    error,
    connectionState,
    handleConnect,
    handleRevoke,
    handleLeave,
    isFromSearch,
  } = useCommunityHome();
  const renderForumPost = ({ item }: { item: ForumPostProps }) => (
    <ForumPost
      post={{
        ...item,
        onPress: () => handleSelectPost(item),
        userCommunityRole: community?.role ?? null,
      }}
    />
  );

  return (
    <View className="flex-1">
      <CommunityHomeHead
        communityId={id}
        communityName={community?.name ?? 'Community Name'}
        memberCount={community?.memberCount ?? 0}
        avatarUri={community?.avatar ?? undefined}
        onAddMembers={handleAddMembersNavigate}
        onDeleteCommunity={handleDeleteCommunity}
        role={community?.role ?? null}
        access={community?.access}
        isRestricted={community?.isRestricted}
        initialConnectionState={connectionState}
        onConnect={handleConnect}
        onRevokeRequest={handleRevoke}
        onLeave={handleLeave}
        isFromSearch={isFromSearch}
      />
      {loading ? (
        <View className="flex-1">
          {Array.from({ length: 3 }).map((_, index) => (
            <ForumPostSkelton key={index} />
          ))}
        </View>
      ) : error ? (
        <View className="flex-1 items-center justify-center py-4">
          <NotFound title="Failed to load" subtitle={error} />
        </View>
      ) : posts.length === 0 ? (
        <View className="flex-1 items-center justify-center py-4">
          <NotFound
            title="No questions yet"
            subtitle="Questions will appear here when users post them."
          />
        </View>
      ) : (
        <View className="mb-48 mt-4 px-2">
          <FlatList
            data={posts}
            renderItem={renderForumPost}
            keyExtractor={(item) => item.postId}
            showsVerticalScrollIndicator={false}
            onEndReachedThreshold={0.7}
            onEndReached={handleLoadMore}
            ListFooterComponent={
              loadingMore ? (
                <View className="py-4 items-center">
                  <ActivityIndicator size="small" color="#448600" />
                </View>
              ) : null
            }
            contentContainerStyle={{
              paddingBottom: 20,
              marginBottom: 30,
            }}
          />
        </View>
      )}
    </View>
  );
};

export default CommunityHome;
