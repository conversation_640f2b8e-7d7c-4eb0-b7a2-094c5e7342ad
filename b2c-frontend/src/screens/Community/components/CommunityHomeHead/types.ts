export type RoleType = 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';

export type CommunityHomeHeadPropsI = {
  communityId: string;
  communityName: string;
  memberCount: number;
  avatarUri?: string;
  access?: 'PUBLIC' | 'PRIVATE' | string;
  isRestricted?: boolean;
  onConnect?: (role?: RoleType) => Promise<void> | void;
  onRevokeRequest?: () => Promise<void> | void;
  onDeleteCommunity?: () => Promise<void> | void;
  onAddMembers?: () => void;
  initialConnectionState?: 'connected' | 'requested' | 'disconnected' | null;
  role: CommunityRoleI | null;
  isFromSearch?: boolean;
  onLeave?: () => Promise<void> | void;
};

export type CommunityRoleI = RoleType | null;
