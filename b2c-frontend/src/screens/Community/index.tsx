import { RouteProp, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CommunityHome from './components/CommunityHome';
import FloatingActionButton from '@/src/components/FloatingActionButton';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';


const CommunityScreen = () => {
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'Community'>>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const params = route.params || {};
  const id = params.id;

  const handleCreateQuestion = () => {
    navigation.navigate('CommunityQuestion', {id:'', communityId: id});
  }


  return (
    <SafeArea>
      <CommunityHome id={id} />
      <FloatingActionButton 
        context='CREATE_COMMUNITY_QUESTION'
        onPress={handleCreateQuestion} 
        visible={true}
      />
    </SafeArea>
  );
};

export default CommunityScreen;
