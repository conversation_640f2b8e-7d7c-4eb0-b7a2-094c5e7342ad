/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ListItem } from '@/src/components/UsersList/types';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

export type UserRequestsNavigationI = StackNavigationProp<LearnCollabStackParamsListI>;
export type UserRequestsRouteI = RouteProp<LearnCollabStackParamsListI, 'CommunityRequests'>;

export type UserRequestsPropsI = {
  communityId: string;
};

export type UseUserRequestsPropsI = {
  communityId: string;
  pageSize?: number;
};

export type UserRequestsHookResultI = {
  data: ListItem[];
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  error: Error | null;
  loadMore: () => void;
  refresh: () => void;
  handleRequest: (
    item: ListItem,
    status: 'ACCEPTED' | 'REJECTED',
    role?: 'member' | 'contributor' | 'moderator' | 'admin',
  ) => Promise<void>;
  requestStatus: Record<string, 'ACCEPTED' | 'REJECTED' | undefined>;
};

export type ForumJoinRequestI = {
  status: 'PENDING' | 'ACCEPTED' | 'PARTIALLY_ACCEPTED' | 'REJECTED' | 'REVOKED';
  requestedType: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designation: {
      id: string;
      name: string;
    } | null;
    entity: {
      id: string;
      name: string;
    } | null;
  };
};

export type ItemsI = ListItem & {
  requestedRole?: 'member' | 'contributor' | 'moderator' | 'admin';
};
