/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import {
  approveCommunityRequestAPI,
  fetchCommunityRequestsAPI,
  rejectCommunityRequestAPI,
} from '@/src/networks/community/request';
import { UseUserRequestsPropsI, UserRequestsHookResultI, ForumJoinRequestI } from './types';

export const useUserRequests = ({
  communityId,
  pageSize = 20,
}: UseUserRequestsPropsI): UserRequestsHookResultI => {
  const [data, setData] = useState<ListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState<number>(1);
  const [requestStatus, setRequestStatus] = useState<
    Record<string, 'ACCEPTED' | 'REJECTED' | undefined>
  >({});

  const transformRequestsToListItems = (requests: ForumJoinRequestI[]): ListItem[] => {
    const roleMap = {
      ADMIN: 'admin',
      MODERATOR: 'moderator',
      CONTRIBUTOR: 'contributor',
      MEMBER: 'member',
    } as const;
    return requests.map((request) => ({
      Profile: request.Profile,
      status: request.status,
      requestedRole: roleMap[request.requestedType],
    })) as ListItem[];
  };

  const fetchUserRequests = useCallback(
    async (pageNum: number, isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        const result = await fetchCommunityRequestsAPI({
          communityId,
          page: pageNum - 1,
          pageSize,
        });
        const transformedData = transformRequestsToListItems(
          result.data as unknown as ForumJoinRequestI[],
        );

        if (isRefresh || pageNum === 1) {
          setData(transformedData);
        } else {
          setData((prev) => [...prev, ...transformedData]);
        }

        setHasMore((pageNum - 1) * pageSize + transformedData.length < result.total);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [pageSize, communityId],
  );

  const handleRequest = useCallback(
    async (
      item: ListItem,
      status: 'ACCEPTED' | 'REJECTED',
      role?: 'member' | 'contributor' | 'moderator' | 'admin',
    ) => {
      const profileId = item.Profile?.id;
      if (!profileId) return;

      // Optimistic update
      setRequestStatus((prev) => ({ ...prev, [profileId]: status }));
      if (status === 'REJECTED') {
        // For rejected requests, remove from list immediately
        setData((prev) => prev.filter((dataItem) => dataItem.Profile.id !== profileId));
      }

      try {
        if (status === 'ACCEPTED') {
          const roleMap: Record<string, 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER'> = {
            admin: 'ADMIN',
            moderator: 'MODERATOR',
            contributor: 'CONTRIBUTOR',
            member: 'MEMBER',
          };
          const acceptedType = roleMap[(role || 'member').toLowerCase()];
          await approveCommunityRequestAPI({ communityId, profileId }, { acceptedType });
        } else {
          await rejectCommunityRequestAPI({ communityId, profileId });
        }
      } catch (err) {
        // Revert optimistic update on failure
        setRequestStatus((prev) => {
          const copy = { ...prev };
          delete copy[profileId];
          return copy;
        });
        if (status === 'REJECTED') {
          // Only re-add if we removed due to rejection
          setData((prev) => [item, ...prev]);
        }
        setError(err as Error);
      }
    },
    [communityId],
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchUserRequests(nextPage);
    }
  }, [loading, hasMore, page, fetchUserRequests]);

  const refresh = useCallback(() => {
    setPage(1);
    setRequestStatus({});
    fetchUserRequests(1, true);
  }, [fetchUserRequests]);

  useEffect(() => {
    fetchUserRequests(1);
  }, [fetchUserRequests]);

  return {
    data,
    loading,
    refreshing,
    hasMore,
    error,
    loadMore,
    refresh,
    handleRequest,
    requestStatus,
  };
};
