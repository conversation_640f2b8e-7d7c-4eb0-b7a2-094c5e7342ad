/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect, useCallback } from 'react';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { fetchLeaderboardAPI } from '@/src/networks/leaderboard/scores';
import {
  LeaderBoardItemI,
  LeaderBoardAPIResponseI,
  UseLeaderboardParams,
  UseLeaderboardReturn,
} from './types';

const transformAPIResponse = (apiData: LeaderBoardAPIResponseI[]): LeaderBoardItemI[] => {
  return apiData.map((item) => ({
    Profile: {
      id: item.Profile.id,
      name: item.Profile.name ?? 'Anonymous User',
      avatar: item.Profile.avatar,
      designation: item.Profile.designation,
      entity: item.Profile.entity,
    },
    EntityProfile: null,
    score: item.score,
    rank: item.rank,
  }));
};

const useLeaderboard = ({ duration, type }: UseLeaderboardParams): UseLeaderboardReturn => {
  const [data, setData] = useState<LeaderBoardItemI[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const fetchData = useCallback(
    async (pageNum: number = 1, isRefresh: boolean = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }
        setError(null);

        const response = await fetchLeaderboardAPI({
          duration,
          type,
          page: pageNum,
          pageSize: 10,
        });

        const transformedData = transformAPIResponse(response);

        if (isRefresh || pageNum === 1) {
          setData(transformedData);
          setPage(1);
        } else {
          setData((prev) => [...prev, ...transformedData]);
        }

        setHasMore(transformedData.length === 10);
      } catch (err) {
        const errorMessage =
          err instanceof APIResError ? err.message : 'Failed to load leaderboard data';
        setError(errorMessage);
        if (!data.length) {
          showToast({
            message: 'Failed to load leaderboard',
            type: 'error',
          });
        }
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [duration, type, data.length],
  );

  const refresh = useCallback(() => {
    setPage(0);
    setHasMore(true);
    fetchData(1, true);
  }, [fetchData]);

  const loadMore = useCallback(() => {
    if (!loading && !refreshing && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchData(nextPage, false);
    }
  }, [loading, refreshing, hasMore, page, fetchData]);

  useEffect(() => {
    setData([]);
    setPage(0);
    setHasMore(true);
    setError(null);
    fetchData(0, false);
  }, [duration, type]);

  return {
    data,
    loading,
    refreshing,
    error,
    refresh,
    loadMore,
    hasMore,
  };
};

export default useLeaderboard;
