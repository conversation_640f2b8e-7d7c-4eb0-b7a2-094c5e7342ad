import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import JobPosts from './components/JobPosts';

const JobPostsScreen = () => {
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <BackButton label="Job Posts" onBack={navigation.goBack} />
      <JobPosts
        isUser={currentUser.isActive}
        profileId={currentUser.profileId}
        entityProfileId={currentEntity.entityProfileId}
      />
    </SafeArea>
  );
};

export default JobPostsScreen;
