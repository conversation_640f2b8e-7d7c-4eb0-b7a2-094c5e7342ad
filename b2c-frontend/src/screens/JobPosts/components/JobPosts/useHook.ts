import { useEffect, useState } from 'react';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { useDispatch, useSelector } from 'react-redux';
import { selectActiveFilterNumbers } from '@/src/redux/selectors/careers';
import { fetchFiltersForEntityMembersAPI } from '@/src/networks/jobs/fetchFiltersForEntityMembers';
import { setFilters } from '@/src/redux/slices/career/careerSlice';
import { fetchJobsForEntityMemberAPI } from '@/src/networks/jobs/fetchJobsForEntityMember';

export const useJobPosts = (
  isUser: boolean,
  profileId: string,
  entityProfileId: string,
  type: string,
) => {
    const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const activeFilterNumbers = useSelector(selectActiveFilterNumbers('jobPosts'));
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
  const [jobPosts, setJobPosts] = useState([
    {
      id: '1',
      title: 'Software Engineer',
      entityProfile: {
        id: '1',
        name: 'Navicater',
        avatar: null,
      },
      createdAt: '2025-08-15T10:30:00Z',
    },
    {
      id: '2',
      title: 'Product Manager',
      entityProfile: {
        id: '2',
        name: 'TechCorp Inc',
        avatar: 'https://via.placeholder.com/200x200/cccccc/666666?text=Company',
      },
      createdAt: '2024-01-14T14:20:00Z',
    },
    {
      id: '3',
      title: 'UX Designer',
      entityProfile: {
        id: '3',
        name: 'DesignStudio asnldiugapldkn;qlkdnwdbpwkd paodpaokdb;wlkdew pgupdidbw;lkdn;woidgpoi',
        avatar: null,
      },
      createdAt: '2024-01-13T09:15:00Z',
    },
    {
      id: '4',
      title: 'Data Analyst',
      entityProfile: {
        id: '4',
        name: 'DataFlow Systems',
        avatar: 'https://example.com/avatar/dataflow.jpg',
      },
      createdAt: '2025-08-25T16:45:00Z',
    },
    {
      id: '5',
      title: 'DevOps Engineer',
      entityProfile: {
        id: '5',
        name: 'CloudSolutions Ltd',
        avatar: null,
      },
      createdAt: '2024-01-11T11:00:00Z',
    },
  ]);

  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0
    },
    {
      id: 'ship-type',
      label: 'Ship Type',
      appliedCount: activeFilterNumbers.shipTypes || 0
    },
  ];

  const fetchJobPosts = async (loadMore = false) => {
    try {
      if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        isOfficial: false,
        entity: null,
        status: transformStatus(type),
        cursorId: loadMore ? cursorId : null,
        pageSize: 10,
      };

      const result = await fetchJobsForEntityMemberAPI(query)

      if (loadMore) {
        // setJobPosts((prev) => [...prev, ...result]);
      } else {
        // setJobPosts(result);
      }

      //   setCursorId(result.nextCursorId);
      //   setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchJobPosts();
  }, [type]);

  const loadMoreJobPosts = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchJobPosts(true);
  };

  const onFilterPress = async() => {
          const filters = await fetchFiltersForEntityMembersAPI();
          dispatch(setFilters({
              page:'jobPosts',
              filters:filters
          }))
      }

  return {
    searchText,
    jobPosts,
    filterTabs,
    loading,
    isLoadingMore,
    setSearchText,
    loadMoreJobPosts,
    fetchJobPosts,
    onFilterPress
  };
};

const transformStatus = (type: string) => {
  const status = {
    active: 'ACTIVE',
    closed: 'DELETED',
    expired: 'EXPIRED',
  };

  return status[type as keyof typeof status];
};
