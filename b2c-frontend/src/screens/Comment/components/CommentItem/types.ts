/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { CommentItemI } from '@/src/networks/content/types';

export type CommentTypeI = CommentItemI;

export interface CommentItemProps {
  item: CommentItemI;
  postId: string;
  type: 'USER_POST' | 'SCRAPBOOK_POST';
  onReplyPress?: (item: CommentItemI) => void;
  cursorId?: number;
  level: number;
}

export type ReplyI = {
  id: string;
  Profile?: { id?: string; avatar?: string | null; name: string };
  EntityProfile?: { id?: string; avatar?: string | null; name: string };
  createdAt?: string;
  text?: string;
  cursorId?: number | null;
};
