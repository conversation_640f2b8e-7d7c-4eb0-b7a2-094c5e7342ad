/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { SetStateAction } from 'react';
import { UserI } from '@/src/redux/slices/content/types';
import { VoidFnI } from '@/src/types/common/function';
import { EntityProfileBaseI } from '@/src/networks/entityProfile/types';
import { CommentTypeI } from '../CommentItem/types';

export interface CommentInputProps {
  user: UserI;
  entityProfile?: EntityProfileBaseI;
  postId: string;
  type: 'USER_POST' | 'SCRAPBOOK_POST';
  portUnLocode?: string;
  parentCommentId?: string;
  replyPreview?: CommentTypeI | null;
  setReplyPreview: React.Dispatch<SetStateAction<CommentTypeI | null>>;
  onCommentSuccess: VoidFnI;
}
