import BackButton from '@/src/components/BackButton'
import SafeArea from '@/src/components/SafeArea'
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native'
import React from 'react'
import { View } from 'react-native'
import JobHead from './components/JobHead'
import { ScrollView } from 'react-native-gesture-handler'
import { CareerStackParamListI, RootStackParamListI } from '@/src/navigation/types'

const CareerDetailedScreen = () => {
    const navigation = useNavigation()
    const route = useRoute<RouteProp<CareerStackParamListI, "CareerDetails">>()
    const jobId = route.params.jobPostId 
    const handleBack = () => {
        navigation.goBack()
    }
    return (
        <SafeArea>
            <View>
                <BackButton onBack={handleBack} label="Careers" labelClassname='text-xl font-bold' />
                <ScrollView>
                    <JobHead   jobId={jobId} />
                </ScrollView>
            </View>
        </SafeArea>
    )
}

export default CareerDetailedScreen