import { updateJobApplication } from "@/src/networks/company/job/application"
import { showToast } from "@/src/utilities/toast"
import { useState } from "react"

const useJobActions = ({ jobId }: { jobId?: string }) => {
  const [applicationId, setApplicationId] = useState<string | undefined>(undefined)
  const [isApplied, setIsApplied] = useState(false)
  const [withdrawing, setWithdrawing] = useState(false)

  const canActOnJob = !!jobId
  

  const handleApply = async () => {
    if (!canActOnJob) {
      showToast({ message: 'Missing job information', type: 'error' })
      return
    }
    try {
      const result = await updateJobApplication({ jobId: jobId!, status: 'PENDING' })
      setApplicationId(result.id)
      setIsApplied(true)
      showToast({ message: 'Applied successfully', type: 'success' })
    } catch (error) {
      showToast({ message: 'Failed to apply', type: 'error' })
    }
  }

  const handleWithdraw = async () => {
    if (!canActOnJob || !applicationId) {
      showToast({ message: 'No application to withdraw', type: 'error' })
      return
    }
    try {
      setWithdrawing(true)
      await updateJobApplication({ jobId: jobId!, status: 'WITHDREW', applicationId })
      setIsApplied(false)
      showToast({ message: 'Application withdrawn', type: 'success' })
    } catch (_e) {
      showToast({ message: 'Failed to withdraw', type: 'error' })
    } finally {
      setWithdrawing(false)
    }
  }

  return {
    handleApply,
    handleWithdraw,
    isApplied,
    setIsApplied,
    withdrawing,
    applicationId,
  }
}

export default useJobActions;