import React, { useEffect, useMemo, useState } from 'react'
import { Pressable, Text, View } from 'react-native'
import { useSelector } from 'react-redux'
import Match from '@/src/assets/svgs/Match'
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis'
import UserAvatar from '@/src/components/UserAvatar'
import Button from '@/src/components/Button'
import BottomSheet from '@/src/components/Bottomsheet'
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu'
import CustomModal from '@/src/components/Modal'
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile'
import { JobHeadPropsI } from './types'
import useJobActions from './useHook'
import { fetchJobForCandidate } from '@/src/networks/company/job/job'
import { closeJob } from '@/src/networks/company/job/job'

import { showToast } from '@/src/utilities/toast'
import { formatElapsedTime } from '@/src/utilities/datetime'
import { useNavigation } from '@react-navigation/native'
import { StackNavigationProp } from '@react-navigation/stack'
import { CareerStackParamListI } from '@/src/navigation/types'

const JobHead: React.FC<JobHeadPropsI> = ({
    jobId,
    title = 'Job Title',
    companyName = 'Company name',
    locationText = 'Mumbai, Maharashtra',
    postedAgoText = '1 week ago',
    matchPercent = 36,
    aboutText = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque scelerisque diam eget fringilla tempor. Sed facilisis porttitor purus venenatis ultrices. Donec nunc quam, vestibulum eget faucibus sit amet, ',
    roles = [
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        'Pellentesque scelerisque diam eget fringilla tempor',
        'Sed facilisis porttitor purus venenatis ultrices',
        'Donec nunc quam, vestibulum eget faucibus sit amet,',
    ],
    requirements = [
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        'Pellentesque scelerisque diam eget fringilla tempor',
        'Sed facilisis porttitor purus venenatis ultrices',
        'Donec nunc quam, vestibulum eget faucibus sit amet,',
    ],
    benefits = [
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        'Pellentesque scelerisque diam eget fringilla tempor',
        'Sed facilisis porttitor purus venenatis ultrices',
    ],
}) => {
    const currentEntity = useSelector(selectCurrentEntityProfile)
    const isEntityView = useMemo(() => !!currentEntity.entityProfileId, [currentEntity.entityProfileId])
    const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>()
    const [optionsVisible, setOptionsVisible] = useState(false)
    const [closeModalVisible, setCloseModalVisible] = useState(false) // shared confirm modal visibility
    const [closing, setClosing] = useState(false)
    const [confirmAction, setConfirmAction] = useState<'withdraw' | 'close' | null>(null)
    const [openWithdrawAfterSheet, setOpenWithdrawAfterSheet] = useState(false)
    const [isClosed, setIsClosed] = useState(false)

    const { handleApply, isApplied, withdrawing, handleWithdraw, setIsApplied } = useJobActions({ jobId })

    const handleOptions = () => setOptionsVisible(true)
    const onWithdrawPress = () => {
        setConfirmAction('withdraw')
        setOpenWithdrawAfterSheet(true)
        setOptionsVisible(false)
    }

    const [displayTitle, setDisplayTitle] = useState(title)
    const [displayCompanyName, setDisplayCompanyName] = useState(companyName)
    const [displayMatchPercent, setDisplayMatchPercent] = useState(matchPercent)
    const [displayPostedAgoText, setDisplayPostedAgoText] = useState(postedAgoText)

    useEffect(() => {
        let isMounted = true
        const load = async () => {
            if (!jobId) return
            try {
                const data = await fetchJobForCandidate(jobId)
                if (!isMounted || !data) return
                if (data?.designation?.name) setDisplayTitle(data.designation.name)
                if (data?.entity?.name) setDisplayCompanyName(data.entity.name)
                if (typeof data?.matching === 'number') setDisplayMatchPercent(data.matching)
                if (data?.createdAt) setDisplayPostedAgoText(formatElapsedTime(data.createdAt))
                if (data?.applicationStatus) setIsApplied(data.applicationStatus === 'PENDING')
            } catch (_e) {
                showToast({ message: 'Error fetching job details', type: 'error' })
            }
        }
        load()
        return () => { isMounted = false }
    }, [jobId])


    const handleApplications = () => {
        navigation.navigate('Applicants', { jobId })
    }


    return (
        <View className='p-4'>
            <View className='flex-row items-center justify-between mb-1'>
                <View className='flex-row gap-2 items-center'>
                    <UserAvatar avatarUri={null} name={displayCompanyName} width={20} height={20} />
                    <Text className='text-sm font-semibold'>{displayCompanyName}</Text>
                </View>
                {!isEntityView && (
                    <Pressable onPress={() => handleOptions()}>
                        <HorizontalEllipsis />
                    </Pressable>
                )}
            </View>

            <Text className='font-semibold text-xl mb-2'>{displayTitle}</Text>

            <View className='flex-row items-center gap-2 mb-4'>
                <View className='flex-row items-center'>
                    <Text className='text-[#666363] text-sm'>{locationText}</Text>
                    <Text className='text-[#666363]'>{' • '}</Text>
                    <Text className='text-[#666363] text-sm'>{displayPostedAgoText}</Text>
                </View>
                <View className='flex-row items-center bg-backgroundGrayDark gap-1 rounded-full px-2 py-1.5 border-[#EAEAEA] border'>
                    <Match />
                    <Text className='text-lg font-semibold text-black'>{displayMatchPercent}%</Text>
                </View>
            </View>

            {/* Actions row */}
            {isEntityView ? (
                <View className='flex-row gap-3 mb-2'>
                    <Button label='View applicants' variant='outline' onPress={handleApplications} className='flex-1 rounded-lg' />
                    {isClosed ? (
                        <View className='flex-1 rounded-lg border border-gray-300 items-center justify-center'>
                            <Text className='text-gray-600'>Closed</Text>
                        </View>
                    ) : (
                        <Button label='Close job' variant='outline' onPress={() => { setConfirmAction('close'); setCloseModalVisible(true) }} className='flex-1 rounded-lg' />
                    )}
                </View>
            ) : (
                <View className='flex-row gap-3 mb-2'>
                    <Button
                        label={isApplied ? 'Applied' : 'Apply'}
                        variant={isApplied ? 'primaryOutline' : 'primary'}
                        onPress={() => {
                            if (isApplied) return
                            handleApply()
                        }}
                        disabled={isApplied}
                        className='flex-1 rounded-lg'
                    />
                    <Button
                        label='Save'
                        variant='outline'
                        onPress={() => { }}
                        className={`flex-1 rounded-lg`}
                    />
                </View>
            )}

            <View className='mt-4'>
                <Text className='text-base font-semibold mb-2'>About</Text>
                <Text className='text-[#333333] text-sm leading-5 mb-4'>{aboutText}</Text>

                <Text className='text-base font-semibold mb-2'>Roles and responsibilities</Text>
                <View className='mb-4'>
                    {roles.map((r, idx) => (
                        <View key={`role-${idx}`} className='flex-row items-start mb-1'>
                            <Text className='text-[#333333] text-sm mr-2'>{'•'}</Text>
                            <Text className='text-[#333333] text-sm flex-1'>{r}</Text>
                        </View>
                    ))}
                </View>

                <Text className='text-base font-semibold mb-2'>Requirements</Text>
                <View className='mb-4'>
                    {requirements.map((req, idx) => (
                        <View key={`req-${idx}`} className='flex-row items-start mb-1'>
                            <Text className='text-[#333333] text-sm mr-2'>{'•'}</Text>
                            <Text className='text-[#333333] text-sm flex-1'>{req}</Text>
                        </View>
                    ))}
                </View>

                <Text className='text-base font-semibold mb-2'>Benefits</Text>
                <View className='mb-6'>
                    {benefits.map((b, idx) => (
                        <View key={`ben-${idx}`} className='flex-row items-start mb-1'>
                            <Text className='text-[#333333] text-sm mr-2'>{'•'}</Text>
                            <Text className='text-[#333333] text-sm flex-1'>{b}</Text>
                        </View>
                    ))}
                </View>
            </View>

            <BottomSheet
                visible={optionsVisible}
                onClose={() => setOptionsVisible(false)}
                onModalHide={() => {
                    if (openWithdrawAfterSheet) {
                        setOpenWithdrawAfterSheet(false)
                        setCloseModalVisible(true)
                    }
                }}
            >
                <OptionsMenu>
                    <OptionItem label='Withdraw application' onPress={onWithdrawPress} />
                </OptionsMenu>
            </BottomSheet>

            <CustomModal
                isVisible={closeModalVisible}
                title={confirmAction === 'withdraw' ? 'Are you sure you want to withdraw your application?' : 'Are you sure you want to close this job?'}
                cancelText='Cancel'
                confirmText={confirmAction === 'withdraw' ? 'Withdraw' : 'Close job'}
                confirmButtonVariant='danger'
                isConfirming={confirmAction === 'withdraw' ? withdrawing : closing}
                onConfirm={async () => {
                    if (!jobId) return
                    if (confirmAction === 'withdraw') {
                        await handleWithdraw()
                        setCloseModalVisible(false)
                        setConfirmAction(null)
                        return
                    }
                    try {
                        setClosing(true)
                        await closeJob(jobId)
                        setIsClosed(true)
                        setCloseModalVisible(false)
                        showToast({ message: 'Job closed', type: 'success' })
                    } catch (_e) {
                        showToast({ message: 'Failed to close job', type: 'error' })
                    } finally {
                        setClosing(false)
                    }
                }}
                onCancel={() => {
                    setCloseModalVisible(false)
                    setConfirmAction(null)
                }}
            />
        </View>
    )
}

export default JobHead