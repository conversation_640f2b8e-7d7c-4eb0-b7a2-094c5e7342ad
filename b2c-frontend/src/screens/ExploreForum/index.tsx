import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import NotFound from '@/src/components/NotFound';
import SafeArea from '@/src/components/SafeArea';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import type { AppDispatch } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ForumPost from '@/src/screens/Forum/components/ForumPost';
import { transformQuestionToForumPost } from '@/src/screens/Forum/components/ForumPost/utils';
import ForumPostSkeleton from '@/src/screens/Forum/components/ForumPostSkelton';
import { fetchForumQuestionsAPI } from '@/src/networks/question/question';
import type {
  ForumQuestionFetchManyPayloadI,
  ForumQuestionI,
  ForumQuestionResultI,
} from '@/src/networks/question/types';

const POSTS_PER_PAGE = 10;

const ExploreForumScreen: React.FC = () => {
  const route = useRoute<StackScreenProps<LearnCollabStackParamsListI, 'ExploreForum'>['route']>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();

  // Data state
  const [forumQuestions, setForumQuestions] = useState<ForumQuestionI[]>([]);
  const [paginationCursor, setPaginationCursor] = useState<string | null>(null);
  const [hasMorePages, setHasMorePages] = useState(true);

  // Loading states
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const buildForumQueryParams = useCallback(
    (cursorDate: string | null, shouldResetCursor: boolean): ForumQuestionFetchManyPayloadI => {
      const routeParams = route.params;
      const questionType = routeParams?.type ?? (routeParams?.topicId ? 'NORMAL' : undefined);

      return {
        pageSize: POSTS_PER_PAGE.toString(),
        cursorDate: shouldResetCursor ? null : cursorDate,
        ...(questionType && { type: questionType }),
        ...(routeParams?.topicId &&
          routeParams?.topicDataType && {
            topicId: routeParams.topicId,
            topicDataType: routeParams.topicDataType,
          }),
        ...(routeParams?.equipmentCategory && {
          equipmentCategoryId: routeParams.equipmentCategory.id,
          equipmentCategoryDataType: routeParams.equipmentCategory.dataType,
        }),
        ...(routeParams?.equipmentManufacturer && {
          equipmentManufacturerId: routeParams.equipmentManufacturer.id,
          equipmentManufacturerDataType: routeParams.equipmentManufacturer.dataType,
        }),
        ...(routeParams?.equipmentModel && {
          equipmentModelId: routeParams.equipmentModel.id,
          equipmentModelDataType: routeParams.equipmentModel.dataType,
        }),
      };
    },
    [route.params],
  );

  const loadForumQuestions = useCallback(
    async (shouldRefresh: boolean) => {
      try {
        setErrorMessage(null);

        if (shouldRefresh) {
          setIsRefreshing(true);
        } else if (forumQuestions.length === 0) {
          setIsInitialLoading(true);
        } else {
          setIsLoadingMore(true);
        }

        const queryParams = buildForumQueryParams(paginationCursor, shouldRefresh);
        const apiResponse = await fetchForumQuestionsAPI(queryParams);

        if (shouldRefresh) {
          // Replace all questions on refresh
          setForumQuestions(apiResponse.data);
        } else {
          // Append new questions and deduplicate
          const existingQuestionIds = new Set(forumQuestions.map((question) => question.id));
          const newUniqueQuestions = apiResponse.data.filter(
            (question) => !existingQuestionIds.has(question.id),
          );
          setForumQuestions((prevQuestions) => [...prevQuestions, ...newUniqueQuestions]);
        }

        // Update pagination state
        setPaginationCursor(apiResponse.nextCursorDate);

        // Calculate if there are more pages available
        const totalLoadedQuestions = shouldRefresh
          ? apiResponse.data.length
          : forumQuestions.length + apiResponse.data.length;

        const hasMoreData =
          totalLoadedQuestions < apiResponse.total && Boolean(apiResponse.nextCursorDate);
        setHasMorePages(hasMoreData);
      } catch (error) {
        console.error('Failed to load forum questions:', error);
        setErrorMessage('Failed to load forum posts. Please try again.');
      } finally {
        setIsLoadingMore(false);
        setIsRefreshing(false);
        setIsInitialLoading(false);
      }
    },
    [buildForumQueryParams, paginationCursor, forumQuestions],
  );

  // Reset state and load initial data when route params change
  useEffect(() => {
    setForumQuestions([]);
    setPaginationCursor(null);
    setHasMorePages(true);
    setErrorMessage(null);
    loadForumQuestions(true);
  }, [route.params]);

  const handleRefresh = useCallback(() => {
    loadForumQuestions(true);
  }, [loadForumQuestions]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMorePages && !errorMessage) {
      loadForumQuestions(false);
    }
  }, [isLoadingMore, hasMorePages, errorMessage, loadForumQuestions]);

  const handleRetry = useCallback(() => {
    if (forumQuestions.length === 0) {
      loadForumQuestions(true);
    } else {
      loadForumQuestions(false);
    }
  }, [forumQuestions.length, loadForumQuestions]);

  const transformedForumPosts = useMemo(
    () =>
      forumQuestions.map((question) =>
        transformQuestionToForumPost(question as unknown as ForumQuestionResultI, question.media),
      ),
    [forumQuestions],
  );

  const handleForumPostSelection = useCallback(
    async (id: string) => {
      try {
        await dispatch(fetchForumQuestionDetail({ questionId: id })).unwrap();
        navigation.navigate('ForumAnswers', { postId: id });
      } catch (error) {
        console.error('Failed to fetch question detail:', error);
      }
    },
    [dispatch, navigation],
  );

  const renderForumPost = useCallback(
    ({ item: forumPost }: { item: ReturnType<typeof transformQuestionToForumPost> }) => (
      <ForumPost
        post={{
          ...forumPost,
          onPress: () => handleForumPostSelection(forumPost.postId),
          community: forumPost.community?.name,
        }}
      />
    ),
    [handleForumPostSelection],
  );

  const renderLoadingFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#666" />
      </View>
    );
  }, [isLoadingMore]);

  const renderErrorMessage = useCallback(() => {
    if (!errorMessage || isInitialLoading) return null;

    return (
      <View className="py-4 px-4 items-center">
        <Text className="text-red-500 text-center mb-2">{errorMessage}</Text>
        <TouchableOpacity onPress={handleRetry} className="bg-blue-500 px-4 py-2 rounded">
          <Text className="text-white font-medium">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }, [errorMessage, isInitialLoading]);

  const renderInitialLoadingSkeleton = useCallback(
    () => (
      <View className="flex-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <ForumPostSkeleton key={`forum-skeleton-${index}`} />
        ))}
      </View>
    ),
    [],
  );

  if (isInitialLoading && forumQuestions.length === 0) {
    return (
      <SafeArea>
        <BackButton onBack={() => navigation.goBack()} label="Explore" />
        <View className="flex-1 bg-white">{renderInitialLoadingSkeleton()}</View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <BackButton
        onBack={() => navigation.goBack()}
        label="Explore Forums"
        labelClassname="font-semibold text-2xl"
      />
      <View className="flex-1 bg-white">
        <FlatList<ReturnType<typeof transformQuestionToForumPost>>
          data={transformedForumPosts}
          keyExtractor={(forumPost) => forumPost.postId}
          renderItem={renderForumPost}
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: 'white',
            paddingBottom: 20,
          }}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} tintColor="#666" />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={
            <>
              <NotFound
                title="No questions found"
                subtitle="Try searching for a different keyword"
              />
            </>
          }
          ListFooterComponent={renderLoadingFooter}
          removeClippedSubviews={true}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
        />
        {renderErrorMessage()}
      </View>
    </SafeArea>
  );
};

export default ExploreForumScreen;
