import { View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LottieView from 'lottie-react-native';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import SafeArea from '@/src/components/SafeArea';
import TextView from '@/src/components/TextView';
import { HomeStackParamListI } from '@/src/navigation/types';

type EntityProfileSuccessRouteProp = RouteProp<HomeStackParamListI, 'CreateEntityProfileSuccess'>;

const CreateEntityProfileSuccessScreen = () => {
  const route = useRoute<EntityProfileSuccessRouteProp>();
  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI, 'EntityProfile'>>();
  const { entityProfileId } = route.params;

  const handleViewProfile = () => {
    navigation.navigate('EntityProfile', { entityProfileId });
  };

  const handleBack = () => {
    navigation.navigate('Home');
  };

  return (
    <SafeArea>
      <BackButton label="" onBack={handleBack} />
      <View className="flex-1 justify-center items-center">
        <View className="items-center gap-y-6 px-4">
          <LottieView
            source={require('../../assets/animations/ship.json')}
            autoPlay
            loop
            style={{ width: 200, height: 200 }}
          />
          <TextView
            title="Your company profile has been created!"
            subtitle="You can now view and edit your profile"
            titleClassName="text-center text-xl font-bold text-gray-900"
            subtitleClassName="text-center text-base text-gray-600"
          />
        </View>
      </View>

      <View className="pb-40 mx-5">
        <Button
          label="View Profile"
          onPress={handleViewProfile}
          variant="primary"
          className="rounded-lg"
        />
      </View>
    </SafeArea>
  );
};

export default CreateEntityProfileSuccessScreen;
