import { ActivityIndicator, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import NotFound from '@/src/components/NotFound';
import ExploreHead from '../ExploreHead';
import ExploreQnA from '../ExploreQna';
import ExploreTroubleShoot from '../ExploreTroubleShoot';
import RecommendedCommunities from '../RecommendedCommunities';
import useExploreForum from './useHook';

const ExploreForum = () => {
  const { loading, topics, recommendedCommunities } = useExploreForum();

  return loading ? (
    <View className="py-6">
      <ActivityIndicator />
    </View>
  ) : (
    <View>
      <ExploreHead />
      <ScrollView>
        <View className="mb-40">
          <ExploreQnA title="Explore QnA" tags={topics} showAllText="See More" />
          {topics.length === 0 ? (
            <NotFound
              title="No topics found"
              subtitle="Try again later or explore troubleshooting posts"
              fullScreen={false}
              className="py-4"
              titleClassName="text-base font-medium"
            />
          ) : null}
          <ExploreTroubleShoot />
          <RecommendedCommunities communities={recommendedCommunities} />
        </View>
      </ScrollView>
    </View>
  );
};

export default ExploreForum;
