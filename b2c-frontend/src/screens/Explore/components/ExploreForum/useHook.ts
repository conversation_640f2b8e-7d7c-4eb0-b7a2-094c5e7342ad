import { useEffect, useState } from 'react';
import type { FetchCommunityClientI } from '@/src/networks/community/types';
import { fetchForumExploreAPI } from '@/src/networks/forum/explore';
import { ForumExploreResponseI } from '@/src/networks/forum/types';
import { QnATagI } from '../ExploreQna/types';

const useExploreForum = () => {
  const [topics, setTopics] = useState<QnATagI[]>([]);
  const [recommendedCommunities, setRecommendedCommunities] = useState<FetchCommunityClientI[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchExplore = async () => {
      try {
        setLoading(true);
        const response: ForumExploreResponseI = await fetchForumExploreAPI();
        const transformedTopics: QnATagI[] = (response.topics || []).map(
          (topic: { id: string; name: string; dataType: 'master' | 'raw' }) => ({
            id: topic.id,
            text: topic.name,
            dataType: topic.dataType,
          }),
        );
        setTopics(transformedTopics);
        setRecommendedCommunities(response.recommendedCommunities || []);
      } catch (error) {
        console.error('Error fetching explore forum data:', error);
        setTopics([]);
        setRecommendedCommunities([]);
      } finally {
        setLoading(false);
      }
    };

    fetchExplore();
  }, []);

  return {
    topics,
    recommendedCommunities,
    loading,
  };
};

export default useExploreForum;
