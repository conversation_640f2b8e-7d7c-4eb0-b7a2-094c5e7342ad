/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import { fetchCommunityMembersAPI } from '@/src/networks/community/members';
import { UseMemberListPropsI, MemberListHookResultI } from './types';

export const useMemberList = ({
  forumId: communityId,
  pageSize = 10,
}: UseMemberListPropsI): MemberListHookResultI => {
  const [data, setData] = useState<ListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [search, setSearch] = useState<string>('');

  const transformToListItems = (
    members: Array<{
      Profile: {
        id: string;
        avatar: string | null;
        name: string;
        designationText: string | null;
        entityText: string | null;
      };
      role: string;
    }>,
  ): ListItem[] => {
    return members.map((m) => ({
      Profile: {
        id: m.Profile.id,
        avatar: m.Profile.avatar,
        name: m.Profile.name,
        designation: m.Profile.designationText
          ? { id: '', name: m.Profile.designationText, dataType: 'master' }
          : null,
        entity: m.Profile.entityText
          ? { id: '', name: m.Profile.entityText, dataType: 'master' }
          : null,
      },
      status: m.role,
    }));
  };

  const fetchMembers = useCallback(
    async (isRefresh = false) => {
      try {
        if (isRefresh) setRefreshing(true);
        else setLoading(true);

        const result = await fetchCommunityMembersAPI({
          communityId,
          cursorId: isRefresh ? null : cursorId,
          pageSize,
          type: 'ALL',
          search: search.trim().length >= 2 ? search.trim() : undefined,
        });

        const items = transformToListItems(result.data);

        if (isRefresh || !cursorId) {
          setData(items);
        } else {
          setData((prev) => [...prev, ...items]);
        }

        setCursorId(result.nextCursorId ?? null);
        setHasMore(!!result.nextCursorId);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [communityId, cursorId, pageSize, search],
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchMembers(false);
    }
  }, [loading, hasMore]);

  const refresh = useCallback(() => {
    setCursorId(null);
    setHasMore(true);
    fetchMembers(true);
  }, [fetchMembers]);

  const removeLocally = useCallback((profileId: string) => {
    setData((prev) => prev.filter((i) => i.Profile.id !== profileId));
  }, []);

  const replaceData = useCallback((newData: ListItem[]) => {
    setData(newData);
  }, []);

  useEffect(() => {
    fetchMembers(true);
  }, []);

  useEffect(() => {
    setCursorId(null);
    setHasMore(true);
    fetchMembers(true);
  }, [search]);

  const updateRoleLocally = useCallback(
    (profileId: string, role: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER') => {
      setData((prev) => prev.map((i) => (i.Profile.id === profileId ? { ...i, status: role } : i)));
    },
    [],
  );

  return {
    data,
    loading,
    refreshing,
    hasMore,
    error,
    loadMore,
    refresh,
    setSearch,
    removeLocally,
    replaceData,
    updateRoleLocally,
  };
};
