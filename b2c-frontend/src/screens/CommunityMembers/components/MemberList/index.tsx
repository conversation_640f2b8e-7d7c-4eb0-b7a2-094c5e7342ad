import { useMemo, useState } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { showToast } from '@/src/utilities/toast';
import Tick from '@/src/assets/svgs/Tick';
import TrashBin from '@/src/assets/svgs/TrashBin';
import UserRole from '@/src/assets/svgs/UserRole';
import { fetchCommunityById } from '@/src/networks/community/fetchOne';
import {
  removeCommunityMemberAPI,
  updateCommunityMemberRoleAPI,
} from '@/src/networks/community/members';
import MemberListHeader from '../MemberListHeader';
import { MemberListNavigationI, MemberListRouteI } from './types';
import { useMemberList } from './useHook';

const MembersList = () => {
  const route = useRoute<MemberListRouteI>();
  const communityId = route.params?.communityId as string;
  const {
    data,
    loadMore,
    loading,
    refresh,
    refreshing,
    setSearch,
    removeLocally,
    updateRoleLocally,
  } = useMemberList({
    forumId: communityId,
  });
  const currentUser = useSelector(selectCurrentUser);
  const [roleModalFor, setRoleModalFor] = useState<{ profileId: string; visible: boolean } | null>(
    null,
  );
  const [communityAccess, setCommunityAccess] = useState<
    'PUBLIC' | 'PRIVATE' | 'GLOBAL' | undefined | string
  >();
  const [confirmState, setConfirmState] = useState<{
    visible: boolean;
    profileId?: string;
    nextRole?: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
  } | null>(null);
  const [roleChanging, setRoleChanging] = useState<boolean>(false);
  const [roleChangeTarget, setRoleChangeTarget] = useState<{
    profileId: string;
    nextRole: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
  } | null>(null);
  const [removeChanging, setRemoveChanging] = useState<boolean>(false);

  useMemo(() => {
    if (communityId) {
      fetchCommunityById(communityId)
        .then((res) => setCommunityAccess(res.access))
        .catch(() => {});
    }
  }, [communityId]);

  const handleUserPress = (user: ListItem) => {
    if (user?.Profile?.id) {
      navigation.navigate('OtherUserProfile', { fromTabPress: false, profileId: user.Profile.id });
    }
  };

  const navigation = useNavigation<MemberListNavigationI>();
  const handleBack = () => {
    navigation.goBack();
  };

  if (loading && !data.length) {
    return (
      <View className="flex-1 bg-white">
        <MemberListHeader handleBack={handleBack} />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" />
        </View>
      </View>
    );
  }

  const doRemove = async (profileId: string) => {
    try {
      setRemoveChanging(true);
      await removeCommunityMemberAPI({ communityId, profileId });
      removeLocally(profileId);
      showToast({ message: 'Member removed', type: 'success' });
    } catch (e) {
      showToast({ message: 'Failed to remove member', type: 'error' });
    } finally {
      setRemoveChanging(false);
    }
  };

  const confirmRemove = (profileId: string) => {
    setConfirmState({ visible: true, profileId });
  };

  const submitRole = async (
    profileId: string,
    type: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER',
  ) => {
    try {
      setRoleChanging(true);
      await updateCommunityMemberRoleAPI({ communityId, profileId, type: type as any });
      updateRoleLocally(profileId, type);
      setRoleModalFor(null);
      showToast({ message: 'Role updated', type: 'success' });
    } catch (e) {
      showToast({ message: 'Failed to update role', type: 'error' });
    } finally {
      setRoleChanging(false);
    }
  };

  const handleRolePress = async (nextRole: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER') => {
    if (!roleModalFor?.profileId) return;
    const profileId = roleModalFor.profileId;
    setRoleChangeTarget({ profileId, nextRole });
    await submitRole(profileId, nextRole);
    setRoleChangeTarget(null);
  };

  const renderActions = (item: ListItem) => {
    const profileId = item.Profile?.id;
    const isOwnAccount = currentUser.profileId === profileId;
    if (!profileId || isOwnAccount) return null;

    return (
      <View className="flex-row items-center gap-3">
        <Pressable
          onPress={(e) => {
            e.stopPropagation();
            setRoleModalFor({ profileId, visible: true });
          }}
          accessibilityLabel="Change role"
        >
          <UserRole height={3.5} width={3.5} />
        </Pressable>

        <Pressable
          onPress={(e) => {
            e.stopPropagation();
            confirmRemove(profileId);
          }}
          accessibilityLabel="Remove member"
        >
          <TrashBin width={2.75} height={2.75} color="#DC2626" />
        </Pressable>
      </View>
    );
  };

  const showContributor = communityAccess !== 'PUBLIC';

  return (
    <View className="flex-1 bg-white">
      <MemberListHeader
        handleBack={handleBack}
        onSearch={(s) => {
          setSearch(s);
        }}
      />
      {data.length === 0 ? (
        <NotFound
          title="No members found"
          subtitle="When there are forum members, they will appear here"
        />
      ) : (
        <UsersList
          data={data}
          loading={loading}
          onLoadMore={loadMore}
          onPress={handleUserPress}
          onRefresh={refresh}
          refreshing={refreshing}
          renderActions={renderActions}
        />
      )}

      <BottomSheet
        visible={!!roleModalFor?.visible}
        onClose={() => setRoleModalFor(null)}
        onModalHide={() => {}}
        height={320}
      >
        <View className="bg-gray-200">
          <OptionsMenu>
            <Text className="text-xl font-semibold text-gray-900 mb-1">Select Role</Text>
            {(() => {
              const selectedUser = data.find((i) => i.Profile.id === roleModalFor?.profileId);
              const currentRole = (selectedUser?.status || '').toUpperCase() as
                | 'ADMIN'
                | 'MODERATOR'
                | 'CONTRIBUTOR'
                | 'MEMBER'
                | '';
              const isCurrent = (role: string) => currentRole === role;
              return (
                <>
                  <OptionItem
                    label="Admin"
                    onPress={() => handleRolePress('ADMIN')}
                    disabled={isCurrent('ADMIN') || roleChanging}
                    rightIcon={
                      roleChanging && roleChangeTarget?.nextRole === 'ADMIN' ? (
                        <ActivityIndicator size="small" />
                      ) : isCurrent('ADMIN') ? (
                        <Tick width={2.75} height={2.0} color="#5a8d3b" />
                      ) : undefined
                    }
                  />
                  <View className="h-[1px]" />
                  <OptionItem
                    label="Member"
                    onPress={() => handleRolePress('MEMBER')}
                    disabled={isCurrent('MEMBER') || roleChanging}
                    rightIcon={
                      roleChanging && roleChangeTarget?.nextRole === 'MEMBER' ? (
                        <ActivityIndicator size="small" />
                      ) : isCurrent('MEMBER') ? (
                        <Tick width={2.75} height={2.0} color="#5a8d3b" />
                      ) : undefined
                    }
                  />
                  <View className="h-[1px] " />
                  <OptionItem
                    label="Moderator"
                    onPress={() => handleRolePress('MODERATOR')}
                    disabled={isCurrent('MODERATOR') || roleChanging}
                    rightIcon={
                      roleChanging && roleChangeTarget?.nextRole === 'MODERATOR' ? (
                        <ActivityIndicator size="small" />
                      ) : isCurrent('MODERATOR') ? (
                        <Tick width={2.75} height={2.0} color="#5a8d3b" />
                      ) : undefined
                    }
                  />
                  {showContributor && (
                    <>
                      <View className="h-[1px] " />
                      <OptionItem
                        label="Contributor"
                        onPress={() => handleRolePress('CONTRIBUTOR')}
                        disabled={isCurrent('CONTRIBUTOR') || roleChanging}
                        rightIcon={
                          roleChanging && roleChangeTarget?.nextRole === 'CONTRIBUTOR' ? (
                            <ActivityIndicator size="small" />
                          ) : isCurrent('CONTRIBUTOR') ? (
                            <Tick width={2.75} height={2.0} color="#5a8d3b" />
                          ) : undefined
                        }
                      />
                    </>
                  )}
                </>
              );
            })()}
          </OptionsMenu>
        </View>
      </BottomSheet>

      <CustomModal
        isVisible={!!confirmState?.visible}
        title={'Remove member?'}
        description={'This member will be removed from the community.'}
        cancelText="Cancel"
        confirmText={'Remove'}
        confirmButtonVariant={'danger'}
        isConfirming={removeChanging}
        onCancel={() => setConfirmState({ visible: false })}
        onConfirm={async () => {
          if (confirmState?.profileId) {
            await doRemove(confirmState.profileId);
          }
          setConfirmState({ visible: false });
        }}
      />
    </View>
  );
};

export default MembersList;
