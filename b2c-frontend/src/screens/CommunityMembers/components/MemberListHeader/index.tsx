import { useCallback, useEffect, useState } from 'react';
import { Text, TextInput, View } from 'react-native';
import BackButton from '@/src/components/BackButton';

const MemberListHeader = ({
  handleBack,
  onSearch,
}: {
  handleBack: () => void;
  onSearch?: (s: string) => void;
}) => {
  const [value, setValue] = useState('');

  useEffect(() => {
    const t = setTimeout(() => {
      onSearch?.(value.trim());
    }, 350);
    return () => clearTimeout(t);
  }, [value, onSearch]);

  const onChangeText = useCallback((t: string) => setValue(t), []);

  return (
    <View className="px-4 py-3 border-b border-gray-100">
      <View className="flex-row items-center mb-2">
        <BackButton onBack={handleBack} label="" />
        <Text className="text-xl font-semibold text-gray-900 ml-2">Member List</Text>
      </View>
      <TextInput
        placeholder="Search members..."
        value={value}
        onChangeText={onChangeText}
        className="mt-1 px-3 py-4 rounded-xl bg-gray-100 flex items-center"
      />
    </View>
  );
};

export default MemberListHeader;
