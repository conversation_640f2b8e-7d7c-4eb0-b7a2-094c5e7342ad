import { useState, useMemo } from 'react';
import { View } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import SafeArea from '@/src/components/SafeArea';
import {
  selectQuestionIsGeneralDepartment,
  selectQuestionType,
  selectQuestionIsAnonymous,
  selectQuestionIsLive,
} from '@/src/redux/selectors/question';
import { selectSelectionByKey, selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { resetCommunityState } from '@/src/redux/slices/community/communitySlice';
import { clearAllSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  setTopics,
  setEquipmentCategory,
  setEquipmentModel,
  setEquipmentManufacturer,
  setDepartment,
  setImo,
  resetFormData,
} from '@/src/redux/slices/question/questionSlice';
import type { AppDispatch } from '@/src/redux/store';
import type { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateCommunityHeader from '../CreateCommunity/components/CreateCommunityHeader';
import AskQuestionForm, { validateAskQuestionForm } from './components/AskQuestionForm';

const CommunityQuestionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'CommunityQuestion'>>();
  const dispatch = useDispatch<AppDispatch>();

  const { id, communityId } = route.params;

  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic')) || [];
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const equipmentCategorySelection = useSelector(selectSelectionByKey('equipmentCategory'));
  const equipmentManufacturerSelection = useSelector(selectSelectionByKey('equipmentManufacturer'));
  const equipmentModelSelection = useSelector(selectSelectionByKey('equipmentModel'));
  const imoSelection = useSelector(selectSelectionByKey('ship')) as unknown as {
    imo: string;
    dataType: string;
    name: string;
  };

  const questionType = useSelector(selectQuestionType);
  const questionIsGeneral = useSelector(selectQuestionIsGeneralDepartment);
  const isAnonymous = useSelector(selectQuestionIsAnonymous);
  const isLive = useSelector(selectQuestionIsLive);

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showDiscardModal, setShowDiscardModal] = useState(false);

  const initialQuestionType = 'NORMAL';
  const initialIsAnonymous = false;
  const initialIsLive = false;
  const initialIsGeneral = false;
  const initialTopics: SearchResultI[] = [];
  const initialDepartment = undefined;
  const initialEquipmentCategory = undefined;
  const initialEquipmentManufacturer = undefined;
  const initialEquipmentModel = undefined;
  const initialImo = undefined;

  const hasChanges = useMemo(() => {
    if (questionType !== initialQuestionType) return true;
    if (isAnonymous !== initialIsAnonymous) return true;
    if (isLive !== initialIsLive) return true;
    if (questionIsGeneral !== initialIsGeneral) return true;
    if (topicsSelection.length !== initialTopics.length) return true;
    if (topicsSelection.some((topic, index) => topic.id !== initialTopics[index]?.id)) return true;
    if (departmentSelection !== initialDepartment) return true;
    if (equipmentCategorySelection !== initialEquipmentCategory) return true;
    if (equipmentManufacturerSelection !== initialEquipmentManufacturer) return true;
    if (equipmentModelSelection !== initialEquipmentModel) return true;
    if (imoSelection !== initialImo) return true;
    return false;
  }, [
    questionType,
    isAnonymous,
    isLive,
    questionIsGeneral,
    topicsSelection,
    departmentSelection,
    equipmentCategorySelection,
    equipmentManufacturerSelection,
    equipmentModelSelection,
    imoSelection,
  ]);

  const handleNext = () => {
    const state = {
      question: { formData: { type: questionType, isGeneral: questionIsGeneral } },
      search: {
        multipleSelections: { topic: topicsSelection },
        selections: {
          department: departmentSelection,
          equipmentCategory: equipmentCategorySelection,
          equipmentManufacturer: equipmentManufacturerSelection,
          equipmentModel: equipmentModelSelection,
          imo: imoSelection,
        },
      },
    };
    const validation = validateAskQuestionForm(state);
    setFormErrors(validation.errors);

    if (!validation.isValid) {
      return;
    }

    if (topicsSelection) {
      dispatch(setTopics(topicsSelection));
    }
    if (departmentSelection) {
      dispatch(setDepartment(departmentSelection));
    }

    if (questionType === 'TROUBLESHOOT') {
      if (equipmentCategorySelection) {
        dispatch(setEquipmentCategory(equipmentCategorySelection));
      }
      if (equipmentManufacturerSelection) {
        dispatch(setEquipmentManufacturer(equipmentManufacturerSelection));
      }
      if (equipmentModelSelection) {
        dispatch(setEquipmentModel(equipmentModelSelection));
      }
      if (imoSelection) {
        dispatch(setImo(imoSelection as unknown as SearchResultI));
      }
    }
    navigation.navigate('CreateQuestion', { communityId });
  };

  const handleDiscardConfirm = () => {
    dispatch(resetFormData());
    dispatch(clearAllSelections());
    dispatch(resetCommunityState());
    setShowDiscardModal(false);
    navigation.goBack();
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  const insets = useSafeAreaInsets();

  return (
    <SafeArea>
      <ScrollView
        contentContainerStyle={{
          paddingBottom: 85 + insets.bottom,
        }}
      >
        <View className="flex-1 p-4 bg-white">
          <CreateCommunityHeader
            currentPage={1}
            totalPage={2}
            onNext={handleNext}
            hasChanges={hasChanges}
            onShowDiscardModal={setShowDiscardModal}
            onDiscard={handleDiscardConfirm}
          />
          <AskQuestionForm formErrors={formErrors} />
        </View>
      </ScrollView>
      <CustomModal
        isVisible={showDiscardModal}
        title="Discard Changes?"
        description="You have unsaved changes. Are you sure you want to discard them?"
        cancelText="Cancel"
        confirmText="Discard"
        onConfirm={handleDiscardConfirm}
        onCancel={handleDiscardCancel}
        confirmButtonVariant="danger"
      />
    </SafeArea>
  );
};

export default CommunityQuestionScreen;
