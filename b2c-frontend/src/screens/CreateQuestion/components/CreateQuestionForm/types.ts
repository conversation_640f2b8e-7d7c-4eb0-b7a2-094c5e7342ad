export interface CreateQuestionFormProps {
  onSuccess: () => void;
  editing?: boolean;
  questionId?: string;
  communityId?: string;
}

export interface MediaI {
  uri: string;
  type: string;
  filename: string;
}

export interface QuestionFormData {
  title: string;
  description: string;
  attachments?: MediaI[];
}

export interface CreateQuestionPayload {
  title: string;
  description: string;
  attachments?: {
    fileUrl: string;
    filename: string;
    mimeType: string;
  }[];
}
