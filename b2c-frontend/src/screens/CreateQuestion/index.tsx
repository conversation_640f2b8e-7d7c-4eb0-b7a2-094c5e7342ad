import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI, LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateQuestionForm from './components/CreateQuestionForm';

const CreateQuestionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'CreateQuestion'>>();
  const { editing, questionId, communityId } = route.params || {};

  const handleSuccess = () => {
    if (communityId) {
      navigation.navigate('Community', { id: communityId });
    } else {
      navigation.navigate('Forum');
    }
  };

  return (
    <SafeArea>
      <CreateQuestionForm
        onSuccess={handleSuccess}
        editing={editing}
        questionId={questionId}
        communityId={communityId}
      />
    </SafeArea>
  );
};

export default CreateQuestionScreen;
