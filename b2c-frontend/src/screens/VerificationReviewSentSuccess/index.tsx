import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LottieView from 'lottie-react-native';
import Button from '@/src/components/Button';
import TextView from '@/src/components/TextView';

const VerificationReviewSentSuccessScreen = () => {
  const navigation = useNavigation();

  const handleHomePress = () => {
    navigation.navigate('HomeStack', {
      screen: 'Home',
      params: undefined,
    });
  };

  const insets = useSafeAreaInsets();

  return (
    <View className="flex-1">
      <View className="flex-1 justify-center items-center">
        <View className="items-center gap-y-6 px-4">
          <LottieView
            source={require('../../assets/animations/ship.json')}
            autoPlay
            loop
            style={{ width: 200, height: 200 }}
          />
          <TextView
            title="Verification under review!"
            subtitle="Our team will verify your request and create your company profile shortly"
            titleClassName="text-center text-xl font-bold text-gray-900"
            subtitleClassName="text-center text-base text-gray-600"
          />
        </View>
      </View>

      <View className="pb-40 mx-5">
        <Button
          label="Back to Home"
          onPress={handleHomePress}
          variant="primary"
          className="rounded-lg"
        />
      </View>
    </View>
  );
};

export default VerificationReviewSentSuccessScreen;
