import React from 'react';
import { useState, useMemo } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import Close from '@/src/assets/svgs/Close';
import Tick from '@/src/assets/svgs/Tick';
import { FollowsListProps } from './types';
import { useFollows } from './useHook';

const FollowsList: React.FC<FollowsListProps> = ({ entityProfileId, type, onProfilePress }) => {
  const { data, loading, loadMore, refresh, refreshing } = useFollows({
    entityProfileId,
    type,
  });

  const transformedData = useMemo(() => {
    return data.map((item) => ({
      Profile: item.Profile,
      status: item.status,
      EntityProfile: item.EntityProfile,
    }));
  }, [data]);

  if (loading && !data.length) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (!data.length) {
    return (
      <NotFound
        title={`No ${type.replace('_', ' ')} found`}
        subtitle="When you have connections, they will appear here"
      />
    );
  }

  return (
    <UsersList
      key={type}
      data={transformedData}
      loading={loading}
      onLoadMore={loadMore}
      onPress={onProfilePress}
      onRefresh={refresh}
      refreshing={refreshing}
    />
  );
};

export default FollowsList;
