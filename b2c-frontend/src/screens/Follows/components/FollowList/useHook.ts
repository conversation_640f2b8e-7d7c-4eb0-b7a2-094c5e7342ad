import { useState, useEffect, useRef, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import { showToast } from '@/src/utilities/toast';
import { fetchConnectionsAPI, fetchMutualsAPI } from '@/src/networks/connect/connection';
import { fetchFollowersAPI, fetchFollowingAPI } from '@/src/networks/connect/follow';
import {
  fetchReceivedRequestsAPI,
  fetchSentRequestsAPI,
  respondReceivedRequestAPI,
} from '@/src/networks/connect/request';
import { FetchFollowersDataI, FetchFollowersResponseI } from '@/src/networks/connect/types';
import {
  fetchEntityProfileFollowersAPI,
  fetchEntityProfileFollowingsAPI,
} from '@/src/networks/entityProfile/fetchFollow';
import { UseFollowsProps } from './types';

export const useFollows = ({ entityProfileId, type, pageSize = 10 }: UseFollowsProps) => {
  const [data, setData] = useState<FetchFollowersDataI[]>([]);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [page, setPage] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const fetchData = useCallback(
    async (isRefresh = false) => {
      if (isRefresh) {
        setRefreshing(true);
      } else if (!isRefresh && !data.length) {
        setLoading(true);
      }
      try {
        let result: FetchFollowersResponseI;

        switch (type) {
          case 'followers':
            result = await fetchEntityProfileFollowersAPI({
              entityProfileId,
              cursorId: isRefresh ? null : cursorId,
              pageSize,
            });
            break;
          case 'following':
            result = await fetchEntityProfileFollowingsAPI({
              entityProfileId,
              cursorId: isRefresh ? null : cursorId,
              pageSize,
            });
            break;
          default:
            throw new Error(`Unknown connection type: ${type}`);
        }

        const newData = result.data || [];
        const totalCount = result.total || 0;
        setTotal(totalCount);

        const minCursorItem =
          newData.length > 0
            ? newData.reduce((max, item) => (item.cursorId < max.cursorId ? item : max), newData[0])
            : null;
        const newCursorId = minCursorItem?.cursorId ?? null;
        const updatedData = isRefresh ? newData : [...data, ...newData];
        setHasMore(updatedData.length < totalCount);
        setCursorId(newCursorId);
        setData(updatedData);
      } catch (err) {
        const errorMsg = `Error fetching ${type}`;
        if (!data.length && !isRefresh) {
          triggerErrorBoundary(
            new Error(
              'Failed to load initial data: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } else {
          showToast({
            type: 'error',
            message: 'Error',
            description: errorMsg,
          });
        }
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [type, entityProfileId, data, cursorId, page, pageSize],
  );

  useEffect(() => {
    setLoading(true);
    setData([]);
    setCursorId(null);
    setPage(0);
    setTotal(0);
    setHasMore(false);
    setRefreshing(false);
    fetchData(true);
  }, [type, entityProfileId]);

  const loadMore = () => {
    if (!loading && !refreshing && hasMore && total > pageSize) {
      fetchData(false);
    }
  };

  const refresh = () => {
    fetchData(true);
  };

  return {
    data,
    loading,
    refreshing,
    hasMore,
    loadMore,
    refresh,
    total,
  };
};

export default useFollows;
