/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ListItem } from '@/src/components/UsersList/types';
import { FetchFollowersDataI } from '@/src/networks/connect/types';

export interface FollowsListProps {
  entityProfileId: string;
  type: string;
  onProfilePress: (user: ListItem) => void;
}

export interface UseFollowsProps {
  entityProfileId: string;
  type: string;
  pageSize?: number;
}

export interface ConnectionsHookResult {
  data: FetchFollowersDataI[];
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  error: Error | null;
  loadMore: () => void;
  refresh: () => void;
  loadingRequestIds: Record<string, boolean>;
  handleConnectionRequest: (item: ListItem, status: 'ACCEPTED' | 'REJECTED') => Promise<void>;
  requestStatus: Record<string, 'ACCEPTED' | 'REJECTED' | undefined>;
}
