/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { ListItem } from '@/src/components/UsersList/types';
import {
  BottomTabNavigationI,
  HomeStackParamListI,
  ProfileStackParamsListI,
} from '@/src/navigation/types';
import FollowsList from './components/FollowList';

const Follows = () => {
  const route = useRoute<RouteProp<HomeStackParamListI, 'Follows'>>();
  const { entityProfileId, type } = route.params;
  const navigation = useNavigation<BottomTabNavigationI>();
  const tabs = [
    { id: 'followers', label: 'Followers' },
    { id: 'following', label: 'Following' },
  ];

  const [activeTab, setActiveTab] = useState<string>(type);

  const handleProfilePress = (item: ListItem) => {
    if (item.Profile) {
      const activeRoute = navigation.getParent()?.getState();
      const currentRouteName = activeRoute?.routes[activeRoute.index].name;
      if (currentRouteName === 'ProfileStack') {
        const drawerNavigation = navigation.getParent();
        if (drawerNavigation) {
          drawerNavigation.navigate('ProfileStack', {
            screen: 'UserProfile',
            params: { profileId: item.Profile.id, fromTabPress: false },
          });
        }
      } else {
        navigation.navigate('HomeStack', {
          screen: 'OtherUserProfile',
          params: { profileId: item.Profile.id, fromTabPress: false },
        });
      }
    } else {
      navigation.navigate('HomeStack', {
        screen: 'EntityProfile',
        params: { entityProfileId: item.EntityProfile?.id },
      });
    }
  };

  const onBack = () => navigation.goBack();

  return (
    <SafeArea>
      <View className="flex-row items-center px-4">
        <BackButton onBack={onBack} label="" />
      </View>
      <Tabs activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />
      <FollowsList
        entityProfileId={entityProfileId}
        type={activeTab}
        onProfilePress={handleProfilePress}
      />
    </SafeArea>
  );
};

export default Follows;
