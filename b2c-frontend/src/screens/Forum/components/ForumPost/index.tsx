/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, Pressable, Image, Share } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Clipboard from '@react-native-clipboard/clipboard';
import LottieView from 'lottie-react-native';
import { useSelector, useDispatch } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  deleteQuestion,
  removeQuestionOptimistic,
} from '@/src/redux/slices/question/questionSlice';
import type { AppDispatch } from '@/src/redux/store';
import { renderTextWithHighlight } from '@/src/utilities/textHighlight';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import {
  useQuestionVoting,
  useQuestionLiveMode,
} from '@/src/screens/Forum/components/ForumPost/useHook';
import Bulb from '@/src/assets/svgs/Bulb';
import Comment from '@/src/assets/svgs/Comment';
import Copy from '@/src/assets/svgs/Copy';
import DownVote from '@/src/assets/svgs/DownVote';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import PathArrow from '@/src/assets/svgs/PathArrow';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import ShareIcon from '@/src/assets/svgs/Share';
import SolvedIcon from '@/src/assets/svgs/SolvedIcon';
import TrashBin from '@/src/assets/svgs/TrashBin';
import UpVote from '@/src/assets/svgs/UpVote';
import type { QuestionDeleteOnePayloadI } from '@/src/networks/question/types';
import FileViewer from '../FileViewer';
import Timer from '../Timer';
import type { ForumPostProps, PreviewIconType } from './types';
import { previewIconMap } from './utils';

const ForumPost: React.FC<{ post: ForumPostProps }> = ({ post }) => {
  const {
    postId,
    communityId,
    canModify,
    profile,
    type,
    topics,
    equipment,
    title,
    isSolved,
    description,
    previewIcons,
    answers,
    endTime,
    answerView,
    community = 'Global',
    attachments = [],
    isLive,
    isAnonymous,
    highlightText,
    onPress,
    userCommunityRole,
  } = post;

  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);

  const deleteForumQuestion = useCallback(
    async (questionId: string) => {
      try {
        const payload: QuestionDeleteOnePayloadI = { questionId };
        await dispatch(deleteQuestion(payload)).unwrap();
        dispatch(removeQuestionOptimistic({ questionId }));
        showToast({
          message: 'Question deleted successfully',
          type: 'success',
        });
        navigation.goBack();
      } catch (error) {
        showToast({
          message: 'Failed to delete question',
          type: 'error',
          description: error instanceof APIResError ? error.message : 'Unknown error',
        });
      }
    },
    [dispatch, navigation],
  );

  const {
    handleUpvote,
    handleDownvote,
    isUpvoted,
    isDownvoted,
    isLoading: voteLoading,
    upVotes,
    downVotes,
  } = useQuestionVoting(postId);

  const [optionsVisible, setOptionsVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [fileViewerVisible, setFileViewerVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pendingDeleteAction, setPendingDeleteAction] = useState(false);

  const isOwnPost = Boolean(canModify);
  const canModerateDelete =
    post.userCommunityRole === 'ADMIN' || post.userCommunityRole === 'MODERATOR';

  const handleOptions = () => setOptionsVisible(true);

  const handleCloseOptions = () => setOptionsVisible(false);

  const handleBottomSheetHide = () => {
    if (pendingDeleteAction) {
      setDeleteModalVisible(true);
      setPendingDeleteAction(false);
    }
  };

  const showDeleteConfirmation = () => {
    setPendingDeleteAction(true);
    setOptionsVisible(false);
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteForumQuestion(postId);
      setDeleteModalVisible(false);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to delete',
        description: 'There was an error deleting your post',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const cancelDelete = () => setDeleteModalVisible(false);

  const handleEdit = () => {
    handleCloseOptions();
  };

  const handleReport = () => {
    showToast({
      type: 'success',
      message: 'Reported',
      description: 'Post reported',
    });
    handleCloseOptions();
  };

  const handleCopyLink = () => {
    const postUrl = `https://network.navicater.com/forum/${postId}`;
    Clipboard.setString(postUrl);
    showToast({
      type: 'success',
      message: 'Link Copied',
      description: 'Post link copied to clipboard',
    });
    handleCloseOptions();
  };

  const handleShare = async () => {
    try {
      const shareUrl = `https://network.navicater.com/forum/${postId}`;
      const shareMessage = `🚀 Hey there!. just shared an absolutely amazing question on Navicater! 🌟 Don't miss out on this insightful content—check it out now and be inspired! 💡✨ \n\n${shareUrl}\n\n#Navicater #GreatContent`;

      const result = await Share.share({
        message: shareMessage,
      });

      if (result.action === Share.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your post has been shared 🎉',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Sharing Failed',
        description: 'Oops! Something went wrong. Try again.',
      });
    }
  };

  const chips = [...(equipment || []), ...(topics || [])];

  const { isLive: isQuestionLive, isSettingLiveMode, toggleLiveMode } = useQuestionLiveMode(postId);
  const [isLiveModalVisible, setIsLiveModalVisible] = useState(false);
  const [isUpdatingLiveMode, setIsUpdatingLiveMode] = useState(false);

  const setLiveMode = () => {
    if (isQuestionLive) {
      setIsLiveModalVisible(true);
    } else {
      setIsLiveModalVisible(true);
    }
  };

  const confirmSetLive = async () => {
    setIsLiveModalVisible(false);
    setIsUpdatingLiveMode(true);
    try {
      await toggleLiveMode(!isQuestionLive);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to toggle live mode',
        description: 'There was an error updating the live status',
      });
    } finally {
      setIsUpdatingLiveMode(false);
    }
  };

  const cancelSetLive = () => setIsLiveModalVisible(false);

  return (
    <>
      <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg">
        <Pressable onPress={onPress} disabled={!onPress}>
          <View className="px-2 flex-row gap-2 items-center">
            {type === 'troubleshooting' && equipment && (
              <View className="bg-[#F5F3FF] rounded-lg mr-2 px-2 py-1">
                <FlatList
                  horizontal
                  data={chips}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <View className="flex-row items-center">
                      <Text className="text-[#262626] text-sm font-medium">{item.label}</Text>
                      {item.id !== equipment[equipment.length - 1].id && (
                        <View className="px-2">
                          <PathArrow width={1.25} height={1.25} />
                        </View>
                      )}
                    </View>
                  )}
                />
              </View>
            )}
            {type === 'question' && topics && (
              <FlatList
                horizontal
                data={topics}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{
                  flexGrow: 1,
                }}
                renderItem={({ item }) => (
                  <View className="bg-[#DDEFC8B2]  rounded-lg mr-2 px-2 py-1">
                    <Text className="text-[#737373] text-sm font-medium">{item.label}</Text>
                  </View>
                )}
              />
            )}
            {isSolved && (
              <View className="rounded-2xl px-2 py-1 flex-row items-center gap-2">
                <SolvedIcon width={2} height={2} />
              </View>
            )}
          </View>
          <View className="px-3 py-2 flex-row items-center">
            <View style={{ flex: 1 }}>
              <Text className="text-[#262626] text-lg font-medium">
                {highlightText ? renderTextWithHighlight(title, highlightText) : title}
              </Text>
            </View>
          </View>
          {description && (
            <View className="py-3 px-3">
              <Text
                className="text-[#262626] text-base font-normal"
                numberOfLines={!answerView ? 1 : undefined}
                ellipsizeMode="tail"
              >
                {highlightText ? renderTextWithHighlight(description, highlightText) : description}
              </Text>
            </View>
          )}
        </Pressable>

        {previewIcons && previewIcons.length > 0 && (
          <View className="px-2">
            <FlatList
              horizontal
              data={previewIcons}
              contentContainerStyle={{
                flexGrow: 1,
              }}
              keyExtractor={(item, idx) => item + idx}
              renderItem={({ item }) => {
                const IconComponent = previewIconMap[item];
                return (
                  <Pressable className="p-2" onPress={() => setFileViewerVisible(true)}>
                    <IconComponent />
                  </Pressable>
                );
              }}
            />
          </View>
        )}
        <View className="flex-row justify-between items-center px-4 pt-2">
          <View className="flex-row items-center gap-5">
            <View className="flex-row items-center gap-2">
              <Pressable onPress={handleUpvote} disabled={voteLoading}>
                <UpVote isLiked={isUpvoted} />
              </Pressable>
              <Text className="text-[#262626] text-sm font-medium">{upVotes}</Text>
            </View>
            <View className="flex-row items-center gap-2">
              <Pressable onPress={handleDownvote} disabled={voteLoading}>
                <DownVote isLiked={isDownvoted} />
              </Pressable>
              <Text className="text-[#262626] text-sm font-medium">{downVotes}</Text>
            </View>
            {!answerView && (
              <View className="flex-row items-center gap-2">
                <Bulb />
                <Text className="text-[#262626] text-sm font-medium">{answers}</Text>
              </View>
            )}
            {answerView && (
              <Pressable
                className="flex-row items-center gap-2"
                onPress={() =>
                  navigation.navigate('ForumComments', { postId, type: 'FORUM_QUESTION' })
                }
              >
                <Comment color="#525252" />
                <Text className="text-[#262626] text-sm font-medium">{post.commentCount}</Text>
              </Pressable>
            )}
          </View>
          <View className="flex-row items-center gap-2">
            {isOwnPost && (
              <Pressable
                onPress={setLiveMode}
                disabled={isSettingLiveMode || isUpdatingLiveMode}
                className="flex-row items-center gap-2"
              >
                {isUpdatingLiveMode ? (
                  <View className="flex-row items-center px-8">
                    <Timer endTime={endTime} />
                  </View>
                ) : isQuestionLive ? (
                  <View className="flex-row items-center px-8">
                    <Timer endTime={endTime} />
                  </View>
                ) : (
                  <LottieView
                    source={require('@/src/assets/animations/static.json')}
                    style={{
                      width: 100,
                      height: 50,
                    }}
                  />
                )}
              </Pressable>
            )}
            {!isOwnPost && isQuestionLive && (
              <View className="flex-row items-center px-8">
                <Timer endTime={endTime} />
              </View>
            )}
            {!isOwnPost && !isQuestionLive && <></>}
            {/* </View>
          <View className="flex-row items-center"> */}
            <Pressable onPress={() => handleShare()}>
              <ShareIcon />
            </Pressable>
          </View>
        </View>
        {answerView && (
          <View className="flex-row items-center justify-between px-4 py-3">
            {profile && !isAnonymous && (
              <View className="flex-row items-center gap-2">
                <Pressable>
                  <UserAvatar
                    avatarUri={profile.avatar}
                    name={profile.name}
                    width={28}
                    height={28}
                  />
                </Pressable>
                <Text className="text-[#262626] text-base font-normal">
                  {profile.name} in <Text className="font-medium italic">{community}</Text>
                </Text>
              </View>
            )}
            {!profile && (
              <View className="flex-row items-center gap-2">
                <Pressable>
                  <UserAvatar avatarUri={null} name="Anonymous" width={28} height={28} />
                </Pressable>
                <Text className="text-[#262626] text-base font-normal">
                  Anonymous in <Text className="font-medium italic">{community}</Text>
                </Text>
              </View>
            )}
            <Pressable onPress={handleOptions}>
              <HorizontalEllipsis width={2.5} height={2.5} />
            </Pressable>
          </View>
        )}
      </View>
      <BottomSheet
        height={isOwnPost || canModerateDelete ? 230 : 200}
        visible={optionsVisible}
        onClose={handleCloseOptions}
        onModalHide={handleBottomSheetHide}
      >
        <OptionsMenu>
          {isOwnPost || canModerateDelete ? (
            <>
              <OptionItem
                icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
                label="Delete post"
                textClassName="text-red-500"
                onPress={showDeleteConfirmation}
              />
            </>
          ) : (
            <OptionItem
              icon={<ReportFlag stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Report post"
              onPress={handleReport}
            />
          )}
          <View className="h-[1px] bg-gray-200 my-2" />
          <OptionItem
            icon={<Copy stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
            label="Copy Link"
            onPress={handleCopyLink}
          />
          <View className="h-[1px] bg-gray-200 my-2" />
          {/* {isOwnPost && (
            <OptionItem
              icon={<EditPencil stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Edit post"
              onPress={handleEdit}
            />
          )} */}
        </OptionsMenu>
      </BottomSheet>
      <CustomModal
        isVisible={deleteModalVisible}
        title="Delete Post"
        description="Are you sure you want to delete this post? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isConfirming={isDeleting}
      />

      <CustomModal
        isVisible={isLiveModalVisible}
        title={`${isQuestionLive ? 'Disable' : 'Enable'} Live Mode`}
        description={
          isQuestionLive
            ? 'Are you sure you want to disable live mode for this post?'
            : 'Are you sure you want to enable live mode for this post?'
        }
        confirmText={isQuestionLive ? 'Disable Live Mode' : 'Enable Live Mode'}
        confirmButtonVariant={`${isQuestionLive ? 'danger' : 'default'}`}
        cancelText="Cancel"
        onConfirm={confirmSetLive}
        onCancel={cancelSetLive}
      />

      {fileViewerVisible && attachments.length > 0 && (
        <FileViewer
          isVisible={fileViewerVisible}
          onClose={() => setFileViewerVisible(false)}
          attachments={attachments}
        />
      )}
    </>
  );
};

export default ForumPost;
