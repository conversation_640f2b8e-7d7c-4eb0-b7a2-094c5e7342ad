import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import { BottomTabNavigationI } from '@/src/navigation/types';
// import UserAvatar from '@/src/components/UserAvatar';
// import AiBot from '@/src/assets/images/others/aibot.png';
import AddItem from '@/src/assets/svgs/AddItem';
import Search from '@/src/assets/svgs/Search';
import ExploreContained from '@/src/assets/svgs/ExploreContained';

const TopBar = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleAdd = () => {
    navigation.navigate('CommunityQuestion', { id: '' });
  };

  const handleSearch = () => {
    navigation.navigate('ForumSearch');
  }

  const handleExplore = () => {
    navigation.navigate('Explore');
  };

  const handleBackToHome = () => {
    navigation.navigate('HomeStack', { screen: 'Home', params: undefined });
  };

  return (
    <View className="flex-row justify-between items-center px-4 py-2 bg-white">
      <View className="flex-row items-center">
        <BackButton onBack={handleBackToHome} label="" />
        <Text className="text-2xl font-semibold text-black">Forum</Text>
      </View>
      <View className="flex-row items-center gap-6 mr-6">
        {/* <Pressable onPress={handleAdd} className="items-center justify-center pr-2"> */}
          {/* <AddItem height={5} width={5} /> */}
          {/* <Text className="text-xs text-black">Create</Text> */}
        {/* </Pressable> */}
        <Pressable onPress={handleExplore} className="items-center justify-center pr-2">
          <ExploreContained color="#448600" width={3} height={3} />
          <Text className="text-xs text-black">Explore</Text>
        </Pressable>

        <Pressable onPress={handleSearch} className="items-center justify-center pr-2">
          <Search color="#448600" width={3} height={3} />
          <Text className="text-xs text-black">Search</Text>
        </Pressable>
      </View>
    </View>
  );
};

export default TopBar;
