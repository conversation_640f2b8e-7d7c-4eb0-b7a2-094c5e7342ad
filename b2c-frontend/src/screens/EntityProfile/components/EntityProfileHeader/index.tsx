import { useState, useEffect, useRef } from 'react';
import type React from 'react';
import { View, Text, Pressable, ActivityIndicator, Image, Dimensions, Share } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import ReactNativeModal from 'react-native-modal';
import Clipboard from '@react-native-clipboard/clipboard';
import { useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { showToast } from '@/src/utilities/toast';
import type { HomeStackParamListI } from '@/src/navigation/types';
import LinkIcon from '@/src/assets/svgs/Attachment';
import EditPencil from '@/src/assets/svgs/EditPencil';
import ShareIcon from '@/src/assets/svgs/Share';
import Tick from '@/src/assets/svgs/Tick';
import {
  entityProfileFollowAPI,
  entityProfileUnfollowAPI,
} from '@/src/networks/entityProfile/followUnfollow';
import { EntityProfileHeaderPropsI } from './types';

const EntityProfileHeader: React.FC<EntityProfileHeaderPropsI> = ({
  isCurrentEntityProfile,
  entityProfileHeaderData,
  entityProfileId,
}) => {
  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI>>();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [foUnfoLoading, setFoUnfoLoading] = useState(false);
  const [isFollowing, setIsFollowing] = useState(entityProfileHeaderData?.isFollowing);
  const currentEntityProfile = useSelector(selectCurrentEntityProfile);
  const currentUser = useSelector(selectCurrentUser);
  const [followersCount, setFollowersCount] = useState(entityProfileHeaderData?.followersCount);

  const [isAvatarModalVisible, setIsAvatarModalVisible] = useState(false);
  const [internalAvatarModalVisible, setInternalAvatarModalVisible] = useState(false);
  const deferredAvatarModalCloseAction = useRef<(() => void) | null>(null);

  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;
  const modalImageSize = Math.min(screenWidth * 0.8, screenHeight * 0.8);

  useEffect(() => {
    if (isAvatarModalVisible) {
      setInternalAvatarModalVisible(true);
      deferredAvatarModalCloseAction.current = null;
    } else {
      setInternalAvatarModalVisible(false);
    }
  }, [isAvatarModalVisible]);

  const handleAvatarModalCloseInitiated = () => {
    deferredAvatarModalCloseAction.current = () => setIsAvatarModalVisible(false);
    setInternalAvatarModalVisible(false);
  };

  const handleAvatarModalHide = () => {
    if (deferredAvatarModalCloseAction.current) {
      deferredAvatarModalCloseAction.current();
      deferredAvatarModalCloseAction.current = null;
    }
  };

  const handleShareProfile = async () => {
    try {
      const profileUrl = `https://network.navicater.com/entity-profile/${entityProfileId}`;
      const result = await Share.share({
        message: `Check out ${entityProfileHeaderData.name}'s profile on Navicater: ${profileUrl}`,
        url: profileUrl,
        title: `${entityProfileHeaderData.name}'s Profile`,
      });
      if (result.action === Share.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your profile has been shared 🎉',
        });
      }
      handleAvatarModalCloseInitiated();
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Share Failed',
        description: 'Could not share profile.',
      });
    }
  };

  const handleCopyLink = () => {
    const profileUrl = `https://network.navicater.com/entity-profile/${entityProfileId}`;
    Clipboard.setString(profileUrl);
    showToast({
      type: 'success',
      message: 'Link Copied!',
      description: 'Company Profile link copied to clipboard.',
    });
    handleAvatarModalCloseInitiated();
  };

  const handleAvatarPress = () => {
    setIsAvatarModalVisible(true);
  };

  const handlePressFollows = (type: string) => {
    navigation.navigate('Follows', {
      entityProfileId,
      type,
    });
  };

  const handleFollowUnFollow = async (action: string) => {
    try {
      setFoUnfoLoading(true);
      if (action === 'follow') {
        await entityProfileFollowAPI({ followeeEntityProfileId: entityProfileId });
        setIsFollowing(true);
        setFollowersCount((prev) => prev + 1);
      } else {
        await entityProfileUnfollowAPI({ unfolloweeEntityProfileId: entityProfileId });
        setIsFollowing(false);
        setFollowersCount((prev) => prev - 1);
      }
    } catch (error) {
    } finally {
      setFoUnfoLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('EditEntityProfile');
  };

  const renderFollowButtons = () => {
    if (isFollowing) {
      return (
        <Pressable
          onPress={() => setIsModalVisible(true)}
          disabled={foUnfoLoading}
          className="border border-1px flex-row items-center justify-center border-[#448600] bg-white rounded-[8px] w-3/4 p-1"
        >
          <View className="p-1">
            {foUnfoLoading ? (
              <ActivityIndicator color="#448600" />
            ) : (
              <Tick color="#448600" width={1.5} height={1.5} />
            )}
          </View>
          <Text className="text-[#448600]">Following</Text>
        </Pressable>
      );
    } else {
      return (
        <Pressable
          onPress={() => handleFollowUnFollow('follow')}
          disabled={foUnfoLoading}
          className="border border-1px flex-row items-center justify-center border-[#448600] bg-white rounded-[8px] w-3/4 p-1"
        >
          <View className="p-1">
            {foUnfoLoading ? (
              <ActivityIndicator color="#448600" />
            ) : (
              <Tick color="#448600" width={1.5} height={1.5} />
            )}
          </View>
          <Text className="text-[#448600]">Follow</Text>
        </Pressable>
      );
    }
  };

  return (
    <>
      <View className="flex-row justify-between">
        <View className="flex-row gap-4 px-4">
          <View>
            <Pressable onPress={handleAvatarPress}>
              <UserAvatar
                avatarUri={entityProfileHeaderData?.avatar}
                name={entityProfileHeaderData?.name || 'Anonymous'}
                width={80}
                height={80}
                className="my-2"
              />
            </Pressable>
          </View>
          <View className="mt-2 gap-2">
            <Text className="text-xl font-medium" numberOfLines={1}>
              {entityProfileHeaderData?.name}
            </Text>
            <Text numberOfLines={1} className="text-subLabelGray">
              {entityProfileHeaderData?.entityType}
            </Text>
            <View className="flex-row gap-4">
              <Pressable onPress={() => handlePressFollows('followers')}>
                <Text className="text-[#448600] font-inter font-medium text-md">
                  {followersCount} followers
                </Text>
              </Pressable>
              <Pressable onPress={() => handlePressFollows('following')}>
                <Text className="text-[#448600] font-inter font-medium text-md">
                  {entityProfileHeaderData?.followingsCount} following
                </Text>
              </Pressable>
            </View>
            {!isCurrentEntityProfile && renderFollowButtons()}
          </View>
        </View>
        {entityProfileId === currentEntityProfile.entityProfileId &&
          currentEntityProfile.role.toLowerCase() === 'admin' && (
            <Pressable className="pr-5" onPress={handleEdit}>
              <EditPencil />
            </Pressable>
          )}
      </View>
      <ReactNativeModal
        isVisible={internalAvatarModalVisible}
        animationIn="fadeIn"
        animationOut="fadeOut"
        onBackdropPress={handleAvatarModalCloseInitiated}
        onBackButtonPress={handleAvatarModalCloseInitiated}
        onModalHide={handleAvatarModalHide}
        backdropOpacity={0.9}
        backdropColor="black"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        statusBarTranslucent
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard
        style={{ margin: 0 }}
      >
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {entityProfileHeaderData?.avatar ? (
            <Image
              source={{ uri: entityProfileHeaderData.avatar }}
              style={{
                width: modalImageSize,
                height: modalImageSize,
                borderRadius: modalImageSize / 2,
              }}
              resizeMode="cover"
            />
          ) : (
            <UserAvatar
              avatarUri={null}
              name={entityProfileHeaderData?.name || 'Anonymous'}
              width={modalImageSize}
              height={modalImageSize}
              className="rounded-full"
            />
          )}
          <View className="flex flex-row gap-12 mt-10">
            <Pressable className="items-center gap-2" onPress={handleShareProfile}>
              <View className="w-14 h-14 rounded-full bg-white justify-center items-center">
                <ShareIcon color="black" width={2} height={2} />
              </View>
              <Text className="text-white text-xs">Share</Text>
            </Pressable>
            <Pressable className="items-center gap-2" onPress={handleCopyLink}>
              <View className="w-14 h-14 rounded-full bg-white justify-center items-center">
                <LinkIcon color="black" width={2.8} height={2.8} />
              </View>
              <Text className="text-white text-xs">Copy link</Text>
            </Pressable>
          </View>
        </View>
      </ReactNativeModal>
      <CustomModal
        isVisible={isModalVisible}
        title="Confirm Action"
        description={'Are you sure you want to unfollow?'}
        cancelText="Cancel"
        confirmText={'Unfollow'}
        onConfirm={() => handleFollowUnFollow('unfollow')}
        confirmButtonVariant="danger"
        isConfirming={foUnfoLoading}
        onCancel={() => setIsModalVisible(false)}
      />
    </>
  );
};

export default EntityProfileHeader;
