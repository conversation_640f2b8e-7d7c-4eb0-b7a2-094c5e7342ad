import { useState, useRef, useEffect } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  RefreshControl,
  View,
  Share as RNShare,
} from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet';
import NotFound from '@/src/components/NotFound';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import Tabs from '@/src/components/Tabs';
import UserPost from '@/src/components/UserPost';
import {
  selectEntityProfileActiveTab,
  SelectLastVisitedEntityProfileId,
} from '@/src/redux/selectors/entityProfile';
import {
  resetEntityProfileUI,
  setEntityProfileActiveTab,
  setLastVisitedEntityProfileId,
} from '@/src/redux/slices/entityprofile/entityProfileUISlice';
import { EntityProfileTabId } from '@/src/redux/slices/entityprofile/types';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Share from '@/src/assets/svgs/Share';
import type { PostExternalClientI } from '@/src/networks/content/types';
import EntityProfileAboutScreen from '../EntityProfileAbout';
import EntityProfileHeader from '../EntityProfileHeader';
import { EntityProfileContentPropsI } from './types';
import useEntityProfileContent from './useHook';

const INITIAL_NUM_TO_RENDER = 5;
const END_REACHED_THRESHOLD = 0.8;
const WINDOW_SIZE = 5;

const ListFooter = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const EntityProfileContent = ({
  isCurrentEntityProfile,
  entityProfileAboutData,
  entityProfileHeaderData,
  entityProfileId,
}: EntityProfileContentPropsI) => {
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const flatListRef = useRef<FlatList>(null);
  const activeTab = useSelector(selectEntityProfileActiveTab);
  const lastVisitedEntityProfileId = useSelector(SelectLastVisitedEntityProfileId);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const route = useRoute();

  const {
    entityProfilePosts,
    refreshing,
    loading,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
  } = useEntityProfileContent(entityProfileAboutData, entityProfileId);

  useEffect(() => {
    if (!entityProfileAboutData?.entityProfileId) return;

    if (entityProfileAboutData.entityProfileId !== lastVisitedEntityProfileId) {
      dispatch(resetEntityProfileUI());
      dispatch(setEntityProfileActiveTab('about'));
    }

    dispatch(setLastVisitedEntityProfileId(entityProfileAboutData.entityProfileId));
  }, [lastVisitedEntityProfileId, dispatch]);

  const tabs = [
    { id: 'about', label: 'About' },
    { id: 'posts', label: 'Posts' },
  ];

  const handleTabChange = (tabId: EntityProfileTabId) => {
    dispatch(setEntityProfileActiveTab(tabId));
  };

  const handleBack = () => bottomTabNavigation.goBack();

  const handleOpenOptions = () => setIsBottomSheetOpen(true);

  const handleCloseOptions = () => setIsBottomSheetOpen(false);

  // const handleSettings = () => {
  //   setIsBottomSheetOpen(false);
  //   drawerNavigation.navigate('ProfileStack', { screen: 'UserSettings' });
  // };

  const handleShareProfile = async () => {
    try {
      const profileUrl = `https://network.navicater.com/entity-profile/${entityProfileId}`;
      const result = await RNShare.share({
        message: `Check out ${entityProfileHeaderData.name}'s profile on Navicater: ${profileUrl}`,
        url: profileUrl,
        title: `${entityProfileHeaderData.name}'s Profile`,
      });
      if (result.action === RNShare.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your company profile has been shared 🎉',
        });
      }
      setIsBottomSheetOpen(false);
    } catch (error) {
      handleError(error);
    }
  };

  const handleEndReached = () => {
    if (!loading && !refreshing) {
      handleLoadMore();
    }
  };

  const navigateToLikesScreen = (params: { postId: string }) => {
    const stack = route.name === 'UserProfile' ? 'ProfileStack' : 'HomeStack';
    if (stack === 'ProfileStack') {
      drawerNavigation.navigate('ProfileStack', {
        screen: 'Likes',
        params: { ...params, type: 'USER_POST' },
      });
    } else {
      bottomTabNavigation.navigate('HomeStack', {
        screen: 'Likes',
        params: { ...params, type: 'USER_POST' },
      });
    }
  };

  const navigateToCommentsScreen = (params: { postId: string }) => {
    const stack = route.name === 'UserProfile' ? 'ProfileStack' : 'HomeStack';
    if (stack === 'ProfileStack') {
      drawerNavigation.navigate('ProfileStack', { screen: 'Comment', params });
    } else {
      bottomTabNavigation.navigate('HomeStack', { screen: 'Comment', params });
    }
  };

  const renderItem = ({ item, index }: { item: PostExternalClientI; index: number }) => (
    <UserPost
      post={item}
      onLikePress={() => handleLikePost(item, 'LIKE')}
      onDeletePress={() => handleDeletePost(item)}
      //   isOwnPost={item.Profile.id === data.entityProfileId}

      onEditPress={() => {
        bottomTabNavigation.navigate('CreateStack', {
          screen: 'CreateContent',
          params: { postId: item.id, editing: true, type: 'USER_POST' },
        });
      }}
      onLikeCountPress={() => navigateToLikesScreen({ postId: item.id })}
      onCommentPress={() => navigateToCommentsScreen({ postId: item.id })}
      parentScrollRef={flatListRef}
      postIndex={index}
    />
  );

  const renderTabContent = () => {
    if (activeTab === 'posts') {
      return (
        <View className="flex-1">
          {!entityProfilePosts?.length ? (
            <NotFound title="Why so quiet?" subtitle="Create some amazing posts and network" />
          ) : (
            <FlatList
              ref={flatListRef}
              data={entityProfilePosts}
              renderItem={renderItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              className="flex-1"
              contentContainerStyle={{
                flexGrow: 1,
                backgroundColor: 'white',
                paddingBottom: 20,
              }}
              initialNumToRender={INITIAL_NUM_TO_RENDER}
              maxToRenderPerBatch={INITIAL_NUM_TO_RENDER}
              windowSize={WINDOW_SIZE}
              removeClippedSubviews
              refreshControl={
                <RefreshControl enabled={true} refreshing={refreshing} onRefresh={handleRefresh} />
              }
              onEndReached={handleEndReached}
              onEndReachedThreshold={END_REACHED_THRESHOLD}
              ListFooterComponent={<ListFooter isLoading={loading} />}
            />
          )}
        </View>
      );
    }

    if (activeTab === 'about') {
      return (
        <EntityProfileAboutScreen
          isCurrentEntityProfile={isCurrentEntityProfile}
          entityProfileAboutData={entityProfileAboutData}
        />
      );
    }
  };

  return (
    <>
      <BottomSheet
        visible={isBottomSheetOpen}
        onModalHide={() => {}}
        onClose={handleCloseOptions}
        height={isCurrentEntityProfile ? 200 : 250}
      >
        <OptionsMenu>
          <OptionItem icon={<Share />} label="Share Profile" onPress={handleShareProfile} />
          {/* {isCurrentEntityProfile && (
            <OptionItem
              icon={<Settings width={2} height={2} />}
              label="Settings"
              onPress={handleSettings}
            />
          )} */}
        </OptionsMenu>
      </BottomSheet>
      <View className="flex-1">
        <View className="justify-between flex-row items-center px-4">
          <BackButton onBack={handleBack} label="" />
          <Pressable onPress={handleOpenOptions}>
            <HorizontalEllipsis />
          </Pressable>
        </View>
        <EntityProfileHeader
          entityProfileHeaderData={entityProfileHeaderData}
          isCurrentEntityProfile={isCurrentEntityProfile}
          entityProfileId={entityProfileId}
        />
        <View className="mt-6">
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={(value) => {
              if (typeof value === 'string' && ['about', 'posts'].includes(value)) {
                handleTabChange(value as EntityProfileTabId);
              }
            }}
          />
        </View>
        {renderTabContent()}
      </View>
    </>
  );
};

export default EntityProfileContent;
