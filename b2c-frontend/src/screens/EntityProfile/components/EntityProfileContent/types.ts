/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { PostExternalClientI } from '@/src/networks/content/types';
import { entityProfileFetchAboutResultI } from '@/src/networks/entityProfile/types';

export type EntityProfileHeaderData = Pick<
  entityProfileFetchAboutResultI,
  | 'entityProfileId'
  | 'avatar'
  | 'name'
  | 'isFollowing'
  | 'followersCount'
  | 'followingsCount'
  | 'entityType'
>;
export type EntityProfileAboutData = Pick<
  entityProfileFetchAboutResultI,
  'entityProfileId' | 'description' | 'foundedAt' | 'overview' | 'website' | 'entityType'
>;

export type EntityProfileContentPropsI = {
  isCurrentEntityProfile: boolean;
  entityProfileAboutData: EntityProfileAboutData;
  entityProfileHeaderData: EntityProfileHeaderData;
  entityProfileId: string;
};

export type AboutProfileI = {
  profileId: string;
  username: string;
  name: string;
  avatar: string;
  designationText: string;
  designationAlternativeId: string;
  designationRawDataId: null;
  entityText: string;
  entityId: string;
  entityRawDataId: null;
  followersCount: number;
  followingsCount: number;
  description: null;
  isFollowing: boolean;
  request: Request;
  institutions: null;
  identities: null;
  statutoryCerts: null;
  mutuals: { avatar: string; name: string }[];
  maritimeSkills: null;
};

export interface UseProfileContentResult {
  entityProfilePosts: PostExternalClientI[];
  refreshing: boolean;
  loading: boolean;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleLikePost: (post: PostExternalClientI, reactionType?: string) => Promise<void>;
  handleDeletePost: (post: PostExternalClientI) => Promise<void>;
}
