/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  addReactionOptimistc,
  deletePostOptimistic,
  removeReactionOptimistic,
  revertDeletePostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { deletePostAPI } from '@/src/networks/content/post';
import { upsertReactionAPI, deleteReactionAPI } from '@/src/networks/content/reaction';
import { PostExternalClientI } from '@/src/networks/content/types';
import { fetchEntityProfilePostsAPI } from '@/src/networks/entityProfile/fetchEntityProfilePosts';
import { EntityProfileAboutData, UseProfileContentResult } from './types';

const PAGE_SIZE = 10;

const useEntityProfileContent = (
  entityProfileAboutdata: EntityProfileAboutData,
  entityProfileId: string,
): UseProfileContentResult => {
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  const dispatch = useDispatch<AppDispatch>();

  const currentUser = useSelector(selectCurrentUser);

  const [entityProfilePosts, setEntityProfilePosts] = useState<PostExternalClientI[]>([]);

  const fetchPosts = async (refresh = false) => {
    try {
      const query = {
        entityProfileId,
        cursorId: refresh ? null : cursorId,
        pageSize: PAGE_SIZE,
      };
      const data = await fetchEntityProfilePostsAPI(query);
      if (refresh) {
        setEntityProfilePosts(data.posts);
      } else {
        setEntityProfilePosts((prev) => [...prev, ...data.posts]);
      }
      setEntityProfilePosts(data.posts);
      setCursorId(data.cursorId);
      setHasMore(data.posts.length === PAGE_SIZE);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to load posts',
      });
      if (!entityProfilePosts) {
        triggerErrorBoundary(new Error('Failed to load profile posts'));
      }
      throw error;
    }
  };

  useEffect(() => {
    const loadInitialPosts = async () => {
      if (!entityProfileId) return;

      setLoading(true);
      try {
        await fetchPosts(true);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    loadInitialPosts();
  }, [entityProfileId]);

  const handleRefresh = async () => {
    if (refreshing) return;

    setRefreshing(true);
    try {
      await fetchPosts(true);
    } catch (error) {
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!hasMore || loading || refreshing || !cursorId) {
      return;
    }

    setLoading(true);
    try {
      await fetchPosts(false);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleLikePost = async (post: PostExternalClientI, reactionType = 'LIKE') => {
    if (!currentUser || !post?.id) return;

    const currentPost = entityProfilePosts.find((p) => p.id === post.id);
    const wasLiked = currentPost?.isLiked ?? post.isLiked;

    try {
      if (wasLiked) {
        dispatch(removeReactionOptimistic({ postId: post.id }));
        setEntityProfilePosts((prev) =>
          prev.map((p) =>
            p.id === post.id
              ? { ...p, isLiked: false, reactionsCount: Math.max(0, (p.reactionsCount || 1) - 1) }
              : p,
          ),
        );
        await deleteReactionAPI({ postId: post.id });
      } else {
        dispatch(addReactionOptimistc({ postId: post.id }));
        setEntityProfilePosts((prev) =>
          prev.map((p) =>
            p.id === post.id
              ? { ...p, isLiked: true, reactionsCount: (p.reactionsCount || 0) + 1 }
              : p,
          ),
        );
        await upsertReactionAPI({ postId: post.id, reactionType });
      }
    } catch (error) {
      if (wasLiked) {
        dispatch(addReactionOptimistc({ postId: post.id }));
        setEntityProfilePosts((prev) =>
          prev.map((p) =>
            p.id === post.id
              ? { ...p, isLiked: true, reactionsCount: (p.reactionsCount || 0) + 1 }
              : p,
          ),
        );
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to unlike post',
        });
      } else {
        dispatch(removeReactionOptimistic({ postId: post.id }));
        setEntityProfilePosts((prev) =>
          prev.map((p) =>
            p.id === post.id
              ? { ...p, isLiked: false, reactionsCount: Math.max(0, (p.reactionsCount || 1) - 1) }
              : p,
          ),
        );
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to like post',
        });
      }
    }
  };

  const handleDeletePost = async (post: PostExternalClientI) => {
    if (!post?.id) return;

    try {
      dispatch(deletePostOptimistic({ post }));
      await deletePostAPI(post.id);
    } catch (error) {
      dispatch(revertDeletePostOptimistic({ postId: post.id }));
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to delete post',
      });
    }
  };

  return {
    entityProfilePosts,
    refreshing,
    loading,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
  };
};

export default useEntityProfileContent;
