import { ScrollView, Text, View } from 'react-native';
import TextView from '@/src/components/TextView';
import { formatToMonthYear } from '@/src/utilities/datetime';
import { EntityProfilePropsI } from './types';

const EntityProfileAboutScreen = ({
  isCurrentEntityProfile,
  entityProfileAboutData,
}: EntityProfilePropsI) => {
  const renderDetail = (detail: string | null) => {
    if (detail && detail.length) {
      return <Text className="font-inter font-normal text-base leading-[22px]">{detail}</Text>;
    } else {
      return (
        <Text>{isCurrentEntityProfile ? 'Add this detail by editing your profile' : 'nil'}</Text>
      );
    }
  };

  return (
    <ScrollView
      contentContainerStyle={{ flexGrow: 1, paddingBottom: 10 }}
      showsVerticalScrollIndicator={false}
      className="px-4"
    >
      <View className="mt-3">{renderDetail(entityProfileAboutData?.description)}</View>
      <View className="h-2 bg-gray-100 mx-[-16px] my-4" />
      <TextView title="Overview" titleClassName="font-medium text-lg text-black" />
      {renderDetail(entityProfileAboutData?.overview)}
      <View className="h-2 bg-gray-100 mx-[-16px] my-4" />
      <TextView title="Website" titleClassName="font-medium text-lg text-black" />
      {renderDetail(entityProfileAboutData?.website)}
      <TextView title="Founded" titleClassName="font-medium text-lg text-black" />
      {renderDetail(
        formatToMonthYear(
          entityProfileAboutData?.foundedAt ? entityProfileAboutData?.foundedAt : '',
        ),
      )}
      <TextView title="Type" titleClassName="font-medium text-lg text-black" />
      {renderDetail(entityProfileAboutData?.entityType)}
    </ScrollView>
  );
};

export default EntityProfileAboutScreen;
