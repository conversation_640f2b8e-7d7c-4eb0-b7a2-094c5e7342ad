/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useCallback, useState } from 'react';
import { RouteProp, useFocusEffect, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setSelfEntityProfileData } from '@/src/redux/slices/entityprofile/entityProfileSlice';
import { AppDispatch } from '@/src/redux/store';
import { HomeStackParamListI } from '@/src/navigation/types';
import { entityProfileFetchAboutAPI } from '@/src/networks/entityProfile/entityProfileAbout';
import ProfileSkeleton from '../UserProfile/components/ProfileSkeleton';
import EntityProfileContent from './components/EntityProfileContent';
import {
  EntityProfileAboutData,
  EntityProfileHeaderData,
} from './components/EntityProfileContent/types';

const EntityProfile = () => {
  const route = useRoute<RouteProp<HomeStackParamListI, 'EntityProfile'>>();
  const entityProfileId = route.params.entityProfileId;
  const dispatch = useDispatch<AppDispatch>();
  const selfEntityProfile = useSelector(selectCurrentEntityProfile);
  const resolvedEntityProfileId = entityProfileId
    ? entityProfileId
    : selfEntityProfile.entityProfileId;
  const [entityProfileHeaderData, setEntityProfileHeaderData] =
    useState<EntityProfileHeaderData | null>();
  const [entityProfileAboutData, setEntityProfileAboutData] =
    useState<EntityProfileAboutData | null>(null);
  const [loading, setLoading] = useState(false);
  const currentUser = useSelector(selectCurrentUser);

  const isCurrentEntityProfile = selfEntityProfile.entityProfileId === resolvedEntityProfileId;

  useFocusEffect(
    useCallback(() => {
      const fetchAbout = async () => {
        try {
          setLoading(true);
          const data = await entityProfileFetchAboutAPI({
            entityProfileId: resolvedEntityProfileId,
          });
          const headerData = {
            entityProfileId: data.entityProfileId,
            name: data.name,
            avatar: data.avatar,
            isFollowing: data.isFollowing,
            followersCount: data.followersCount,
            followingsCount: data.followingsCount,
            entityType: data.entityType,
          };
          const aboutData = {
            overview: data.overview,
            foundedAt: data.foundedAt,
            website: data.website,
            entityProfileId: data.entityProfileId,
            description: data.description,
            entityType: data.entityType,
          };
          setEntityProfileHeaderData(headerData);
          setEntityProfileAboutData(aboutData);
          if (isCurrentEntityProfile) {
            dispatch(setSelfEntityProfileData(data));
          }
        } catch (e) {
        } finally {
          setLoading(false);
        }
      };
      fetchAbout();
    }, [resolvedEntityProfileId]),
  );

  return (
    <SafeArea>
      {loading ? (
        <ProfileSkeleton />
      ) : (
        <EntityProfileContent
          isCurrentEntityProfile={isCurrentEntityProfile}
          entityProfileHeaderData={entityProfileHeaderData!}
          entityProfileAboutData={entityProfileAboutData!}
          entityProfileId={resolvedEntityProfileId}
        />
      )}
    </SafeArea>
  );
};

export default EntityProfile;
