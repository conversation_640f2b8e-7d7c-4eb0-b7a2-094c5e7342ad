import { useRef, useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { FlatList, RefreshControl } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import type { PostExternalClientI } from '@/src/networks/content/types';
import Skeleton from '../PostListSkeleton';
import type { PostListProps, ListFooterProps } from './types';
import usePostList from './useHook';

const INITIAL_NUM_TO_RENDER = 5;
const END_REACHED_THRESHOLD = 0.5;
const WINDOW_SIZE = 5;

const ListFooter = ({ isLoading }: ListFooterProps) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const PostList = ({
  posts: externalPosts,
  refreshing: externalRefreshing,
  loading: externalLoading,
  onRefresh: externalOnRefresh,
  onLoadMore: externalOnLoadMore,
  setScrollToTop,
  setHandleRefresh,
}: PostListProps) => {
  const {
    posts: fetchedPosts,
    refreshing: fetchedRefreshing,
    loading: fetchedLoading,
    handleRefresh: fetchedOnRefresh,
    handleLoadMore: fetchedOnLoadMore,
    handleLikePost,
    handleDeletePost,
  } = usePostList();
  const currentUser = useSelector(selectCurrentUser);
  const flatListRef = useRef<FlatList>(null);

  const data = externalPosts ?? fetchedPosts;
  const refreshing = externalRefreshing ?? fetchedRefreshing;
  const loading = externalLoading ?? fetchedLoading;
  const onRefresh = externalOnRefresh ?? fetchedOnRefresh;
  const onLoadMore = externalOnLoadMore ?? fetchedOnLoadMore;

  const [firstLoading, setFirstLoading] = useState(false);

  if (fetchedLoading && !firstLoading) {
    setFirstLoading(true);
  }

  const scrollToTop = () => {
    flatListRef.current?.scrollToOffset?.({ offset: 0, animated: true });
  };

  useEffect(() => {
    if (setScrollToTop) {
      setScrollToTop(scrollToTop);
    }
  }, [setScrollToTop]);

  useEffect(() => {
    if (setHandleRefresh) {
      setHandleRefresh(() => {
        fetchedOnRefresh();
      });
    }
  }, [setHandleRefresh, fetchedOnRefresh]);

  const handleEndReached = () => {
    if (!loading && !refreshing && onLoadMore) {
      onLoadMore();
    }
  };

  const navigation = useNavigation<BottomTabNavigationI>();

  const renderItem = ({ item, index }: { item: PostExternalClientI; index: number }) => (
    <UserPost
      post={item}
      onLikePress={() => handleLikePost(item, 'LIKE')}
      onDeletePress={() => handleDeletePost(item)}
      isOwnPost={currentUser?.profileId === item.Profile?.id}
      onLikeCountPress={() =>
        navigation.navigate('HomeStack', {
          screen: 'Likes',
          params: { postId: item.id, type: 'USER_POST' },
        })
      }
      onEditPress={() => {
        navigation.navigate('CreateStack', {
          screen: 'CreateContent',
          params: { postId: item.id, editing: true, type: 'USER_POST' },
        });
      }}
      onCommentPress={() =>
        navigation.navigate('HomeStack', {
          screen: 'Comment',
          params: { postId: item.id, type: 'USER_POST' },
        })
      }
      parentScrollRef={flatListRef}
      postIndex={index}
    />
  );

  const keyExtractor = (item: PostExternalClientI) => item.id;

  if ((fetchedLoading && !data?.length && !externalPosts) || !firstLoading) {
    return <Skeleton />;
  }

  if (!data?.length && firstLoading) {
    return (
      <NotFound
        title="No posts found"
        subtitle="There are no posts to display at the moment. Check back later or create a new post."
      />
    );
  }

  return (
    <View className="flex-1">
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderItem}
        scrollEnabled={true}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        initialNumToRender={INITIAL_NUM_TO_RENDER}
        maxToRenderPerBatch={INITIAL_NUM_TO_RENDER}
        windowSize={WINDOW_SIZE}
        removeClippedSubviews
        refreshControl={
          <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingBottom: 20,
        }}
        onEndReached={handleEndReached}
        onEndReachedThreshold={END_REACHED_THRESHOLD}
        ListFooterComponent={<ListFooter isLoading={loading} />}
      />
    </View>
  );
};

export default PostList;
