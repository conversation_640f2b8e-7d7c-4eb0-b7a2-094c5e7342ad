// src/screens/ExploreQna/index.tsx
import { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, FlatList, Pressable, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { fetchTopicsAPI } from '@/src/networks/forum/topics';
import { TopicI } from '@/src/networks/forum/types';

type GroupedTopicsI = { [key: string]: TopicI[] };

const ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
const PAGE_SIZE = 10;

type FlatRow = { type: 'header'; letter: string } | { type: 'item'; topic: TopicI };

function buildFlatData(letters: string[], groups: GroupedTopicsI): FlatRow[] {
  const data: FlatRow[] = [];
  letters.forEach((letter) => {
    data.push({ type: 'header', letter });
    groups[letter].forEach((topic) => data.push({ type: 'item', topic }));
  });
  return data;
}

const ExploreQnaScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const listRef = useRef<FlatList<any>>(null);

  const [topics, setTopics] = useState<TopicI[]>([]);
  const [page, setPage] = useState(0);
  const [total, setTotal] = useState<number | null>(null);
  const [hasMore, setHasMore] = useState(true);

  const [initialLoading, setInitialLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const loadPage = async (nextPage: number, append: boolean) => {
    try {
      append ? setLoadingMore(true) : setInitialLoading(true);
      setError(null);

      const res = await fetchTopicsAPI({ page: nextPage, pageSize: PAGE_SIZE });

      const incoming = res.data ?? [];
      if (typeof res.total === 'number') setTotal(res.total);

      setTopics((prev) => {
        const base = append ? prev : [];
        const seen = new Set(base.map((t) => String(t.id)));
        const merged = [...base];
        for (const t of incoming) {
          const id = String(t.id);
          if (!seen.has(id)) {
            seen.add(id);
            merged.push(t);
          }
        }
        return merged;
      });

      const totalSoFar = (append ? topics.length : 0) + incoming.length;
      const noMore =
        (typeof res.total === 'number' && totalSoFar >= res.total) || incoming.length === 0;
      setHasMore(!noMore);
      if (!noMore) setPage(nextPage);
    } catch (e) {
      setError('Failed to load topics. Please try again.');
    } finally {
      append ? setLoadingMore(false) : setInitialLoading(false);
    }
  };

  useEffect(() => {
    loadPage(0, false);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPage(0, false);
    setRefreshing(false);
  };

  const onEndReached = () => {
    if (initialLoading || loadingMore || !hasMore) return;
    loadPage(page + 1, true);
  };

  const groupedTopics = useMemo(() => {
    const grouped: GroupedTopicsI = {};
    const sorted = [...topics].sort((a, b) => a.name.localeCompare(b.name));
    sorted.forEach((t) => {
      const ch = t.name.charAt(0).toUpperCase();
      if (!grouped[ch]) grouped[ch] = [];
      grouped[ch].push(t);
    });
    return grouped;
  }, [topics]);

  const availableLetters = useMemo(() => Object.keys(groupedTopics).sort(), [groupedTopics]);

  const handleLetterPress = (letter: string) => {
    setSelectedLetter(letter);
    const flatData = buildFlatData(availableLetters, groupedTopics);
    const idx = flatData.findIndex((i) => i.type === 'header' && i.letter === letter);
    if (idx >= 0) {
      listRef.current?.scrollToIndex({ index: idx, animated: true });
    }
  };

  const handleBack = () => navigation.goBack();

  const handleTopicPress = async (topic: TopicI) => {
    navigation.navigate('ExploreForum', {
      type: 'NORMAL',
      topicId: topic.id,
      topicDataType: topic.dataType,
    });
  };

  const renderTopicItem = ({ item }: { item: TopicI }) => (
    <Pressable
      className="py-4 px-5 border-b border-gray-100"
      onPress={() => handleTopicPress(item)}
    >
      <Text className="text-lg font-medium text-gray-900">{item.name}</Text>
      <Text className="text-sm text-gray-500 mt-1">
        {item.dataType === 'master' ? 'Verified' : 'Community'}
      </Text>
    </Pressable>
  );

  const renderAlphabetSidebar = () => (
    <View className="absolute right-2 top-20 bottom-20 justify-center">
      <View className="bg-white rounded-lg shadow-sm py-2 px-1">
        {ALPHABET.map((letter) => {
          const enabled = availableLetters.includes(letter);
          const active = selectedLetter === letter;
          return (
            <Pressable
              key={letter}
              onPress={() => handleLetterPress(letter)}
              className={`py-1 px-2 rounded ${enabled ? (active ? 'bg-primaryGreen' : 'bg-transparent') : 'opacity-30'}`}
              disabled={!enabled}
            >
              <Text
                className={`text-xs font-medium text-center ${active ? 'text-white' : 'text-gray-700'}`}
              >
                {letter}
              </Text>
            </Pressable>
          );
        })}
      </View>
    </View>
  );

  const flatListData = useMemo(
    () => buildFlatData(availableLetters, groupedTopics),
    [availableLetters, groupedTopics],
  );

  const renderFlatListItem = ({ item }: { item: FlatRow }) => {
    if (item.type === 'header') {
      return (
        <View className="bg-gray-50 py-3 px-5">
          <Text className="text-lg font-bold text-gray-700">{item.letter}</Text>
        </View>
      );
    }
    return renderTopicItem({ item: item.topic });
  };

  if (initialLoading && topics.length === 0) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#10B981" />
          <Text className="mt-4 text-gray-600">Loading topics...</Text>
        </View>
      </SafeArea>
    );
  }

  if (error && topics.length === 0) {
    return (
      <SafeArea>
        <View className="flex-1 bg-white">
          <View className="flex-row items-center px-4 py-2 border-b border-gray-200">
            <BackButton onBack={handleBack} label="" />
            <Text className="text-2xl font-semibold text-black ml-2">Explore QnA</Text>
          </View>
          <View className="flex-1 justify-center items-center px-4">
            <Text className="text-red-500 text-center mb-4">{error}</Text>
            <Pressable
              onPress={() => loadPage(0, false)}
              className="bg-primaryGreen px-6 py-3 rounded-lg"
            >
              <Text className="text-white font-medium">Retry</Text>
            </Pressable>
          </View>
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="flex-row items-center px-4 py-2 border-b border-gray-200">
          <BackButton onBack={handleBack} label="" />
          <Text className="text-2xl font-semibold text-black ml-2">Explore QnA</Text>
        </View>

        <View className="flex-1 mb-20">
          <FlatList
            ref={listRef}
            data={flatListData}
            renderItem={renderFlatListItem}
            keyExtractor={(item, index) =>
              item.type === 'header' ? `header-${item.letter}` : `topic-${item.topic.id}-${index}`
            }
            contentContainerStyle={{ paddingBottom: 20, marginBottom: 20 }}
            showsVerticalScrollIndicator={false}
            refreshing={refreshing}
            onRefresh={onRefresh}
            onEndReached={onEndReached}
            onEndReachedThreshold={0.4}
            ListFooterComponent={
              loadingMore ? (
                <View className="py-4 items-center">
                  <ActivityIndicator />
                </View>
              ) : !hasMore ? (
                <View className="py-4 items-center">
                  <Text className="text-gray-400">No more topics</Text>
                </View>
              ) : null
            }
          />

          {renderAlphabetSidebar()}
        </View>
      </View>
    </SafeArea>
  );
};

export default ExploreQnaScreen;
