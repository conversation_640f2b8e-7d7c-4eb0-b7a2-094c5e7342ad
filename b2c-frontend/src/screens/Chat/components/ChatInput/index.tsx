import {
  Animated,
  Pressable,
  Text,
  View,
  TextInput,
  ActivityIndicator,
  Platform,
  Keyboard,
  Alert,
} from 'react-native';
import { useRef, useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Attachment from '@/src/assets/svgs/Attachment';
import Microphone from '@/src/assets/svgs/Microphone';
import Send from '@/src/assets/svgs/Send';
import ReplyPreview from '../ReplyPreview';
import type { ChatInputProps } from './types';

const ChatInput = ({
  isSelectionMode,
  replyPreview,
  handleCloseReply,
  mediaPreview,
  renderMediaPreview,
  isRecording,
  recordingTime,
  handleMicrophonePress,
  recordedAudioFile,
  renderAudioPreview,
  textInputRef,
  messageText,
  handleTextInputChange,
  isTextInputDisabled,
  handleSendMessage,
  isSendButtonDisabled,
  isAudioUploading,
  handleAttachment,
  handleAttachmentWithVoice,
  isAttachmentDisabled,
  isMicrophoneDisabled,
}: ChatInputProps) => {
  const insets = useSafeAreaInsets();
  const hasContent = messageText.trim().length > 0 || mediaPreview.length > 0 || recordedAudioFile;

  const handlePlusPress = () => {
    handleAttachmentWithVoice();
  };

  return (
    <>
      {!isSelectionMode && (
        <View
          className="border-t border-gray-200 bg-white"
          style={{
            paddingVertical: 12,
            paddingBottom: Platform.OS === 'android' ? 12 : Math.max(insets.bottom || 0, 12),
          }}
        >
          {replyPreview && <ReplyPreview message={replyPreview} onClose={handleCloseReply} />}
          {mediaPreview.length > 0 && (
            <View className="px-4 pb-3">
              {renderMediaPreview()}
              <Text className="text-sm text-gray-500 mb-2">Add a caption (optional)</Text>
            </View>
          )}
          {isRecording ? (
            <View className="px-4 mb-3">
              <View className="flex-row items-center justify-between bg-gray-100 p-3 rounded-lg">
                <View className="flex-row items-center gap-2">
                  <View className="h-3 w-3 rounded-full bg-gray-500" />
                  <Text className="text-gray-700 font-medium">Recording {recordingTime}</Text>
                </View>
                <Pressable
                  className="bg-gray-500 rounded-full px-3 py-1"
                  onPress={handleMicrophonePress}
                >
                  <Text className="text-white font-bold text-xs">STOP</Text>
                </Pressable>
              </View>
            </View>
          ) : recordedAudioFile ? (
            renderAudioPreview()
          ) : null}

          <View className="flex-row items-end px-4 gap-3">
            {hasContent ? (
              <Pressable
                onPress={handlePlusPress}
                className="w-11 h-11 rounded-full items-center justify-center bg-gray-200"
                style={{ marginBottom: 0 }}
              >
                <Text className="text-gray-600 text-lg font-bold">+</Text>
              </Pressable>
            ) : (
              <Pressable
                onPress={handleAttachment}
                disabled={isAttachmentDisabled}
                className="w-11 h-11 rounded-full items-center justify-center bg-gray-200"
                style={{ marginBottom: 0 }}
              >
                <Attachment color="#666666" width={3} height={3} />
              </Pressable>
            )}

            <View className="flex-1 relative border rounded-2xl bg-[#F3ECEC] border-[#DEDEDE] px-4">
              <TextInput
                ref={textInputRef}
                value={messageText}
                onChangeText={handleTextInputChange}
                placeholder={
                  mediaPreview.length > 0 || recordedAudioFile
                    ? 'Add a caption...'
                    : replyPreview
                      ? replyPreview.isEditing
                        ? 'Edit message...'
                        : 'Reply to message...'
                      : 'Type a message'
                }
                placeholderTextColor="#9CA3AF"
                className="text-sm text-gray-900 py-3 min-h-[44px] max-h-[120px]"
                textAlignVertical="top"
                editable={!isTextInputDisabled}
                multiline={true}
                blurOnSubmit={false}
              />
            </View>

            {hasContent ? (
              <Pressable
                onPress={() => {
                  handleSendMessage();
                  Keyboard.dismiss();
                }}
                disabled={isSendButtonDisabled}
                className={`w-11 h-11 rounded-full items-center justify-center ${
                  isSendButtonDisabled ? 'bg-gray-400' : 'bg-green-800'
                }`}
              >
                {isAudioUploading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Send color="#ffffff" width={2} height={2} />
                )}
              </Pressable>
            ) : (
              <Pressable
                onPress={handleMicrophonePress}
                disabled={isMicrophoneDisabled}
                className={`w-11 h-11 rounded-full items-center justify-center ${
                  isRecording ? 'bg-gray-500' : 'bg-gray-200'
                }`}
              >
                <Microphone color={isRecording ? '#ffffff' : '#666666'} width={2.5} height={2.5} />
              </Pressable>
            )}
          </View>
        </View>
      )}
    </>
  );
};
export default ChatInput;

