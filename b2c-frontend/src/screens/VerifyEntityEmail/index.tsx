/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { HomeStackParamListI } from '@/src/navigation/types';
import VerifyEntityEmailForm from './components/VerifyEntityEmail';

type VerifyAccountRouteProp = RouteProp<HomeStackParamListI, 'VerifyEntityEmail'>;

const VerifyEntityEmail = () => {
  const route = useRoute<VerifyAccountRouteProp>();
  const { email, entityProfileId, emailType } = route.params;

  return (
    <SafeArea>
      <VerifyEntityEmailForm
        email={email}
        entityProfileId={entityProfileId}
        emailType={emailType}
      />
    </SafeArea>
  );
};

export default VerifyEntityEmail;
