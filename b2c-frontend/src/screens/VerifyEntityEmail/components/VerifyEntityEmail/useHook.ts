import { useState, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { OTPInputHandle } from '@/src/components/OtpInput/types';
import { showToast } from '@/src/utilities/toast';
import { HomeStackParamListI } from '@/src/navigation/types';
import { verifyOTPForEntityProfileEmailVerificationAPI } from '@/src/networks/entityProfile/createEntityProfile';

const RESEND_OTP_COUNTDOWN = 90;

export const useVerifyEntityEmail = (email: string, emailType: string, entityProfileId: string) => {
  const [otp, setOtp] = useState('');
  const [countdown, setCountdown] = useState(RESEND_OTP_COUNTDOWN);
  const [isResending, setIsResending] = useState(false);
  const [loading, setLoading] = useState(false);
  const otpRef = useRef<OTPInputHandle>(null);
  const navigation =
    useNavigation<StackNavigationProp<HomeStackParamListI, 'CreateEntityProfileSuccess'>>();

  const handleVerify = async () => {
    if (otp.length === 6) {
      try {
        setLoading(true);
        await verifyOTPForEntityProfileEmailVerificationAPI({ entityProfileId, otp });
        if (entityProfileId && emailType === 'WORK') {
          navigation.navigate('CreateEntityProfileSuccess', { entityProfileId });
        }
        if (emailType === 'PERSONAL') {
          navigation.navigate('VerificationReviewSentSuccess');
        }
      } catch (error) {
        showToast({
          type: 'error',
          message: 'Failed to Verify OTP',
          description: 'Please try again later.',
        });
        console.error('OTP verification failed:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleResendOTP = async () => {
    if (!email || isResending) return;

    try {
      setIsResending(true);

      setCountdown(RESEND_OTP_COUNTDOWN);
      setOtp('');
      otpRef.current?.clear();

      showToast({
        type: 'success',
        message: 'OTP Sent',
        description: 'A new verification code has been sent to your email.',
      });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to Send OTP',
        description: 'Please try again later.',
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return {
    otp,
    setOtp,
    setCountdown,
    countdown,
    handleResendOTP,
    handleVerify,
    otpRef,
    loading,
    isResending,
    handleBack,
  };
};
