import { useEffect } from 'react';
import { Pressable, Text, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import OtpInput from '@/src/components/OtpInput';
import { VerifyEntityFormPropsI } from './types';
import { useVerifyEntityEmail } from './useHook';

const VerifyEntityEmailForm = ({ email, entityProfileId, emailType }: VerifyEntityFormPropsI) => {
  const {
    otp,
    setOtp,
    setCountdown,
    countdown,
    handleResendOTP,
    handleVerify,
    otpRef,
    isResending,
    loading,
    handleBack,
  } = useVerifyEntityEmail(email, emailType, entityProfileId);

  useEffect(() => {
    if (countdown <= 0) return;
    const timer = setInterval(() => setCountdown((prev) => prev - 1), 1000);
    return () => clearInterval(timer);
  }, [countdown]);

  //   useEffect(() => {
  //     const redirectIfVerified = async () => {
  //       if (isEmailVerified && currentUser) {
  //         await navigateBasedOnUserState({
  //           isUsernameSaved: currentUser.isUsernameSaved ?? false,
  //           isPersonalDetailsSaved: currentUser.isPersonalDetailsSaved ?? false,
  //           isWorkDetailsSaved: currentUser.isWorkDetailsSaved ?? false,
  //           email: currentUser.email ?? '',
  //           isEmailVerified: true,
  //           profileId: currentUser.profileId ?? '',
  //           isPrivacyPolicyAccepted: currentUser.isPrivacyPolicyAccepted ?? false,
  //           isReferred: currentUser.isReferred ?? false,
  //         });
  //       }
  //     };

  //     redirectIfVerified();
  //   }, [isEmailVerified, currentUser]);

  return (
    <View className="px-5 gap-2">
      <BackButton onBack={handleBack} />
      <OtpInput
        title="Verification"
        subtitle={`Please enter the code sent to ${email}`}
        value={otp}
        onChange={setOtp}
        autoFocus
        ref={otpRef}
      />

      <View className="flex-col gap-5">
        <View className="flex-row mt-4 gap-1">
          <Text className="text-gray-500">Didn't get the code?</Text>
          <Pressable onPress={handleResendOTP} disabled={countdown > 0 || isResending}>
            <Text className={countdown > 0 || isResending ? 'text-gray-400' : 'text-green-700'}>
              {isResending
                ? 'Sending...'
                : countdown > 0
                  ? `Resend in ${countdown}s`
                  : 'Resend Code'}
            </Text>
          </Pressable>
        </View>

        <Button
          label="Verify"
          onPress={handleVerify}
          variant={otp.length === 6 ? 'primary' : 'tertiary'}
          disabled={otp.length !== 6 || loading}
          loading={loading}
        />
      </View>
    </View>
  );
};

export default VerifyEntityEmailForm;
