/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { navigate } from '@/src/utilities/navigation';
import { HomeStackParamListI } from '@/src/navigation/types';
import AdminDetails from './components/AddAdminDetailsForm';
import { AdminDetailsFormDataI, AdminI } from './components/AddAdminDetailsForm/types';
import CompanyDetails from './components/AddCompanyDetailsForm';
import { CompanyDetailsFormDataI } from './components/AddCompanyDetailsForm/types';
import VerificationDetails from './components/AddVerificationDetailsForm';
import { VerificationDetailsFormDataI } from './components/AddVerificationDetailsForm/types';

const CreateEntityProfile = () => {
  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI, 'VerifyEntityEmail'>>();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [companyData, setCompanyData] = useState<CompanyDetailsFormDataI>();
  const [adminData, setAdminData] = useState<AdminI[]>();
  const [verificationData, setVerificationData] = useState<VerificationDetailsFormDataI>();

  const handleCompanyDetailsNext = (data: CompanyDetailsFormDataI) => {
    setCompanyData(data);
    setCurrentStepIndex(1);
  };

  const handleAdminDetailsNext = async (data: AdminI[]) => {
    setAdminData(data);
    setCurrentStepIndex(2);
  };

  const handleVerificationDetailsNext = async (data: VerificationDetailsFormDataI) => {
    setVerificationData(data);
  };

  const handleAdminBack = () => {
    setCurrentStepIndex(0);
  };

  const handleVerificationBack = () => {
    setCurrentStepIndex(1);
  };

  const handleCompanyDetailsBack = () => {
    navigation.goBack();
  };

  const defaultCompanyData = {
    company: companyData?.company!,
    website: companyData?.website,
    type: companyData?.type!,
  };

  const defaultAdminData = {
    company: companyData?.company!,
    website: companyData?.website,
    type: companyData?.type,
    admins: adminData,
  };

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{ flex: 1 }}
        >
          <View className="flex-1 p-5">
            {(() => {
              switch (currentStepIndex) {
                case 0:
                  return (
                    <CompanyDetails
                      onNext={handleCompanyDetailsNext}
                      initialData={companyData}
                      onBack={handleCompanyDetailsBack}
                    />
                  );
                case 1:
                  return (
                    <AdminDetails
                      onNext={handleAdminDetailsNext}
                      onBack={handleAdminBack}
                      companyData={defaultCompanyData}
                      initialData={adminData}
                    />
                  );
                case 2:
                  return (
                    <VerificationDetails
                      onNext={handleVerificationDetailsNext}
                      onBack={handleVerificationBack}
                      adminData={
                        defaultAdminData as unknown as CompanyDetailsFormDataI &
                          AdminDetailsFormDataI
                      }
                      initialData={verificationData}
                    />
                  );
                default:
                  return (
                    <CompanyDetails
                      onNext={handleCompanyDetailsNext}
                      onBack={handleCompanyDetailsBack}
                    />
                  );
              }
            })()}
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default CreateEntityProfile;
