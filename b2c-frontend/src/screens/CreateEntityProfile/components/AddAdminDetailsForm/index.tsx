import { Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import AdminSelection from '@/src/components/AdminSelection';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';
import ProgressBar from '@/src/components/Progress';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { AdminDetailsPropsI } from './types';
import useCompanyDetails from './useHook';

const AdminDetails = ({ onNext, onBack, initialData, companyData }: AdminDetailsPropsI) => {
  const { methods, isSubmitting, onSubmit, admins, handleDeleteAdmin } = useCompanyDetails(
    onNext,
    companyData!,
  );
  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = methods;

  return (
    <View className="flex-1 bg-white">
      {onBack && <BackButton onBack={onBack} />}
      <View className="my-4">
        <View className="flex-row items-center justify-between">
          <Text className="text-xl font-bold leading-6">
            Enter the following details to create your organisation profile
          </Text>
        </View>
      </View>
      <View className="mt-2 flex flex-row items-center gap-4">
        <Text className="text-base font-medium leading-6">Admin details (2/3)</Text>
        <ProgressBar progress={60} />
      </View>
      <AdminSelection admins={admins} onDelete={handleDeleteAdmin} classname="pt-5" />

      <EntitySearch
        title="Select Admin(s)"
        placeholder="select one admin"
        selectionKey="username"
      />
      <View className="mt-8">
        <Button
          label="Next"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </View>
  );
};

export default AdminDetails;
