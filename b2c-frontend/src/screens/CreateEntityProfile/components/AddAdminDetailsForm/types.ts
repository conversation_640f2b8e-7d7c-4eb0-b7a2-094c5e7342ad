/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { CompanyDetailsFormDataI } from '../AddCompanyDetailsForm/types';

export type AdminDetailsFormDataI = {
  admins: SearchResultI[];
};

export type UseAdminDetailsFormI = {
  methods: UseFormReturn<AdminDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: AdminDetailsFormDataI) => Promise<void>;
  admins: AdminI[];
  handleDeleteAdmin: (id: string) => void;
};

export type AdminDetailsPropsI = {
  onNext: (data: AdminI[]) => void;
  onBack?: () => void;
  initialData?: AdminI[];
  companyData?: CompanyDetailsFormDataI;
};

export type AdminI = {
  id: string;
  name: string;
  avatar: string | null;
  username: string;
};
