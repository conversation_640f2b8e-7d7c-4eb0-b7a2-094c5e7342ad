/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { CompanyDetailsFormDataI } from '../AddCompanyDetailsForm/types';
import { AdminDetailsFormDataI, AdminI, UseAdminDetailsFormI } from './types';

const useCompanyDetails = (
  onNext: (data: AdminI[]) => void,
  companyData: CompanyDetailsFormDataI,
): UseAdminDetailsFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const adminSelection = useSelector(selectSelectionByKey('username')) as unknown as AdminI;
  const [admins, setAdmins] = useState([
    {
      id: currentUser.profileId,
      username: currentUser.username,
      avatar: currentUser.avatar,
      name: currentUser.fullName,
    },
  ]);

  useEffect(() => {
    if (adminSelection) {
      setAdmins((prev) => {
        return prev.some(({ id }) => id === adminSelection.id)
          ? prev
          : [{ ...adminSelection }, ...prev];
      });
    }
  }, [adminSelection]);

  const entitySelection = useSelector(selectSelectionByKey('entity'));

  const methods = useForm<AdminDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      admins: [entitySelection],
    },
  });

  const onSubmit = async (data: AdminDetailsFormDataI) => {
    try {
      setIsSubmitting(true);

      const payload = {};

      //   await companyDetailsCreationAPI(payload);
      onNext(admins);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAdmin = (id: string) => {
    setAdmins((prev) => prev.filter((item) => item.id !== id));
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    admins,
    handleDeleteAdmin,
  };
};

export default useCompanyDetails;
