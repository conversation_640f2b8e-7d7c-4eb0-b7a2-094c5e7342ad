import { useEffect } from 'react';
import { Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';
import ProgressBar from '@/src/components/Progress';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { CompanyDetailsFormDataI, CompanyDetailsPropsI } from './types';
import useCompanyDetails from './useHook';

const TYPE_OPTIONS = [
  { title: 'EDUCATION', id: 'education' },
  { title: 'COMPANY', id: 'company' },
  { title: 'GO', id: 'go' },
  { title: 'NGO', id: 'ngo' },
  { title: 'SMO', id: 'smo' },
  { title: 'OTHER', id: 'other' },
];

const CompanyDetails = ({ onNext, onBack, initialData }: CompanyDetailsPropsI) => {
  const { methods, isSubmitting, onSubmit } = useCompanyDetails(onNext);
  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = methods;

  const entitySelection = useSelector(selectSelectionByKey('entity'));

  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        setValue(key as keyof CompanyDetailsFormDataI, value);
      });
      trigger();
    }
    if (entitySelection) {
      methods.setValue('company', entitySelection);
    }
  }, [initialData, setValue, trigger, entitySelection]);

  return (
    <View className="flex-1 bg-white">
      <BackButton onBack={onBack} />
      <View className="my-4">
        <View className="flex-row items-center justify-between">
          <Text className="text-xl font-bold leading-6">
            Enter the following details to create your organisation profile
          </Text>
        </View>
      </View>
      <View className="mt-2 flex flex-row items-center gap-4">
        <Text className="text-base font-medium leading-6">Company details (1/3)</Text>
        <ProgressBar progress={30} />
      </View>
      <View className="mt-5 flex flex-col gap-4">
        <Controller
          control={control}
          name="company"
          rules={{
            required: 'Company name is required',
          }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Organisation name"
              selectionKey="entity"
              title="Organisation Name"
              data={entitySelection?.name || ''}
              error={error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="type"
          render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
            <Select
              ref={ref}
              label="Organisation Type"
              options={TYPE_OPTIONS}
              value={value}
              onChange={(val) => {
                onChange(val);
                trigger('type');
              }}
              placeholder="Select Organisation Type"
              error={error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="website"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Website"
              value={value}
              onChangeText={(val) => {
                onChange(val);
              }}
              placeholder="Enter website"
              error={error?.message}
              autoCapitalize="none"
              keyboardType="url"
            />
          )}
        />
      </View>
      <View className="mt-8">
        <Button
          label="Next"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </View>
  );
};

export default CompanyDetails;
