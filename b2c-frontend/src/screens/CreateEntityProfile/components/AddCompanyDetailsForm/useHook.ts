/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelection<PERSON>y<PERSON>ey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { CompanyDetailsFormDataI, UseCompanyDetailsFormI } from './types';

const useCompanyDetails = (
  onNext: (data: CompanyDetailsFormDataI) => void,
): UseCompanyDetailsFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const entitySelection = useSelector(selectSelectionByKey('entity'));

  const methods = useForm<CompanyDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      company: entitySelection,
      website: '',
      type: '',
    },
  });

  const onSubmit = async (data: CompanyDetailsFormDataI) => {
    try {
      setIsSubmitting(true);
      onNext(data);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
  };
};

export default useCompanyDetails;
