/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type CompanyDetailsFormDataI = {
  company: SearchResultI;
  website?: string;
  type: string;
};

export type UseCompanyDetailsFormI = {
  methods: UseFormReturn<CompanyDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: CompanyDetailsFormDataI) => Promise<void>;
};

export type CompanyDetailsPropsI = {
  onNext: (data: CompanyDetailsFormDataI) => void;
  onBack: () => void;
  initialData?: CompanyDetailsFormDataI;
};
