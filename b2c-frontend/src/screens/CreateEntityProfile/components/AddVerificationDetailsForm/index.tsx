import { useEffect } from 'react';
import { Text, View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import CharacterCounter from '@/src/components/CharacterCounter';
import EntitySearch from '@/src/components/EntitySearch';
import InfoModal from '@/src/components/InfoModal';
import ProgressBar from '@/src/components/Progress';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { VerificationDetailsFormDataI, VerificationDetailsPropsI } from './types';
import useCompanyDetails from './useHook';

const VerificationDetails = ({
  onNext,
  onBack,
  initialData,
  adminData,
}: VerificationDetailsPropsI) => {
  const { methods, isSubmitting, onSubmit, isPersonalEmail } = useCompanyDetails(onNext, adminData);
  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = methods;

  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        setValue(key as keyof VerificationDetailsFormDataI, value);
      });
      trigger();
    }
  }, [initialData, setValue, trigger]);

  return (
    <View className="flex-1 bg-white">
      {onBack && <BackButton onBack={onBack} />}
      <View className="my-4">
        <View className="flex-row items-center justify-between">
          <Text className="text-xl font-bold leading-6">
            Enter the following details to create your organisation profile
          </Text>
        </View>
      </View>
      <View className="mt-2 flex flex-row items-center gap-4">
        <Text className="text-base font-medium leading-6">Verification (3/3)</Text>
        <ProgressBar progress={100} />
      </View>
      <View className="mt-5 flex flex-col gap-4">
        <Controller
          control={control}
          name="email"
          rules={{ required: 'Email is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Business Email ID"
              value={value}
              onChangeText={(val) => {
                onChange(val);
              }}
              placeholder="<EMAIL>"
              error={error?.message}
              autoCapitalize="none"
            />
          )}
        />
        {isPersonalEmail && (
          <Controller
            control={control}
            name="purpose"
            rules={{
              required: isPersonalEmail
                ? 'Please explain the purpose of creating this account'
                : false,
              maxLength: {
                value: 1000,
                message: 'Purpose cannot exceed 1000 characters',
              },
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <>
                <TextInput
                  label="What is the purpose of creating this account?"
                  value={value}
                  onChangeText={(val) => {
                    onChange(val);
                  }}
                  placeholder="Enter here"
                  error={error?.message}
                  type="textarea"
                />
                <CharacterCounter currentLength={value!.length} maxLength={1000} />
              </>
            )}
          />
        )}
      </View>
      <View className="mt-8">
        <Button
          label="Next"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </View>
  );
};

export default VerificationDetails;
