/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { AdminDetailsFormDataI } from '../AddAdminDetailsForm/types';
import { CompanyDetailsFormDataI } from '../AddCompanyDetailsForm/types';

export type VerificationDetailsFormDataI = {
  email: string;
  purpose?: string;
};

export type UseVerificationDetailsFormI = {
  methods: UseFormReturn<VerificationDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: VerificationDetailsFormDataI) => Promise<void>;
  isPersonalEmail: boolean;
};

export type VerificationDetailsPropsI = {
  onNext: (data: VerificationDetailsFormDataI) => void;
  onBack?: () => void;
  initialData?: VerificationDetailsFormDataI;
  adminData: CompanyDetailsFormDataI & AdminDetailsFormDataI;
};
