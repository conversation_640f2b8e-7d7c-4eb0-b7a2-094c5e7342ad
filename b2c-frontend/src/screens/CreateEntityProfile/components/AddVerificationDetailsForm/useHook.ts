/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { FetchProfileResultI } from '@/src/redux/slices/user/types';
import { handleError } from '@/src/utilities/errors/errors';
import { containsPersonalEmailDomain } from '@/src/utilities/mail/checkGenericMail';
import { HomeStackParamListI } from '@/src/navigation/types';
import {
  createEntityProfileAPI,
  sendOTPForEntityProfileEmailVerificationAPI,
} from '@/src/networks/entityProfile/createEntityProfile';
import { CreateEntityProfileBodyI } from '@/src/networks/entityProfile/types';
import { AdminDetailsFormDataI } from '../AddAdminDetailsForm/types';
import { CompanyDetailsFormDataI } from '../AddCompanyDetailsForm/types';
import { UseVerificationDetailsFormI, VerificationDetailsFormDataI } from './types';

const useVerificationDetails = (
  onNext: (data: VerificationDetailsFormDataI) => void,
  adminData: CompanyDetailsFormDataI & AdminDetailsFormDataI,
): UseVerificationDetailsFormI => {
  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI>>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPersonalEmail, setIsPersonalEmail] = useState(false);

  const currentUser = useSelector(selectCurrentUser);

  const methods = useForm<VerificationDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      purpose: '',
    },
  });

  const { watch } = methods;
  const email = watch('email');

  useEffect(() => {
    if (email) {
      setIsPersonalEmail(containsPersonalEmailDomain(email));
    }
  }, [email]);

  const onSubmit = async (data: VerificationDetailsFormDataI) => {
    try {
      setIsSubmitting(true);

      const payload = transformDataforCreate(
        data,
        adminData,
        currentUser as unknown as FetchProfileResultI,
      );
      const result = await createEntityProfileAPI(payload);
      await sendOTPForEntityProfileEmailVerificationAPI({ entityProfileId: result.id });
      navigation.navigate('VerifyEntityEmail', {
        email: data.email,
        entityProfileId: result.id,
        emailType: payload.emailType!,
      });
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    isPersonalEmail,
  };
};

export default useVerificationDetails;

const transformDataforCreate = (
  data: VerificationDetailsFormDataI,
  adminData: CompanyDetailsFormDataI & AdminDetailsFormDataI,
  currentUser: FetchProfileResultI,
) => {
  const result: CreateEntityProfileBodyI = {
    profileId: currentUser.profileId,
    name: adminData.company.name,
    type: adminData.type.toUpperCase(),
    admins: adminData.admins.map((admin) => admin.id),
    email: data.email,
  };

  if (containsPersonalEmailDomain(data.email)) {
    result['emailType'] = 'PERSONAL';
  } else {
    result['emailType'] = 'WORK';
  }

  if (adminData.company.dataType === 'master') {
    result['entityId'] = adminData.company.id;
  } else {
    result['entityRawDataId'] = adminData.company.id;
  }

  if (adminData.website) {
    result['website'] = adminData.website.startsWith('http')
      ? adminData.website
      : `https://${adminData.website}`;
  }

  if (data.purpose?.length) {
    result['purpose'] = data.purpose;
  }

  return result;
};
