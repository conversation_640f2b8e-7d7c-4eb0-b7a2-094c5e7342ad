import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { RootDrawerParamListI } from '@/src/navigation/types';
import UserSettingsMenu from './components/UserSettingsMenu';

const UserSettings = () => {
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const handleBack = () => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate('MainTabs', {
        screen: 'HomeStack',
        params: {
          screen: 'Home',
        },
      });
    }
  };
  return (
    <SafeArea>
      <UserSettingsMenu onBack={handleBack} />
    </SafeArea>
  );
};

export default UserSettings;
