import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CareerStackParamListI } from '@/src/navigation/types';
import { fetchJobsForApplicantAPI } from '@/src/networks/jobs/fetchJobsForApplicants';
import { JobI } from '../Careers/types';
import { useDispatch, useSelector } from 'react-redux';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { selectActiveFilterNumbers, selectActiveFilters } from '@/src/redux/selectors/careers';
import { AppDispatch } from '@/src/redux/store';
import { setFilters } from '@/src/redux/slices/career/careerSlice';
import { fetchFiltersForApplicantAPI } from '@/src/networks/jobs/fetchFiltersForApplicants';

export const useMyJobs = (type: string) => {
  const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
  const activeFilterNumbers = useSelector(selectActiveFilterNumbers('myJobs'));
  const [jobs, setJobs] = useState<JobI[]>([]);
  const filters = useSelector(selectActiveFilters('myJobs'))
  

  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0
    },
    {
      id: 'ship-type',
      label: 'Ship Type',
      appliedCount: activeFilterNumbers.shipTypes || 0
    },
  ];

  const fetchJobs = async (loadMore = false) => {
    try {
      if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        cursorId: null,
        pageSize: 10,
        status: transformStatus(type),
      };
      const body = {
        designations:filters.designations,
        shipTypes:filters.shipTypes
      }
      const result = await fetchJobsForApplicantAPI(query,body);
      if (loadMore) {
        setJobs((prev) => [...prev,...result.data])
      } else {
        setJobs(result.data)
      }

        setCursorId(result.nextCursorId);
        setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchJobs();
  }, [type]);

  const loadMoreJobs = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchJobs(true);
  };

    const onFilterPress = async() => {
        const filters = await fetchFiltersForApplicantAPI();
        dispatch(setFilters({
            page:'myJobs',
            filters:filters
        }))
    }

  return {
    jobs,
    searchText,
    navigation,
    filterTabs,
    isLoadingMore,
    loading,
    setSearchText,
    loadMoreJobs,
    fetchJobs,
    onFilterPress
  };
};

const transformStatus = (type: string) => {
  const status = {
    applied: 'PENDING',
    shortlisted: 'SHORTLISTED',
    offered: 'OFFERED',
  };

  return status[type as keyof typeof status];
};
