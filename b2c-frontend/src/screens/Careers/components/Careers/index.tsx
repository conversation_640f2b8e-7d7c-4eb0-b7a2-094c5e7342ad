import { useCallback, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import CareerFilterBar from '@/src/components/CareerFilterBar';
import Job from '@/src/components/Job';
import SafeArea from '@/src/components/SafeArea';
import ToggleSelection from '@/src/components/ToggleSelection';
import { CareersPropsI } from './types';
import { useCareers } from './useHook';

const Careers = ({ onBack }: CareersPropsI) => {
  const {
    navigation,
    bottomSheetItems,
    searchText,
    jobs,
    filterTabs,
    loading,
    isLoadingMore,
    loadMoreJobs,
    setSearchText,
    fetchJobs,
    onFilterPress
  } = useCareers();

  const onRefresh = useCallback(() => {
    fetchJobs(false);
  }, []);

  const renderFooter = () => (
    <View className="pb-4">
      {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
    </View>
  );

  const options = [{ id: 'jobs', name: '<PERSON><PERSON>' }];

  const [selectedOption, setSelectedOption] = useState({ id: 'jobs', name: 'Jobs' });

  return (
    <SafeArea>
      <View className="flex-row items-center justify-between mb-6">
        <BackButton onBack={onBack} label="Careers" />
        <ToggleSelection
          options={options}
          onPress={setSelectedOption}
          selectedOption={selectedOption}
        />
      </View>
      <CareerFilterBar
        page='jobs'
        ellipsesVisible={true}
        searchTextValue={searchText}
        onSearchTextChange={(text) => setSearchText(text)}
        bottomSheetItems={bottomSheetItems}
        filterTabs={filterTabs}
        onFilterPress={onFilterPress}
        onApplyPress={fetchJobs}
      />
      {selectedOption.id === 'jobs' ? (
        <FlatList
          data={jobs}
          renderItem={({ item }) => <Job job={item} />}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          onEndReached={loadMoreJobs}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor="#000" />
          }
        />
      ) : (
        <></>
      )}
    </SafeArea>
  );
};

export default Careers;
