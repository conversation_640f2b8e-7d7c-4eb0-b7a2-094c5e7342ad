import { FC, JSX } from 'react';
import { FilledIconPropsI, OutlinedIconPropsI } from '@/src/assets/svgs/types';
import { ProfileExternalI } from '@/src/networks/connect/types';
import { IdNameTypeI, ImoNameDataTypeI } from '@/src/types/common/data';

export type optionScreensI = {
  jobs: JSX.Element;
  courses: JSX.Element;
};

export type bottomSheetItemPropsI = {
  id: string;
  label: string;
  icon: FC<OutlinedIconPropsI> | FC<FilledIconPropsI>;
  onPress: () => void;
};

export type JobI = {
  applicationStatus?: string | null;
  createdAt:string
  creator:ProfileExternalI;
  cursorId:string
  department:IdNameTypeI
  designation:IdNameTypeI
  entity:IdNameTypeI
  expiryDate:string
  id:string
  isOfficial:boolean
  isUrgent:boolean
  matching:number
  maxSalary:number
  maxYears:number
  minSalary:number
  minYears:number
  ship:ImoNameDataTypeI;
  status:string
};

export type CareersPropsI = {
  onBack: () => void;
};
