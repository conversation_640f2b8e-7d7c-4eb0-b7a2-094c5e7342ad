import SafeArea from '@/src/components/SafeArea';
import CommunityList from './components/CommunityList';
import FloatingActionButton from '@/src/components/FloatingActionButton';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const MyCommunitiesScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleCreateCommunity = () => {
    navigation.navigate('CreateCommunity');
  }

  return (
    <SafeArea>
      <CommunityList />
      <FloatingActionButton
        context="CREATE_COMMUNITY"
        onPress={handleCreateCommunity}
        visible={true}
      />
    </SafeArea>
  );
};

export default MyCommunitiesScreen;
