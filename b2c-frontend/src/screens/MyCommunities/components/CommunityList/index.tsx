import { FlatList, Pressable, StatusBar, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BackButton from '@/src/components/BackButton';
import AddItem from '@/src/assets/svgs/AddItem';
import { FetchCommunityClientI } from '@/src/networks/community/types';
import CommunityItem from '../CommunityItem';
import useMyCommunities from './useHook';

const CommunityList = () => {
  const insets = useSafeAreaInsets();
  const { communities, loadMore, onCommunityPress, handleAdd, onBack } = useMyCommunities();

  const renderCommunityItem = ({ item }: { item: FetchCommunityClientI }) => (
    <CommunityItem community={item} onPress={() => onCommunityPress?.(item)} />
  );

  return (
    <View className="px-3 flex-1">
      <View className="flex-row items-center justify-between ">
        {/* <Pressable onPress={handleAdd}>
          <AddItem />
        </Pressable> */}
      </View>

      <FlatList
        data={communities}
        renderItem={renderCommunityItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        className="flex-1"
        onEndReached={loadMore}
        onEndReachedThreshold={0.7}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: 50 + insets.bottom,
        }}
      />
    </View>
  );
};

export default CommunityList;
