import React, { useEffect, useState } from 'react';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { showToast } from '@/src/utilities/toast';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { fetchOwnCommunities } from '@/src/networks/community/community';
import { CommunityFetchForClientI, FetchCommunityClientI } from '@/src/networks/community/types';

const useMyCommunities = () => {
  const [communities, setCommunities] = useState<FetchCommunityClientI[]>([]);
  const [lastCursorId, setLastCursorId] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const onBack = () => {
    navigation.navigate('Forum', { activeTab: 'communities' });
  };

  const handleAdd = () => {
    navigation.navigate('CreateCommunity');
  };

  const fetchCommunities = async (query: CommunityFetchForClientI) => {
    try {
      const response = await fetchOwnCommunities(query);
      setCommunities((prev) => (query.cursorId ? [...prev, ...response] : response));
      if (response.length > 0) {
        setLastCursorId(response[response.length - 1].cursorId);
        setHasMore(true);
      } else {
        setLastCursorId(null);
        setHasMore(false);
      }
    } catch (err) {
      showToast({
        type: 'error',
        message: 'Failed to fetch communities',
      });
    }
  };

  const loadMore = async () => {
    if (!hasMore || lastCursorId === null) {
      return;
    }
    await fetchCommunities({
      cursorId: parseInt(lastCursorId),
    });
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchCommunities({ cursorId: null });
    });
    fetchCommunities({ cursorId: null });
    return unsubscribe;
  }, [navigation]);

  const onCommunityPress = (community: FetchCommunityClientI) => {
    navigation.navigate('Community', {
      id: community.id,
      fromCommunityTab: true,
    });
  };
  const removeCommunityLocal = (communityId: string) => {
    setCommunities((prev) => prev.filter((c) => c.id !== communityId));
  };

  return {
    communities,
    loadMore,
    onBack,
    onCommunityPress,
    handleAdd,
  };
};

export default useMyCommunities;
