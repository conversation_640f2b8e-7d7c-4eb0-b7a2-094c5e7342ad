import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { CareerStackParamListI } from '@/src/navigation/types';
import Applicants from './components/Applicants';

const ApplicantsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<CareerStackParamListI, 'Applicants'>>();

  const { jobId } = route.params;

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <Applicants onBack={handleBack} jobId={jobId} />
    </SafeArea>
  );
};

export default ApplicantsScreen;
