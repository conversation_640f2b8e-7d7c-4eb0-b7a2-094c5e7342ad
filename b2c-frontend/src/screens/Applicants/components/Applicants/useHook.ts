import { useEffect, useState } from 'react';
import { fetchApplicantsForJobPostsAPI } from '@/src/networks/jobs/fetchApplicantsForJobPosts';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { useSelector } from 'react-redux';
import { selectActiveFilterNumbers } from '@/src/redux/selectors/careers';

export const useApplicants = (type: string, jobId: string) => {
  const [applicants, setApplicants] = useState([
    {
      id: '1',
      name: '<PERSON><PERSON>',
      avatar: null,
      designation: {
        id: '3',
        name: 'Third Engineer',
      },
      entity: {
        id: '4',
        name: 'Seaspan Corporation',
      },
      createdAt: '2025-08-15T10:30:00Z',
      score: 36,
    },
    {
      id: '2',
      name: 'Kasjkb ',
      avatar: null,
      designation: {
        id: '5',
        name: 'Junior Analyst',
      },
      entity: null,
      createdAt: '2025-08-14T14:20:00Z',
      score: 42,
    },
    {
      id: '3',
      name: '<PERSON><PERSON> sky',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      designation: {
        id: '8',
        name: 'Senior Architect',
      },
      entity: {
        id: '12',
        name: 'OceanTech Engineering',
      },
      createdAt: '2025-08-13T09:15:00Z',
      score: 29,
    },
    {
      id: '4',
      name: 'mobile electon',
      avatar: null,
      designation: {
        id: '6',
        name: 'Chief Electrician',
      },
      entity: {
        id: '9',
        name: 'Global Shipping Partners',
      },
      createdAt: '2025-08-12T16:45:00Z',
      score: 51,
    },
    {
      id: '5',
      name: 'Rahul mankootathil',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      designation: {
        id: '15',
        name: 'Operations Director slkdjbplkjdnlwkdblwjh lbadpkwjbd;wlekfdw',
      },
      entity: {
        id: '18',
        name: 'Harbor Logistics Ltd lkjbsdpkbdcpowiebpwoknnkajbcpwlkcb poibpcowen',
      },
      createdAt: '2025-08-11T11:00:00Z',
      score: 38,
    },
    {
      id: '6',
      name: 'Baby Babie',
      avatar: null,
      designation: {
        id: '22',
        name: 'Senior Surveyor',
      },
      entity: {
        id: '25',
        name: 'Maritime Safety Bureau',
      },
      createdAt: '2025-08-10T08:30:00Z',
      score: 45,
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
const activeFilterNumbers = useSelector(selectActiveFilterNumbers('applicants'));
  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'location',
      label: 'Location',
    appliedCount: activeFilterNumbers.locations || 0   
    },
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0
    },
    {
      id: 'yoe',
      label: 'Year(s) of Experience',
      appliedCount: activeFilterNumbers.yearOfExperiences || 0
    },
  ];

  const fetchApplicants = async (loadMore = false) => {
    try {
      if (loadMore) {
        if (!cursorId || !hasMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        cursorId: null,
        pageSize: 10,
        status: transformStatus(type),
        jobId,
      };

      const result = await fetchApplicantsForJobPostsAPI(query);
      console.log('result = ', result);
      if (loadMore) {
        // setApplicants((prev) => [...prev,...result])
      } else {
        // setApplicants(result)
      }

      //   setCursorId(result.nextCursorId);
      //   setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchApplicants();
  }, [type]);

  const loadMoreApplicants = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchApplicants(true);
  };

  const onFilterPress = () => {

  }

  return {
    applicants,
    searchText,
    filterTabs,
    loading,
    isLoadingMore,
    setSearchText,
    loadMoreApplicants,
    fetchApplicants,
    onFilterPress
  };
};

const transformStatus = (type: string) => {
  const status = {
    applied: 'PENDING',
    shortlisted: 'SHORTLISTED',
    accepted: 'ACCEPTED_BY_APPLICANT',
    rejected: 'REJECTED_BY_ENTITY',
  };

  return status[type as keyof typeof status];
};
