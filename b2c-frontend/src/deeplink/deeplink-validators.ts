import { fetchPostAPI } from '../networks/content/post';
import { entityProfileFetchAboutAPI } from '../networks/entityProfile/entityProfileAbout';
import { fetchShipDetails } from '../networks/experienceShip.ts/ship';
import { fetchPortProfile } from '../networks/port/profile';
import { fetchScrapbookPost } from '../networks/port/scrapbook';
import { fetchProfileAPI } from '../networks/profile/userProfile';
import { fetchForumQuestionForClientAPI } from '../networks/question/question';
import { DeepLinkCache } from './deeplink-cache';
import { Route, ValidationResult } from './types';

const cache = new DeepLinkCache();

export const validatePost = async (postId: string, type: string): Promise<ValidationResult> => {
  const cacheKey = `post_${postId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const result =
      type === 'USER_POST' ? await fetchPostAPI(postId) : await fetchScrapbookPost(postId);
    if (result) {
      cache.set(cacheKey, result);
      return { isValid: true };
    }
    return { isValid: false };
  } catch {
    return { isValid: false };
  }
};

export const validateProfile = async (profileId: string): Promise<ValidationResult> => {
  const cacheKey = `profile_${profileId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const response = await fetchProfileAPI(profileId);
    cache.set(cacheKey, response);
    return { isValid: true };
  } catch {
    return { isValid: false };
  }
};

export const validateEntityProfile = async (entityProfileId: string): Promise<ValidationResult> => {
  const cacheKey = `entityprofile_${entityProfileId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const response = await entityProfileFetchAboutAPI({ entityProfileId });
    cache.set(cacheKey, response);
    return { isValid: true };
  } catch {
    return { isValid: false };
  }
};

export const validateForumPost = async (postId: string): Promise<ValidationResult> => {
  const cacheKey = `forum_${postId}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const result = await fetchForumQuestionForClientAPI(postId);
    if (result) {
      cache.set(cacheKey, result);
      return { isValid: true };
    }
    return { isValid: false };
  } catch {
    return { isValid: false };
  }
};

export const validateShip = async (imo: string, dataType: string): Promise<ValidationResult> => {
  const cacheKey = `ship_${imo}_${dataType}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const result = await fetchShipDetails({ imo, dataType });
    if (result) {
      cache.set(cacheKey, result);
      return { isValid: true };
    }
    return { isValid: false };
  } catch {
    return { isValid: false };
  }
};

export const validatePort = async (
  unLocode: string,
  dataType: string,
): Promise<ValidationResult> => {
  const cacheKey = `port_${unLocode}_${dataType}`;
  const cached = cache.get(cacheKey);
  if (cached) return { isValid: true, cached: true };

  try {
    const result = await fetchPortProfile({ unLocode, dataType });
    if (result) {
      cache.set(cacheKey, result);
      return { isValid: true };
    }
    return { isValid: false };
  } catch {
    return { isValid: false };
  }
};

export const validateRoute = async (
  route: Route,
  params: Record<string, string>,
): Promise<ValidationResult> => {
  if (route === 'post') return validatePost(params.postId, params.type);
  if (route === 'profile') return validateProfile(params.profileId);
  if (route === 'entity-profile') return validateEntityProfile(params.entityProfileId);
  if (route === 'forum') return validateForumPost(params.postId);
  if (route === 'ship') return validateShip(params.imo, params.dataType);
  if (route === 'port') return validatePort(params.unLocode, params.dataType);
  return { isValid: false };
};
