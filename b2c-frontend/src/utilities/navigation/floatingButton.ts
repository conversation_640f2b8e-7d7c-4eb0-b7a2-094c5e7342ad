import { getFocusedRouteNameFromRoute } from '@react-navigation/native';
import type { FloatingButtonContextI } from '@/src/components/FloatingActionButton/types';

export const getFloatingButtonContext = (navigationState: any): FloatingButtonContextI => {
    const mainTabsRoute = navigationState?.routes?.find((route: any) => route.name === 'MainTabs');

    if (!mainTabsRoute?.state) {
        if (mainTabsRoute && !mainTabsRoute.state) {
            return 'CREATE_POST';
        }
        return 'DEFAULT';
    }

    const tabState = mainTabsRoute.state;
    const currentTabRoute = tabState.routes[tabState.index];

    if (!currentTabRoute) return 'DEFAULT';


    const focusedRouteName = getFocusedRouteNameFromRoute(currentTabRoute);

    switch (currentTabRoute.name) {
        case 'HomeStack':
            return focusedRouteName === 'Home' ? 'CREATE_POST' : 'DEFAULT';

        case 'LearnCollabStack':
            if (focusedRouteName === 'Forum' || focusedRouteName === 'Community') {
                return 'DEFAULT';
            }
            const forumScreens = ['LearnCollab', 'ExploreForum', 'ExploreQna',
                'Community'];
            return forumScreens.includes(focusedRouteName || '') ? 'CREATE_QUESTION' : 'DEFAULT';

        case 'NearbyStack':
            if (focusedRouteName === 'Nearby') {
                return 'DEFAULT';
            }
            const nearbyParams = currentTabRoute.params as any;
            const activeTab = nearbyParams?.activeTab || 'people';
            return activeTab === 'event' ? 'CREATE_EVENT' : 'DEFAULT';

        case 'NotificationStack':
            return 'DEFAULT';

        default:
            return 'DEFAULT';
    }
};


 export const getFloatingButtonNavigation = (
      context: FloatingButtonContextI,
      navigation: any
  ) => {
      switch (context) {
          case 'CREATE_POST':
              return () => navigation.navigate('CreateStack', {
                  screen: 'CreateContent',
                  params: { type: 'USER_POST' }
              });

          case 'CREATE_QUESTION':
              return () => navigation.navigate('MainTabs', {
                  screen: 'LearnCollabStack',
                  params: { screen: 'CommunityQuestion', params: { id: '' } }
              });

          case 'CREATE_EVENT':
              return () => navigation.navigate('NearbyStack', {
                  screen: 'EditAnnouncement'
              });

          case 'CREATE_COMMUNITY':  
              return () => navigation.navigate('MainTabs', {
                  screen: 'LearnCollabStack',
                  params: { screen: 'CreateCommunity' }
              });

          case 'CREATE_COMMUNITY_QUESTION':
              return () => navigation.navigate('MainTabs', {
                  screen: 'LearnCollabStack',
                  params: { screen: 'CommunityQuestion', params: { id: 'new' } }
              });

          default:
              return () => navigation.navigate('CreateStack', {
                  screen: 'CreateContent',
                  params: { type: 'USER_POST' }
              });
      }
  };
