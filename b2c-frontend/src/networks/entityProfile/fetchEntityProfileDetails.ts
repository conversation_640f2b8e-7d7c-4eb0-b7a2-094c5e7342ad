/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  EntityProfileDetailsUpdateBodyI,
  entityProfileFetchAboutQueryI,
  entityProfileFetchBasicDetailsI,
} from './types';

export const fetchEntityProfileBasicDetailsAPI = async (
  query: entityProfileFetchAboutQueryI,
): Promise<entityProfileFetchBasicDetailsI> => {
  const result = await apiCall<entityProfileFetchAboutQueryI, entityProfileFetchBasicDetailsI>(
    '/backend/api/v1/company/entity-profile',
    'GET',
    { isAuth: true, query },
  );

  return result;
};

export const updateEntityProfileBasicDetailsAPI = async (
  query: entityProfileFetchAboutQueryI,
  payload: EntityProfileDetailsUpdateBodyI,
): Promise<void> => {
  await apiCall('/backend/api/v1/company/entity-profile', 'PATCH', {
    isAuth: true,
    query,
    payload,
  });
};
