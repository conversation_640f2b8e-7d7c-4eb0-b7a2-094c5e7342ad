/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { entityProfileFetchAboutQueryI, entityProfileFetchAboutResultI } from './types';

export const entityProfileFetchAboutAPI = async (
  query: entityProfileFetchAboutQueryI,
): Promise<entityProfileFetchAboutResultI> => {
  const result = await apiCall<entityProfileFetchAboutQueryI, entityProfileFetchAboutResultI>(
    '/backend/api/v1/company/entity-profile/about',
    'GET',
    { isAuth: true, query },
  );
  return result;
};
