import { apiCall } from '@/src/services/api';
import { fetchEntityProfilePostsQueryI, fetchEntityProfilePostsResultI } from './types';

export const fetchEntityProfilePostsAPI = async (query: fetchEntityProfilePostsQueryI) => {
  const result = await apiCall<fetchEntityProfilePostsQueryI, fetchEntityProfilePostsResultI>(
    '/backend/api/v1/feed/entity-profile/posts',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
