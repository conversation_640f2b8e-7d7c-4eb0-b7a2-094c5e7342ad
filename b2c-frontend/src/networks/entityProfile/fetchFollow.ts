import { apiCall } from '@/src/services/api';
import { FetchFollowersResponseI } from '../connect/types';
import { fetchEntityProfileFollowersQueryI } from './types';

export const fetchEntityProfileFollowersAPI = async (query: fetchEntityProfileFollowersQueryI) => {
  const result = await apiCall<fetchEntityProfileFollowersQueryI, FetchFollowersResponseI>(
    '/backend/api/v1/company/network/followers',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const fetchEntityProfileFollowingsAPI = async (query: fetchEntityProfileFollowersQueryI) => {
  const result = await apiCall<fetchEntityProfileFollowersQueryI, FetchFollowersResponseI>(
    '/backend/api/v1/company/network/followers',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
