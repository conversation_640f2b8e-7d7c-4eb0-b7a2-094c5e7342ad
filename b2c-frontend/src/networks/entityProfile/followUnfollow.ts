import { apiCall } from '@/src/services/api';
import { EntityProfileFollowQueryI, EntityProfileUnfollowQueryI } from './types';

export const entityProfileFollowAPI = async (payload: EntityProfileFollowQueryI) => {
  await apiCall('/backend/api/v1/company/entity-profile/follow', 'POST', {
    isAuth: true,
    payload,
  });
};

export const entityProfileUnfollowAPI = async (query: EntityProfileUnfollowQueryI) => {
  await apiCall('/backend/api/v1/company/entity-profile/unfollow', 'DELETE', {
    isAuth: true,
    query,
  });
};
