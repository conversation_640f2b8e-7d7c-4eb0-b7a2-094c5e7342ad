/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  CreateEntityProfileBodyI,
  CreateEntityProfileResultI,
  sendOTPForEntityProfileEmailVerificationBodyI,
  verifyOTPForEntityProfileEmailVerificationBodyI,
} from './types';

export const createEntityProfileAPI = async (
  payload: CreateEntityProfileBodyI,
): Promise<CreateEntityProfileResultI> => {
  const result = await apiCall<CreateEntityProfileBodyI, CreateEntityProfileResultI>(
    '/backend/api/v1/company/entity-profile',
    'POST',
    { isAuth: true, payload },
  );

  return result;
};

export const sendOTPForEntityProfileEmailVerificationAPI = async (
  payload: sendOTPForEntityProfileEmailVerificationBodyI,
) => {
  const result = await apiCall<sendOTPForEntityProfileEmailVerificationBodyI, undefined>(
    '/backend/api/v1/company/entity-profile/email/otp',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const verifyOTPForEntityProfileEmailVerificationAPI = async (
  payload: verifyOTPForEntityProfileEmailVerificationBodyI,
) => {
  const result = await apiCall<verifyOTPForEntityProfileEmailVerificationBodyI, unknown>(
    '/backend/api/v1/company/entity-profile/email/otp',
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
