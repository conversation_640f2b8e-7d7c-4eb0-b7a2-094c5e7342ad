import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, IdTypeI } from '@/src/types/common/data';
import { postCodePlaceDetail } from '@/src/screens/EditAnnouncement/components/EditAnnouncement/types';

export type fetchPeopleBodyI = {
  latitude: number;
  longitude: number;
};

export type fetchPeopleResultDataI = {
  id: string;
  name: string;
  avatar: string | null;
  cursorId?: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
  distanceInMeters: number;
  nextCursorId?: string;
  latitude: string;
  longitude: string;
};

export type fetchPeopleResultI = {
  data: fetchPeopleResultDataI[];
  nextCursorId: null | string;
};

export type fetchPeopleQueryI = {
  cursorId: number | null;
  pageSize: number;
};

export type addAnnouncementBodyI = {
  title: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  description: string;
  latitude?: number;
  longitude?: number;
  addressMapBox?: postCodePlaceDetail;
  city?: IdTypeI;
  cityMapBox?: IdNameI;
  countryIso2?: string;
  postCodeMapBox?: postCodePlaceDetail;
  port?: portForCreateAnnouncementI;
};

export type portForCreateAnnouncementI = {
  unLocode: string;
  name: string;
  dataType: string;
  latitude: number;
  longitude: number;
};

export type fetchAnnoucementsBodyI = {
  coordinates: {
    longitude: number;
    latitude: number;
  }[];
  radius: number;
};

export type attendEventBodyI = {
  id: string;
  status: string;
};

export type fetchAnnoucementI = {
  addressRawData: {
    id: string;
    text: string;
  };
  announcementId: string;
  attendeesSummary: {
    topAttendees: [];
    othersCount: 0;
  };
  cityId: string;
  cityRawDataId: string;
  countryIso2: string;
  createdAt: string;
  cursorId: number;
  description: string;
  distanceInMeters: string;
  endDate: string;
  endTime: string;
  isRSVPed: boolean;
  latitude: number;
  longitude: number;
  profile: {
    id: string;
    name: string;
    avatar: null;
    designation: SearchResultI;
    entity: null | SearchResultI;
  };
  entityProfile: {
    id: string;
    name: string;
    avatar: string | null;
  } | null;
  startDate: string;
  startTime: string;
  title: string;
  totalAttendees: number;
  updatedAt: string;
  topAttendeesRaw:
    | {
        id: string;
        name: string;
        avatar: null | string;
      }[]
    | null;
};

export type fetchAnnoucementsResultI = {
  data: fetchAnnoucementI[];
  nextCursorId: null | number;
};

export type fetchPeopleAttendingQueryI = {
  cursorId: number | null;
  pageSize: number;
  id: string;
};

export type fetchPeopleAttendingResultDataI = {
  avatar: string;
  designation: SearchResultI;
  entity: SearchResultI | null;
  id: string;
  name: string;
};

export type fetchPeopleAttendingResultI = {
  data: fetchPeopleAttendingResultDataI[];
  nextCursorId: string;
};
