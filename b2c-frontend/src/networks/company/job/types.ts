export type ApplicationStatusI =
    | 'PENDING'
    | 'WITHDREW'
    | 'ACCEPTED_BY_APPLICANT';

export type UpsertApplicantApplicationPayloadI = {
    jobId: string;
    status: ApplicationStatusI;
    applicationId?: string;
};

export type UpsertApplicantApplicationResultI = { id: string };

export type JobEntityI = { id?: string; name?: string };
export type JobDesignationI = { id?: string; name?: string };

export type JobCandidateFetchOneResultI = {
    id: string;
    entity?: JobEntityI | null;
    designation?: JobDesignationI | null;
    matching?: number;
    createdAt?: string;
    applicationStatus?: string | null;
};

export type EntityMemberApplicationStatusI = 'SHORTLISTED' | 'REJECTED_BY_ENTITY' | 'OFFERED';
export type UpdateEntityMemberApplicationStatusPayloadI = {
    applicationId: string;
    status: EntityMemberApplicationStatusI;
};