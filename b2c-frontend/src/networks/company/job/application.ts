import { apiCall } from '@/src/services/api';
import { UpdateEntityMemberApplicationStatusPayloadI, UpsertApplicantApplicationPayloadI, UpsertApplicantApplicationResultI } from './types';

export const updateJobApplication = async (
  payload: UpsertApplicantApplicationPayloadI,
): Promise<UpsertApplicantApplicationResultI> => {
  const result = await apiCall<UpsertApplicantApplicationPayloadI, UpsertApplicantApplicationResultI>(
    '/backend/api/v1/company/job/application/applicant',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return result;
};



export const updateApplicationStatus = async (
  payload: UpdateEntityMemberApplicationStatusPayloadI,
): Promise<void> => {
  await apiCall<UpdateEntityMemberApplicationStatusPayloadI, void>(
    `/backend/api/v1/company/job/application/entity-member/${payload.applicationId}`,
    'PATCH',
    { isAuth: true, payload },
  );
};
