import { CommunityMemberI } from '@/src/redux/slices/community/types';
import { apiCall } from '@/src/services/api';

export type UpdateCommunityPayload = {
  name?: string;
  description?: string | null;
  access?: 'PUBLIC' | 'PRIVATE';
  isRestricted?: boolean;
  members?: CommunityMemberI[];
  avatar?: { opr: 'CREATE' | 'UPDATE' | 'DELETE'; fileUrl?: string };
};

export type UpdateCommunityParams = { id: string };

export const updateCommunityAPI = async (
  params: UpdateCommunityParams,
  payload: UpdateCommunityPayload,
) => {
  return await apiCall<UpdateCommunityPayload, unknown>(
    '/backend/api/v1/forum/community',
    'PATCH',
    {
      isAuth: true,
      payload,
      query: params,
    },
  );
};

export const deleteCommunityAPI = async (params: UpdateCommunityParams) => {
  return await apiCall<undefined, unknown>('/backend/api/v1/forum/community', 'DELETE', {
    isAuth: true,
    query: params,
  });
};
