import { apiCall } from '@/src/services/api';

export type CommunityMemberFetchForClientI = {
  profileId: string;
  communityId: string;
};

export const fetchCommunityMemberForClient = async (
  query: CommunityMemberFetchForClientI,
): Promise<{ communityId: string; profileId: string; type: string }> => {
  return await apiCall<
    CommunityMemberFetchForClientI,
    { communityId: string; profileId: string; type: string }
  >('/backend/api/v1/forum/community-member', 'GET', {
    isAuth: true,
    query,
  });
};

export const leaveCommunityAPI = async (query: CommunityMemberFetchForClientI): Promise<void> => {
  await apiCall<unknown, void>('/backend/api/v1/forum/community-member', 'DELETE', {
    isAuth: true,
    query,
  });
};
