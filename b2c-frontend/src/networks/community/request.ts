/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  CommunityRequestApprovalBodyI,
  CommunityRequestApprovalQueryI,
  CommunityRequestCreatePayloadI,
  CommunityRequestCreateResultI,
  CommunityRequestListQueryI,
  CommunityRequestListResponseI,
  CommunityRequestRejectionQueryI,
  CommunityRequestRevocationQueryI,
} from './types';

export const createCommunityRequestAPI = async (
  payload: CommunityRequestCreatePayloadI,
): Promise<CommunityRequestCreateResultI> => {
  return apiCall<CommunityRequestCreatePayloadI, CommunityRequestCreateResultI>(
    '/backend/api/v1/forum/community-request',
    'POST',
    { isAuth: true, payload },
  );
};

export const revokeCommunityRequestAPI = async (
  query: CommunityRequestRevocationQueryI,
): Promise<void> => {
  await apiCall<unknown, void>('/backend/api/v1/forum/community-request-revocation', 'POST', {
    isAuth: true,
    query,
  });
};

export const fetchCommunityRequestsAPI = async (
  query: CommunityRequestListQueryI,
): Promise<CommunityRequestListResponseI> => {
  return await apiCall<undefined, CommunityRequestListResponseI>(
    '/backend/api/v1/forum/community-requests',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
};

export const approveCommunityRequestAPI = async (
  query: CommunityRequestApprovalQueryI,
  payload: CommunityRequestApprovalBodyI,
): Promise<{ status: 'PENDING' | 'ACCEPTED' | 'PARTIALLY_ACCEPTED' | 'REJECTED' | 'REVOKED' }> => {
  return await apiCall<
    CommunityRequestApprovalBodyI,
    { status: 'PENDING' | 'ACCEPTED' | 'PARTIALLY_ACCEPTED' | 'REJECTED' | 'REVOKED' }
  >('/backend/api/v1/forum/community-request-approval', 'POST', {
    isAuth: true,
    query,
    payload,
  });
};

export const rejectCommunityRequestAPI = async (
  query: CommunityRequestRejectionQueryI,
): Promise<void> => {
  await apiCall<undefined, void>('/backend/api/v1/forum/community-request-rejection', 'DELETE', {
    isAuth: true,
    query,
  });
};
