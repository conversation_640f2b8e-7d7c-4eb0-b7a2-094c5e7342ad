import { apiCall } from '@/src/services/api';

export type CommunityMembersQueryI = {
  communityId: string;
  cursorId?: number | null;
  pageSize?: number;
  type?: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER' | 'ALL';
  search?: string;
};

export type CommunityMemberListItemI = {
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designationText: string | null;
    designationAlternativeId: string | null;
    designationRawDataId: string | null;
    entityText: string | null;
    entityId: string | null;
    entityRawDataId: string | null;
  };
  role: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
};

export type CommunityMembersResultI = {
  total: number;
  nextCursorId: number | null;
  data: CommunityMemberListItemI[];
};

export const fetchCommunityMembersAPI = async (
  query: CommunityMembersQueryI,
): Promise<CommunityMembersResultI> => {
  return await apiCall<unknown, CommunityMembersResultI>(
    '/backend/api/v1/forum/community-members',
    'GET',
    { isAuth: true, query },
  );
};

export type UpdateMemberRolePayloadI = {
  communityId: string;
  profileId: string;
  type: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
};

export const updateCommunityMemberRoleAPI = async (payload: UpdateMemberRolePayloadI) => {
  return await apiCall<
    UpdateMemberRolePayloadI,
    {
      action: 'created' | 'updated';
      member: { profileId: string; type: UpdateMemberRolePayloadI['type'] };
      communityId: string;
    }
  >('/backend/api/v1/forum/community-member', 'PATCH', { isAuth: true, payload });
};

export const removeCommunityMemberAPI = async (query: {
  communityId: string;
  profileId: string;
}): Promise<void> => {
  await apiCall<unknown, void>('/backend/api/v1/forum/community-member', 'DELETE', {
    isAuth: true,
    query,
  });
};
