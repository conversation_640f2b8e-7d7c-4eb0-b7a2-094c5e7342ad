import { CommunityI } from '@/src/screens/MyCommunities/components/CommunityList/types';
import { apiCall } from '@/src/services/api';
import { FetchCommunityClientI } from './types';

export const fetchCommunityById = async (id: string): Promise<CommunityI> => {
  const result = await apiCall<undefined, CommunityI>(
    `/backend/api/v1/forum/community/${id}`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};
