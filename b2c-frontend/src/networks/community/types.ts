export type CommunityFetchForClientI = {
  name?: string | null;
  cursorId?: number | null;
  pageSize?: number;
};

export type FetchCommunityClientI = {
  id: string;
  cursorId: string;
  name: string;
  memberCount: number;
  questionCount: number;
  description: string | null;
  avatar: string | null;
};

export type CommunityRequestCreatePayloadI = {
  communityId: string;
  profileId: string;
  requestedType: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
};

export type CommunityRequestCreateResultI = {
  status: 'PENDING' | 'ACCEPTED' | 'PARTIALLY_ACCEPTED' | 'REJECTED' | 'REVOKED';
};

export type CommunityRequestRevocationQueryI = {
  communityId: string;
  profileId: string;
};

export type CommunityRequestListQueryI = {
  communityId: string;
  page?: number;
  pageSize?: number;
};

export type CommunityRequestListItemI = {
  status: 'PENDING' | 'ACCEPTED' | 'PARTIALLY_ACCEPTED' | 'REJECTED' | 'REVOKED';
  requestedType: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designationText: string | null;
    designationAlternativeId: string | null;
    designationRawDataId: string | null;
    entityText: string | null;
    entityId: string | null;
    entityRawDataId: string | null;
  };
};

export type CommunityRequestListResponseI = {
  total: number;
  data: CommunityRequestListItemI[];
};

export type CommunityRequestApprovalQueryI = {
  communityId: string;
  profileId: string;
};

export type CommunityRequestApprovalBodyI = {
  acceptedType: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';
};

export type CommunityRequestRejectionQueryI = CommunityRequestApprovalQueryI;
