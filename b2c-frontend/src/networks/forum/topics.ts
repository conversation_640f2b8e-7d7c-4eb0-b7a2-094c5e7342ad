/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { TopicFetchParamsI, TopicFetchResponseI } from './types';

export const fetchTopicsAPI = async (
  params: TopicFetchParamsI = {},
): Promise<TopicFetchResponseI> => {
  const result = await apiCall<undefined, TopicFetchResponseI>(
    '/backend/api/v1/forum/topic/options',
    'GET',
    {
      isAuth: true,
      query: params,
    },
  );

  return result;
};
