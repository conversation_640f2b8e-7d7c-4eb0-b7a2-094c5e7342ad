/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { ForumExploreOptionsI, ForumExploreResponseI } from './types';

export const fetchForumExploreAPI = async (
  query?: ForumExploreOptionsI,
): Promise<ForumExploreResponseI> => {
  const result = await apiCall<undefined, ForumExploreResponseI>(
    '/backend/api/v1/forum/explore',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
