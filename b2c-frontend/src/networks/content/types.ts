import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';
import { EntityProfileBaseI, fetchEntityProfilesForUsersResultI } from '../entityProfile/types';

export type FileExtensionI = 'jpeg' | 'jpg' | 'webp' | 'pdf';

export type ParentFolderI = 'AVATAR' | 'CERTIFICATION' | 'DOCUMENTATION' | 'PORT' | 'POST' | 'SHIP';

export interface AuthorProfileI {
  avatar: string | null;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
  id: string;
  name: string;
}

export interface CommentCreateI {
  parentCommentId?: string;
  postId: string;
  text: string;
}

export interface CommentCreateResponseI {
  id: string;
  cursorId: number;
}

export interface CommentItemI {
  createdAt: string;
  updatedAt?: string;
  cursorId: number | null;
  id: string;
  Profile: AuthorProfileI;
  EntityProfile?: EntityProfileBaseI | null;
  profile?: AuthorProfileI;
  replies?: CommentItemI[];
  repliesCount?: number;
  text: string;
}

export interface GetCommentsPayloadI extends ObjUnknownI {
  cursorId: number | null;
  id?: string;
  pageSize?: number;
  postId: string;
}

export interface GetCommentsRepliesPayloadI {
  cursorId: number | null;
  pageSize: number;
  parentCommentId: string;
  postId: string;
}

export type GetCommentsResponseI = {
  comments: CommentItemI[];
  cursorId: number | null;
  total?: number;
};

export interface GetPostsPayloadI extends ObjUnknownI {
  cursorId: string | null;
  otherCursorId?: string;
  pageSize?: string;
  profileId?: string;
  refresh?: boolean;
}

export interface PaginationI {
  cursorId: number | null;
  hasMore: boolean;
  otherCursorId?: number | null;
}

export interface PostCreateOneI {
  caption?: string;
  files?: PostMediaI[];
  entityProfileId?: string;
}

export interface PostCreateResponse {
  caption: string;
  createdAt: Date;
  id: string;
  Media: PostMediaI[];
  profileId: string;
  updatedAt: Date;
}

export interface PostExternalClientI {
  caption: string;
  createdAt: string;
  cursorId?: number;
  id: string;
  isLiked: boolean;
  Media: PostMediaI[];
  Profile?: AuthorProfileI;
  EntityProfile?: fetchEntityProfilesForUsersResultI;
  reactionsCount: number;
  isCaptionTruncated: boolean;
  totalCommentsCount: number;
}

export interface PostFetchManyResultI {
  cursorId?: number;
  otherCursorId?: number;
  posts: PostExternalClientI[];
}

export interface PostMediaI {
  caption?: string | null;
  createdAt?: Date;
  extension: string;
  fileUrl: string;
  id?: string;
  postId?: string;
  profileId?: string;
  updatedAt?: Date;
}

export interface ReactionDeleteResponseI {
  postId: string;
}

export interface ReactionDetail {
  postId: string;
  Profile: AuthorProfileI;
  EntityProfile: EntityProfileBaseI | null;
  reactionType: string;
}

export interface ReactionExternalClientI {
  reactions: ReactionDetail[];
  totalCount: number;
}

export interface ReactionFetchManyI extends ObjUnknownI {
  page?: number;
  pageSize?: number;
  postId: string;
}

export interface ReactionPostIdI extends ObjUnknownI {
  postId: string;
}

export interface ReactionUpsertI {
  postId: string;
  reactionType: string;
}

export interface ReactionUpsertResponseI {
  postId: string;
}

export interface PostCaptionResponse {
  caption: string;
}
