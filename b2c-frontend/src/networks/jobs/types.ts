import { ProfileItemI } from '@/src/components/Announcement/types';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameTypeI, IdTypeI } from '@/src/types/common/data';
import { ProfileExternalI } from '../answerVote/types';

export type fetchJobsForCandidateQueryI = {
  entity?: IdTypeI;
  isOfficial?: boolean;
  status?: string;
  pageSize: number;
  cursorId: string | null;
};

export type fetchJobsForCandidateBodyI = {
  designations?: IdTypeI[] | null,
  countries?: string[] | null,
  shipTypes?: string[] | null,
};

export type fetchJobsForCandidateResultI = {
  data:{
    id: string;
    cursorId: string;
    isUrgent: boolean;
    entity: IdNameTypeI;
    designation: IdNameTypeI;
    department: IdNameTypeI;
    ship: Omit<SearchResultI, 'id'> & { imo: string };

    minYears: number;
    maxYears?: number | null;
    minSalary: number;
    maxSalary?: number | null;
    status?: string | null;
    expiryDate?: string;
    isOfficial: boolean;
    createdAt: string;
    applicationStatus?: string | null;
    creator: ProfileExternalI;

    matching?: number | null;
  }[];
  nextCursorId: null | string
};

export type fetchJobsForApplicantQueryI = {
  cursorId: string | null;
  pageSize: number;
  status: string;
};

export type fetchJobsForApplicantBodyI = {
  designations?: IdTypeI[] | null,
  shipTypes?: string[] | null,
};

export type fetchJobsForApplicantsResultI = {
  data:{
    id: string;
    cursorId: string | null;
    status: string;
    matching: number;
    designation: IdNameTypeI;
    entity: IdNameTypeI;
  }[];
  nextCursorId: null | string;
};

export type fetchApplicantsForJobPostsQueryI = {
  jobId: string;
  status: string;
  cursorId: string | null;
  pageSize: number;
};

export type fetchApplicantsForJobPostsResultI = {
  id: string;
  cursorId: string | null;
  matching: number;
  status: string;
  ApplicantProfile: ProfileItemI;
  DecisionMakerProfile: ProfileItemI;
};

export type FiltersForJobsResultI = {
  
}
