/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { fetchJobsForCandidateBodyI, fetchJobsForCandidateQueryI, fetchJobsForCandidateResultI } from './types';

export const fetchJobsForCandidatesAPI = async (
  query: fetchJobsForCandidateQueryI,
  payload:fetchJobsForCandidateBodyI
): Promise<fetchJobsForCandidateResultI> => {
  const result = await apiCall<fetchJobsForCandidateBodyI, fetchJobsForCandidateResultI>(
    '/backend/api/v1/company/job/candidate',
    'POST',
    {
      isAuth: true,
      query,
      payload
    },
  );

  return result;
};
