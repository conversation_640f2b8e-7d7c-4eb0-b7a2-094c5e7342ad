import { apiCall } from '@/src/services/api';
import { fetchApplicantsForJobPostsQueryI, fetchApplicantsForJobPostsResultI } from './types';

export const fetchApplicantsForJobPostsAPI = async (
  query: fetchApplicantsForJobPostsQueryI,
): Promise<fetchApplicantsForJobPostsResultI[]> => {
  const result = await apiCall<
    fetchApplicantsForJobPostsQueryI,
    fetchApplicantsForJobPostsResultI[]
  >('/backend/api/v1/company/job/application/entity-member', 'GET', {
    isAuth: true,
    query,
  });

  return result;
};
