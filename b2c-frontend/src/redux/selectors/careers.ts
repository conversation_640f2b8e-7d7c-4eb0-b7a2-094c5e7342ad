/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RootState } from '../store';

export const selectFilters = (page: string, filter: string) => (state: RootState) => {
  const filterData = state.career[page]?.[filter] || { values: [], selectedValues: [] };
  
  const allValues = [
    ...filterData.selectedValues.map(value => ({
      ...value,
      isSelected: true
    })),
    ...filterData.values.map(value => ({
      ...value,
      isSelected: false
    }))
  ];
  return allValues;
};

export const selectActiveFilterNumbers = (page: string) => (state: RootState) => {
  const pageFilters = state.career[page] || {};
  
  const result: Record<string, number> = {};
  
  Object.entries(pageFilters).forEach(([filterName, filterData]) => {
    result[filterName] = filterData?.selectedValues?.length;
  });
  
  return result;
};

export const selectActiveFilters = (page: string) => (state: RootState) => {
  const pageFilters = state.career[page] || {};
  
  const result: Record<string, any> = {};
  
  Object.entries(pageFilters).forEach(([filterName, filterData]) => {
    const selectedValues = filterData?.selectedValues || [];
    
    switch (filterName) {
      case 'designations':
        result[filterName] = selectedValues.map((designation: any) => ({
          id: designation.id,
          dataType: designation.dataType
        }));
        break;
      
      case 'locations':
        result[filterName] = selectedValues.map((location: any) => 
          typeof location === 'string' ? location : location.id || location.code
        );
        break;
      
      case 'shipTypes':
        result[filterName] = selectedValues.map((shipType: any) => 
          typeof shipType === 'string' ? shipType : shipType.id
        );
        break;
      
      case 'benefits':
        result[filterName] = selectedValues.map((benefit: any) => ({
          id: benefit.id,
        }));
        break;
      
      case 'yearOfExperiences':
        result[filterName] = selectedValues.map((experience: any) => 
          typeof experience === 'string' ? experience : experience.value || experience.id
        );
        break;
      
      default:
        result[filterName] = selectedValues;
    }
  });
  
  return result;
};