/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RootState } from '../store';

export const selectEntityProfileUI = (state: RootState) => state.entityProfileUI;
export const selectEntityProfileActiveTab = (state: RootState) => state.entityProfileUI.activeTab;
export const SelectLastVisitedEntityProfileId = (state: RootState) =>
  state.entityProfileUI.lastVisitedEntityProfileId;

export const selectCurrentEntityProfile = (state: RootState) => state.entityProfile;
