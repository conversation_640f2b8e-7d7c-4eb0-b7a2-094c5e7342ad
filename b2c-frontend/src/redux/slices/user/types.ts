/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { SearchResultI } from '../entitysearch/types';

export type FetchProfileResultI = {
  email: string;
  name: string;
  username: string;
  avatar: string | null;
  profileId: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
  country: SearchResultI | null;
  description: string | null;
};

export interface UserState {
  username: string;
  email: string;
  profileId: string;
  fullName: string;
  gender: string;
  avatar: string | null;
  country?: SearchResultI;
  organisation?: SearchResultI;
  designation?: SearchResultI;
  isUsernameSaved: boolean;
  isEmailVerified: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  isAuthenticated: boolean;
  isPrivacyPolicyAccepted: boolean;
  token: string;
  jwtToken: string;
  loading: boolean;
  error: string | null;
  pendingVerificationEmail?: string;
  forgotPasswordEmail?: string;
  isOTPVerified: boolean;
  previousStatus?: 'ACTIVE' | 'INACTIVE' | 'SCHEDULED_FOR_DELETION' | 'BLOCKED' | 'DELETED';
  description: string | null;
  referralCode: string;
  referredByCode: string;
  isReferred: boolean;
  isActive: boolean;
}

export interface LoginResponse {
  name: string;
  email: string;
  username: string;
  profileId: string;
  avatar: string | null;
  isEmailVerified: boolean;
  isUsernameSaved: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  isPrivacyPolicyAccepted: boolean;
  token: string;
  jwtToken: string;
  previousStatus: 'ACTIVE' | 'INACTIVE' | 'SCHEDULED_FOR_DELETION' | 'BLOCKED' | 'DELETED';
  referralCode: string;
}

export interface RegisterResponse {
  profileId: string;
  token: string;
}

export interface SendOTPForPasswordResetBodyI {
  email: string;
}

export interface VerifyOTPForPasswordResetBodyI {
  email: string;
  otp: string;
}

export interface ResetPasswordBodyI {
  email: string;
  newPassword: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  message: string;
}
