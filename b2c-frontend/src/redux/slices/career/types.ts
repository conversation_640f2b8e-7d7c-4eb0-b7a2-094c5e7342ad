export type CareerPage = 'jobs' | 'myJobs' | 'jobPosts' | 'applicants';
export type FilterCategory = 'locations' | 'designations' | 'benefits' | 'shipTypes' | 'yearOfExperiences';

export type CareerState = {
  jobs: {
    locations: {
        values:[],
        selectedValues:[]
    };
    designations: {
        values:[],
        selectedValues:[]
    };
    benefits: {
        values:[],
        selectedValues:[]
    };
    shipTypes: {
        values:[],
        selectedValues:[]
    };
  };
  myJobs: {
    locations: {
        values:[],
        selectedValues:[]
    };
    designations: {
        values:[],
        selectedValues:[]
    };
    benefits: {
        values:[],
        selectedValues:[]
    };
    shipTypes: {
        values:[],
        selectedValues:[]
    };
  };
  jobPosts: {
    designations: {
        values:[],
        selectedValues:[]
    };
    shipTypes: {
        values:[],
        selectedValues:[]
    };
  };
  applicants: {
    locations: {
        values:[],
        selectedValues:[]
    };
    designations: {
        values:[],
        selectedValues:[]
    };
    yearOfExperiences: {
        values:[],
        selectedValues:[]
    };
  };
};


export type IdLabelCountDataTypeI = {
    id:string;
    label:string
    count:number;
    dataType:string
}

export type setFilterPayload = {
    page:CareerPage;
    filters:{
        locations?:IdLabelCountDataTypeI[];
        designations?:IdLabelCountDataTypeI[];
        benefits?: IdLabelCountDataTypeI[];
        shipTypes?: IdLabelCountDataTypeI[];
    }
}

export type toggleFilterPayload = {
    page: string;
    filter: string;
    value: IdLabelCountDataTypeI;
    selected: boolean;
}