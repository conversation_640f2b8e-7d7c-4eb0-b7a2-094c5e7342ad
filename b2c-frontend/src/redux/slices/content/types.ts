import type { SearchResultI } from '@/src/screens/EntitySearch/components/SearchScreen/types';
import type {
  PostExternalClientI,
  GetCommentsResponseI,
  ReactionExternalClientI,
  CommentItemI,
} from '@/src/networks/content/types';
import { EntityProfileBaseI } from '@/src/networks/entityProfile/types';
import type {
  ScrapBookPostFetchForClientI,
  ScrapBookCommentFetchForClientI,
  ScrapBookCommentFetchManyResultI,
} from '@/src/networks/port/types';

export interface PaginationState {
  cursorId: string | null;
  otherCursorId: number | undefined;
  hasMore: boolean;
}

export interface SimplePaginationState {
  cursorId: string | null;
  hasMore: boolean;
}

export interface ProfilePostsCollection {
  posts: PostExternalClientI[];
  pagination: SimplePaginationState;
}

export interface DeletedPostData {
  post: PostExternalClientI;
  reactions: ReactionExternalClientI;
  comments: GetCommentsResponseI;
}

export interface DeletedScrapbookPostData {
  post: ScrapBookPostFetchForClientI;
}

export interface ScrapbookPaginationState {
  page: number;
  hasMore: boolean;
  total: number;
}

export interface UserI {
  profileId: string;
  fullName: string;
  avatar: string;
  designation: SearchResultI;
  organisation: SearchResultI;
}

export interface CommentReplyPagination {
  cursorId: number | null;
  hasMore: boolean;
  total: number;
}

export interface ExtendedPostI extends PostExternalClientI {
  isScrapbookPost: boolean;
  reactionCount?: number;
  commentCount?: number;
  textPreview?: string;
}

export interface ContentState {
  posts: PostExternalClientI[];
  cachedPosts: PostExternalClientI[];
  post: ExtendedPostI | null;
  pagination: PaginationState;
  ownPosts: PostExternalClientI[];
  popularPosts: PostExternalClientI[];
  ownPostsPagination: SimplePaginationState;
  profilePosts: Record<string, ProfilePostsCollection>;
  reactions: Record<string, ReactionExternalClientI>;
  comments: Record<string, GetCommentsResponseI>;
  commentReplies: Record<
    string,
    { comments: CommentItemI[]; cursorId: number | null; total: number }
  >;
  deletedPosts: Record<string, DeletedPostData>;
  deletedScrapbookPosts: Record<string, DeletedScrapbookPostData>;
  scrapbookPosts: ScrapBookPostFetchForClientI[];
  scrapbookPagination: ScrapbookPaginationState;
  scrapbookComments: Record<string, ScrapBookCommentFetchManyResultI>;
  scrapbookCommentReplies: Record<
    string,
    { comments: ScrapBookCommentFetchForClientI[]; cursorId: number | null; total: number }
  >;
  searchPosts: Record<string, PostExternalClientI[]>;
  scrapbookReactions: Record<string, ReactionExternalClientI>;
}

export type ReactionPayload = {
  postId: string;
};

export type CommentPayload = {
  postId: string;
  text: string;
  tempId: string;
  parentCommentId?: string;
  user: UserI;
  entityProfile?: EntityProfileBaseI;
};

export type CommentActionPayload = {
  postId: string;
  commentId: string;
  parentCommentId?: string;
};

export type FetchRepliesPayload = {
  postId: string;
  parentCommentId: string;
  cursorId: number | null;
  pageSize?: number;
};

export type ScrapbookCommentPayload = {
  scrapbookPostId: string;
  text: string;
  tempId: string;
  parentCommentId?: string;
  user: UserI;
};

export type ScrapbookCommentActionPayload = {
  scrapbookPostId: string;
  commentId: string;
  parentCommentId?: string;
};

export type FetchScrapbookRepliesPayload = {
  scrapbookPostId: string;
  parentCommentId: string;
  cursorId: number | null;
  pageSize?: number;
};

export type DeletePostPayload = {
  post: PostExternalClientI;
};

export type RevertDeletePayload = {
  postId: string;
};
