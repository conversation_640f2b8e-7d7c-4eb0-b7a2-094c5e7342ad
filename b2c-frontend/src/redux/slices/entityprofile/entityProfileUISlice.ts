/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { EntityProfileTabId, EntityProfileUIStateI } from './types';

const initialState: EntityProfileUIStateI = {
  activeTab: 'about',
  lastVisitedEntityProfileId: '',
};

const profileUiSlice = createSlice({
  name: 'entityProfileUI',
  initialState,
  reducers: {
    setEntityProfileActiveTab(state, action: PayloadAction<EntityProfileTabId>) {
      state.activeTab = action.payload;
    },
    setLastVisitedEntityProfileId(state, action: PayloadAction<string>) {
      state.lastVisitedEntityProfileId = action.payload;
    },
    resetEntityProfileUI(state) {
      state.activeTab = 'about';
      state.lastVisitedEntityProfileId = undefined;
    },
  },
});

export const { setEntityProfileActiveTab, resetEntityProfileUI, setLastVisitedEntityProfileId } =
  profileUiSlice.actions;
export default profileUiSlice.reducer;
