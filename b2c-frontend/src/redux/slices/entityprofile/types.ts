export type EntityProfileStateI = {
  entityProfileId: string;
  name: string;
  avatar: string | null;
  description: string | null;
  overview: string | null;
  foundedAt: string;
  website: string | null;
  loading: boolean;
  error: string | null;
  isActive: boolean;
  role: string;
};

export type EntityProfileTabId = 'about' | 'posts';

export type EntityProfileUIStateI = {
  activeTab: EntityProfileTabId;
  lastVisitedEntityProfileId?: string;
};

export type EntityProfileI = {
  id: string;
  name: string;
  avatar: string | null;
  isVerified: boolean;
  role: string;
};
