import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';

type PropsI = {
  width?: number;
  height?: number;
  fill?: string;
};

const Plus = ({ width = 2.1, height = 2.1, fill = 'none', ...props }: PropsI) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 16 16"
      fill={fill}
      {...props}
    >
      <Path
        d="M16 8a.666.666 0 01-.667.667H8.667v6.666a.667.667 0 11-1.334 0V8.667H.667a.667.667 0 010-1.334h6.666V.667a.667.667 0 011.334 0v6.666h6.666A.667.667 0 0116 8z"
        fill={fill}
      />
    </Svg>
  );
};

export default Plus;
