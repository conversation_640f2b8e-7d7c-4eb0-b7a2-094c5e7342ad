import * as React from "react"
import { RFPercentage } from "react-native-responsive-fontsize"
import Svg, { Path } from "react-native-svg"

export const Match = ({ width = 2.2, height = 2.2 }: { width?: number, height?: number }) => {
    return (
        <Svg
            width={RFPercentage(width)}
            height={RFPercentage(height)}
            viewBox="0 0 16 16"
            fill="none"
        >
            <Path
                d="M6.912 5.566a3.5 3.5 0 011.531.352L7.153 7.21a1.9 1.9 0 00-2.138 1.88c0 1.045.852 1.896 1.897 1.896A1.898 1.898 0 008.79 8.848l1.291-1.292a3.5 3.5 0 01.354 1.533 3.527 3.527 0 01-3.523 3.523A3.528 3.528 0 013.39 9.089a3.528 3.528 0 013.523-3.523z"
                fill="#448600"
                stroke="#F6F6F6"
                strokeWidth={0.2}
            />
            <Path
                d="M6.911 2.277c1.46 0 2.814.463 3.924 1.249L9.662 4.699a5.152 5.152 0 00-2.75-.795 5.19 5.19 0 00-5.185 5.184 5.19 5.19 0 005.184 5.185 5.19 5.19 0 005.185-5.185 5.15 5.15 0 00-.796-2.752l1.174-1.172a6.772 6.772 0 011.249 3.924A6.82 6.82 0 016.91 15.9 6.82 6.82 0 01.1 9.088 6.82 6.82 0 016.91 2.277z"
                fill="#448600"
                stroke="#F6F6F6"
                strokeWidth={0.2}
            />
            <Path
                d="M13.791.113c.1.032.175.117.193.221l.237 1.364.012.07.07.011 1.363.238a.282.282 0 01.22.192h.002c.031.1.004.21-.07.285L14.762 3.55a.283.283 0 01-.2.083h-1.615L7.222 9.358a.41.41 0 01-.58-.579l5.727-5.727V1.437c0-.074.03-.147.083-.2L13.506.183h0a.282.282 0 01.285-.07z"
                fill="#448600"
                stroke="#F6F6F6"
                strokeWidth={0.2}
            />
        </Svg>
    )
}

export default Match 
