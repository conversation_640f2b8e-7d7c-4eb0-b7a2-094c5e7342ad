import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { G, <PERSON>, Defs, ClipPath } from 'react-native-svg';

const UserRole = ({
  width = 3,
  height = 3,
  color,
}: {
  width: number;
  height: number;
  color?: string;
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 28 28"
      fill={color ?? 'none'}
    >
      <G clipPath="url(#clip0_6148_2339)">
        <Path
          d="M12.627 17.462a6.07 6.07 0 00-5.865 5.867H5.205a7.626 7.626 0 012.229-5.194 7.626 7.626 0 015.193-2.23v1.557zm6.071.486a1.669 1.669 0 100 3.337 1.669 1.669 0 000-3.337zm-5.87-14.766a5.669 5.669 0 015.67 5.67 5.67 5.67 0 01-5.67 5.671 5.669 5.669 0 01-5.67-5.67 5.668 5.668 0 015.67-5.67zm0 1.557a4.112 4.112 0 00-4.114 4.113 4.113 4.113 0 108.227 0 4.113 4.113 0 00-4.114-4.113zm5.092 18.01l-.144-.043a3.221 3.221 0 01-1.294-.748l-.108-.103-.129.075-.798.46-.778-1.348.797-.46.13-.074-.036-.144a3.238 3.238 0 010-1.496l.036-.145-.13-.075-.797-.46.778-1.349.798.461.129.075.108-.103a3.22 3.22 0 011.294-.748l.144-.042v-1.07h1.557v1.07l.142.042c.49.145.932.405 1.294.748l.108.103.13-.075.797-.46.779 1.347-.797.461-.13.075.035.145a3.237 3.237 0 010 1.495l-.***************.797.46-.779 1.348-.797-.46-.13-.075-.108.103a3.22 3.22 0 01-1.294.748l-.142.043v1.069H17.92v-1.07z"
          fill="#000"
          stroke="#fff"
          strokeWidth={0.4}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_6148_2339">
          <Path fill="#fff" d="M0 0H28V28H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default UserRole;
