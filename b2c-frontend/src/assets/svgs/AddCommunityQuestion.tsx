/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { FilledIconPropsI } from './types';
import Svg, { Defs, Path, SvgProps } from 'react-native-svg';

const AddCommunityQuestionIcon: React.FC<FilledIconPropsI> = ({
    width = 10,
    height = 10,
    fill = '#000',
    color,
    disabled,
    accessibilityLabel = 'Add Community Icon',
    ...props
}) => {
  const strokeColor = '#fff';
  const fillColor = strokeColor;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 1440 1440"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Defs></Defs>
      {/* Chat bubble with text lines */}
      <Path
        d="m 0,0 c 0,-6.6 -5.4,-12 -12,-12 h -93.553 c -6.6,0 -12,5.4 -12,12 v 4.886 c 0,6.6 5.4,12 12,12 H -12 c 6.6,0 12,-5.4 12,-12 z m 93.801,-13.642 c -47.428,0 -89.502,22.905 -115.779,58.251 h -84.423 c -6.6,0 -12,5.4 -12,12 v 4.834 c 0,6.6 5.4,12 12,12 h 67.777 c -1.702,3.942 -3.234,7.974 -4.586,12.087 -9.08,1.658 -15.868,6.514 -16.794,12.406 h -46.397 c -6.6,0 -12,5.401 -12,12 v 2.685 c 0,6.6 5.4,12 12,12 h 46.659 v 13.026 h -45.811 c -6.6,0 -12,5.4 -12,12 v 8.883 c 0,6.6 5.4,12 12,12 h 45.811 V 179 h -63.435 c -27.5,0 -50,-22.5 -50,-50 V -13.258 c 0,-24.591 17.997,-45.17 41.468,-49.251 l -3.905,-56.656 81.347,55.907 H 94.187 c 27.5,0 50,22.5 50,50 v 8.673 c -15.685,-5.851 -32.66,-9.057 -50.386,-9.057 z"
        fill="none"
        stroke={strokeColor}
        strokeWidth={6}
        strokeMiterlimit={10}
        transform="matrix(1.3333333,0,0,-1.3333333,690.38547,809.3868)"
      />
      {/* Plus sign - solid fill */}
      <Path
        d="m 0,0 h -39.106 v 39.106 c 0,4.736 -3.875,8.612 -8.612,8.612 v 0 c -4.736,0 -8.611,-3.876 -8.611,-8.612 V 0 h -39.106 c -4.737,0 -8.612,-3.875 -8.612,-8.612 0,-4.736 3.875,-8.612 8.612,-8.612 h 39.106 v -39.105 c 0,-4.737 3.875,-8.612 8.611,-8.612 v 0 c 4.737,0 8.612,3.875 8.612,8.612 v 39.105 H 0 c 4.736,0 8.612,3.876 8.612,8.612 C 8.612,-3.875 4.736,0 0,0"
        fill={fillColor}
        stroke="none"
        transform="matrix(1.3333333,0,0,-1.3333333,698.72947,590.67267)"
      />
      {/* People/Community icons */}
      <Path
        d="m 0,0 c 0,2.761 -2.238,5 -5,5 -8.283,0.002 -14.997,6.719 -14.995,15.002 0.002,8.284 6.719,14.998 15.003,14.995 6.837,-10e-4 12.807,-4.626 14.518,-11.245 0.69,-2.675 3.418,-4.284 6.093,-3.593 2.675,0.69 4.283,3.418 3.593,6.092 -3.454,13.368 -17.09,21.404 -30.457,17.95 -13.367,-3.453 -21.403,-17.089 -17.95,-30.456 1.211,-4.685 3.755,-8.918 7.323,-12.185 -6.792,-2.947 -12.696,-7.619 -17.125,-13.552 -1.655,-2.211 -1.205,-5.344 1.005,-6.999 2.208,-1.654 5.338,-1.207 6.994,0.999 6.111,8.215 15.76,13.039 25.998,12.999 2.762,0 5,2.238 5,5 z m 34.997,-34.997 c 11.047,0 20.002,8.955 20.002,20.001 0,11.047 -8.955,20.002 -20.002,20.002 -11.044,0 -19.998,-8.953 -19.998,-19.998 0,-11.045 8.954,-19.999 19.998,-19.999 z m 39.324,-22.499 c 1.497,-2.32 0.829,-5.414 -1.492,-6.911 -2.32,-1.496 -5.415,-0.828 -6.911,1.492 -0.088,0.136 -0.169,0.276 -0.243,0.42 -10.005,16.941 -31.848,22.564 -48.789,12.559 -5.18,-3.059 -9.501,-7.38 -12.56,-12.559 -1.264,-2.455 -4.279,-3.419 -6.734,-2.155 -2.455,1.265 -3.419,4.28 -2.154,6.735 0.074,0.143 0.155,0.283 0.242,0.419 4.847,8.329 12.237,14.883 21.085,18.699 C 3.609,-28.728 1.107,-9.9 11.176,3.256 c 10.069,13.157 28.897,15.659 42.053,5.59 13.156,-10.069 15.659,-28.897 5.59,-42.054 -1.609,-2.101 -3.488,-3.981 -5.59,-5.589 8.851,-3.815 16.244,-10.369 21.092,-18.699 z m 33.671,38.497 c -2.212,-1.645 -5.337,-1.198 -7,1 C 94.881,-9.785 85.232,-4.96 74.994,-5 c -2.761,0.143 -4.884,2.497 -4.741,5.258 0.132,2.562 2.18,4.609 4.741,4.742 8.284,-0.002 15.001,6.712 15.002,14.996 0.002,8.283 -6.712,15 -14.996,15.001 -6.839,0.002 -12.813,-4.624 -14.525,-11.245 -0.69,-2.677 -3.419,-4.287 -6.096,-3.597 -2.676,0.69 -4.286,3.42 -3.596,6.096 3.458,13.366 17.097,21.398 30.462,17.94 13.367,-3.458 21.398,-17.096 17.94,-30.462 -1.21,-4.678 -3.75,-8.905 -7.312,-12.169 6.797,-2.945 12.706,-7.617 17.139,-13.552 1.651,-2.213 1.196,-5.346 -1.017,-6.998 -0.001,0 -0.002,-0.001 -0.003,-0.002 z"
        fill="none"
        stroke={strokeColor}
        strokeWidth={6}
        strokeMiterlimit={10}
        transform="matrix(1.3333333,0,0,-1.3333333,777.86027,618.732)"
      />
      {/* Question mark circle */}
      <Path
        d="m 0,0 c -53.606,0 -99.069,-34.836 -115.004,-83.103 h 33.789 c 6.6,0 12,-5.4 12,-12 v -9.005 c 0,-6.6 -5.4,-12 -12,-12 h -39.766 c -0.067,-1.653 -0.11,-3.313 -0.11,-4.983 0,-66.876 54.214,-121.091 121.091,-121.091 66.877,0 121.091,54.215 121.091,121.091 C 121.091,-54.214 66.877,0 0,0 Z"
        fill="none"
        stroke={strokeColor}
        strokeWidth={6}
        strokeMiterlimit={10}
        transform="matrix(1.3333333,0,0,-1.3333333,817.12667,470.7468)"
      />
    </Svg>
  );
};

export default AddCommunityQuestionIcon;