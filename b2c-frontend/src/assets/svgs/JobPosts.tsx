import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const JobPosts: React.FC<OutlinedIconPropsI> = ({ width = 3.5, height = 3.5, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <Path
        d="M4.321 17.05h2.16a.49.49 0 01.261.057c.048.03.087.074.12.146.071.161.09.408.09.747 0 .34-.019.585-.09.746a.31.31 0 01-.12.147.489.489 0 01-.26.057h-2.16a.488.488 0 01-.261-.057.31.31 0 01-.12-.147c-.072-.16-.09-.407-.09-.746s.018-.586.09-.747a.31.31 0 01.12-.146.491.491 0 01.26-.057zm6 0h9.36c.133 0 .212.026.261.057a.31.31 0 01.12.146c.072.161.09.408.09.747 0 .34-.018.585-.09.746a.31.31 0 01-.12.147.487.487 0 01-.26.057h-9.36a.488.488 0 01-.261-.057.31.31 0 01-.12-.147c-.072-.16-.09-.407-.09-.746s.018-.586.09-.747a.31.31 0 01.12-.146.492.492 0 01.26-.057zm-6-6h2.16a.49.49 0 01.261.057c.048.03.087.074.12.146.071.161.09.408.09.747 0 .34-.019.585-.09.746a.31.31 0 01-.12.147.489.489 0 01-.26.057h-2.16a.488.488 0 01-.261-.057.31.31 0 01-.12-.147c-.072-.16-.09-.407-.09-.746s.018-.586.09-.747a.31.31 0 01.12-.146.491.491 0 01.26-.057zm6 0h6.96c.132 0 .212.026.261.057.048.03.088.074.12.146.072.161.09.408.09.747 0 .34-.018.585-.09.746a.31.31 0 01-.12.147.489.489 0 01-.26.057h-6.96a.488.488 0 01-.261-.057.31.31 0 01-.12-.147c-.072-.16-.09-.407-.09-.746s.018-.586.09-.747a.31.31 0 01.12-.146.492.492 0 01.26-.057zm-6-6h2.16c.132 0 .212.026.261.057.048.03.087.074.12.146.071.161.09.408.09.747 0 .34-.019.585-.09.746a.31.31 0 01-.12.147.488.488 0 01-.26.057h-2.16a.489.489 0 01-.261-.057.31.31 0 01-.12-.147c-.072-.16-.09-.407-.09-.746s.018-.586.09-.747a.31.31 0 01.12-.146.49.49 0 01.26-.057zm6 0h9.36c.133 0 .212.026.261.057a.31.31 0 01.12.146c.072.161.09.408.09.747 0 .34-.018.585-.09.746a.31.31 0 01-.12.147.488.488 0 01-.26.057h-9.36a.489.489 0 01-.261-.057.31.31 0 01-.12-.147c-.072-.16-.09-.407-.09-.746s.018-.586.09-.747a.31.31 0 01.12-.146.491.491 0 01.26-.057z"
        fill="#000"
        stroke="#fff"
        strokeWidth={0.5}
      />
    </Svg>
  );
};

export default JobPosts;
