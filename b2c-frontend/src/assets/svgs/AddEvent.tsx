/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { FilledIconPropsI } from './types';
import Svg, { Defs, ClipPath, Path } from 'react-native-svg';

const AddEventIcon: React.FC<FilledIconPropsI> = ({
    width = 12,
    height = 12,
    fill = '#343330',
    color,
    disabled,
    accessibilityLabel = 'Add Event Icon',
    ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 1440 1440"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Defs>
        <ClipPath id="a">
          <Path
            d="M0 1080h1080V0H0z"
            transform="translate(-589.909 -594.909)"
          />
        </ClipPath>
        <ClipPath id="b">
          <Path
            d="M0 1080h1080V0H0z"
            transform="translate(-447.929 -552.818)"
          />
        </ClipPath>
        <ClipPath id="c">
          <Path d="M0 1080h1080V0H0z" transform="translate(-444.017 -459)" />
        </ClipPath>
        <ClipPath id="d">
          <Path
            d="M0 1080h1080V0H0z"
            transform="translate(-543.077 -458.702)"
          />
        </ClipPath>
        <ClipPath id="e">
          <Path
            d="M0 1080h1080V0H0z"
            transform="translate(-639.503 -461.045)"
          />
        </ClipPath>
        <ClipPath id="f">
          <Path d="M0 1080h1080V0H0z" transform="translate(-536.258 -531)" />
        </ClipPath>
        <ClipPath id="g">
          <Path d="M0 1080h1080V0H0z" transform="translate(-639.63 -530.702)" />
        </ClipPath>
        <ClipPath id="h">
          <Path
            d="M0 1080h1080V0H0z"
            transform="translate(-667.539 -563.896)"
          />
        </ClipPath>
        <ClipPath id="i">
          <Path
            d="M0 1080h1080V0H0z"
            transform="translate(-727.242 -638.922)"
          />
        </ClipPath>
        <ClipPath id="j">
          <Path d="M0 1080h1080V0H0z" transform="translate(-407.771 -606.97)" />
        </ClipPath>
      </Defs>
      
      {/* Calendar Grid - Stroke only */}
      <Path
        d="M622.952 560.7h36.135v-35.4h-36.135zM524.995 560.7h36.135v-35.4h-36.135zM426.3 560.7h36.135v-35.4H426.3zM622.952 488.7h36.135v-35.4h-36.135zM524.995 488.7h36.135v-35.4h-36.135zM427.038 488.7h36.135v-35.4h-36.135z"
        transform="matrix(1.33333 0 0 -1.33333 0 1440)"
        fill="none"
        stroke={fillColor}
        strokeWidth={6}
        strokeLinecap="butt"
        strokeLinejoin="miter"
        strokeMiterlimit={4}
        strokeOpacity={opacity}
      />
      
      {/* "Event" text - Fill */}
      <Path
        d="M0 0l-2.887-.511A3.411 3.411 0 01-3.462.543a2.85 2.85 0 01-1.044.821c-.44.213-.991.319-1.652.319-.902 0-1.655-.202-2.258-.607-.604-.398-.906-.913-.906-1.545 0-.546.203-.987.608-1.321.405-.334 1.058-.607 1.96-.82l2.599-.597c1.506-.348 2.628-.884 3.367-1.608C-.05-5.54.32-6.481.32-7.638a4.29 4.29 0 00-.853-2.621c-.561-.76-1.346-1.357-2.354-1.79-1.002-.433-2.163-.65-3.484-.65-1.832 0-3.327.391-4.485 1.172-1.157.789-1.868 1.907-2.13 3.356l3.078.469c.192-.803.586-1.41 1.183-1.822.597-.405 1.374-.607 2.333-.607 1.044 0 1.879.216 2.503.65.626.44.938.976.938 1.608 0 .511-.192.941-.575 1.289-.376.348-.955.611-1.737.788l-2.77.608c-1.527.348-2.656.902-3.387 1.662-.725.76-1.087 1.722-1.087 2.887 0 .966.27 1.811.809 2.535.54.725 1.286 1.289 2.238 1.694.951.412 2.042.618 3.27.618 1.769 0 3.161-.383 4.176-1.15C-.998 2.298-.326 1.278 0 0m-22.063 7.916h3.185v-15.48c0-.618.093-1.083.277-1.395.185-.306.423-.515.714-.629.299-.106.621-.16.969-.16.256 0 .48.018.672.054.191.035.341.063.447.085l.575-2.632a5.324 5.324 0 00-.788-.213 6.31 6.31 0 00-1.278-.128 5.604 5.604 0 00-2.344.448 4.018 4.018 0 00-1.758 1.448c-.447.654-.671 1.475-.671 2.462zm6.541-6.478h-8.938v2.557h8.938zm-22.383-4.091v-9.716h-3.185V3.995h3.057V1.332h.203a4.682 4.682 0 001.768 2.088c.81.525 1.829.788 3.058.788 1.115 0 2.091-.234 2.93-.703.838-.462 1.488-1.151 1.949-2.067.462-.916.692-2.049.692-3.398v-10.409h-3.185v10.025c0 1.186-.309 2.113-.926 2.781-.618.675-1.467 1.012-2.547 1.012-.738 0-1.395-.16-1.971-.48-.568-.319-1.019-.788-1.353-1.406-.326-.611-.49-1.349-.49-2.216m-13.74-10.046c-1.612 0-3.001.345-4.165 1.034-1.158.696-2.053 1.672-2.685 2.929-.625 1.265-.937 2.745-.937 4.443 0 1.676.312 3.153.937 4.432.632 1.278 1.512 2.276 2.642 2.993 1.137.717 2.464 1.076 3.984 1.076.924 0 1.819-.153 2.685-.458a6.498 6.498 0 002.333-1.438c.689-.654 1.232-1.502 1.63-2.546.398-1.037.597-2.298.597-3.783v-1.129h-13.008v2.387h9.886c0 .838-.17 1.58-.511 2.227a3.866 3.866 0 01-1.439 1.544c-.61.377-1.327.565-2.151.565-.895 0-1.677-.22-2.344-.661a4.429 4.429 0 01-1.534-1.705 4.92 4.92 0 01-.533-2.269v-1.864c0-1.094.192-2.024.576-2.791.39-.767.933-1.353 1.629-1.758.696-.397 1.51-.597 2.44-.597.604 0 1.154.086 1.651.256.497.178.927.44 1.289.788.363.348.639.778.831 1.29l3.015-.544a5.47 5.47 0 00-1.299-2.333c-.618-.66-1.396-1.175-2.334-1.545-.93-.362-1.992-.543-3.185-.543m-9.49 16.694l-5.934-16.364h-3.409l-5.944 16.364h3.419l4.145-12.592h.17l4.134 12.592zm-31.819-16.364V9.45h13.68V6.616h-10.388V-.032h9.674v-2.823h-9.674v-6.68h10.515v-2.834z"
        transform="matrix(1.33333 0 0 -1.33333 786.545 646.789)"
        clipPath="url(#a)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar number "1" - Fill */}
      <Path
        d="M0 0v-21.818h-2.642V-2.77h-.128l-5.327-3.537v2.685L-2.642 0z"
        transform="matrix(1.33333 0 0 -1.33333 597.239 702.909)"
        clipPath="url(#b)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar number "2" - Fill */}
      <Path
        d="M0 0V21.818h2.514V0zm-10.27 4.474v2.174l9.588 15.17H.895v-3.366H-.17L-7.415 6.989v-.171H5.497V4.474z"
        transform="matrix(1.33333 0 0 -1.33333 592.023 828)"
        clipPath="url(#c)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar number "5" - Fill */}
      <Path
        d="M0 0c-1.25 0-2.376.248-3.377.746-1.002.497-1.804 1.179-2.408 2.045-.604.867-.934 1.854-.991 2.962h2.557c.099-.987.547-1.804 1.343-2.451.802-.639 1.761-.958 2.876-.958.895 0 1.69.209 2.386.628a4.435 4.435 0 011.651 1.726c.405.739.608 1.573.608 2.504 0 .951-.21 1.8-.629 2.546a4.62 4.62 0 01-1.704 1.779c-.725.433-1.552.654-2.482.66a6.8 6.8 0 01-2.057-.309c-.703-.206-1.282-.472-1.736-.798l-2.472.298 1.321 10.739H6.221v-2.345h-9.119l-.767-6.434h.128c.448.355 1.009.65 1.683.884a6.395 6.395 0 002.11.352c1.335 0 2.524-.32 3.568-.959a6.693 6.693 0 002.472-2.6c.604-1.101.906-2.358.906-3.771 0-1.392-.313-2.635-.938-3.729A6.863 6.863 0 003.707.937C2.621.313 1.385 0 0 0"
        transform="matrix(1.33333 0 0 -1.33333 724.102 828.398)"
        clipPath="url(#d)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar number "6" - Fill */}
      <Path
        d="M0 0c.895 0 1.697.224 2.408.671a4.812 4.812 0 011.694 1.8c.418.753.628 1.592.628 2.515 0 .902-.202 1.722-.607 2.461a4.631 4.631 0 01-1.652 1.779c-.696.44-1.491.66-2.386.66a4.448 4.448 0 01-1.886-.404 4.861 4.861 0 01-1.533-1.087 5.234 5.234 0 01-1.023-1.587 4.745 4.745 0 01-.373-1.865c0-.866.202-1.676.607-2.429A5.011 5.011 0 01-2.44.692C-1.722.231-.909 0 0 0m0-2.344a8.17 8.17 0 00-2.684.512c-.896.327-1.712.877-2.451 1.651-.738.781-1.332 1.836-1.779 3.164-.447 1.335-.671 3.012-.671 5.028 0 1.932.181 3.644.543 5.136.362 1.498.888 2.758 1.577 3.781.689 1.03 1.52 1.811 2.493 2.344.98.533 2.084.799 3.313.799 1.221 0 2.308-.245 3.26-.735a6.223 6.223 0 002.344-2.024c.603-.867.994-1.864 1.172-2.994h-2.6c-.241.98-.71 1.794-1.406 2.44-.696.646-1.619.97-2.77.97-1.69 0-3.022-.736-3.995-2.206-.966-1.47-1.453-3.533-1.46-6.19h.171a6.388 6.388 0 003.249 2.547 6.363 6.363 0 002.12.351c1.25 0 2.393-.312 3.431-.937 1.037-.618 1.868-1.474 2.492-2.568.625-1.086.938-2.333.938-3.739 0-1.349-.302-2.585-.905-3.707a6.898 6.898 0 00-2.547-2.664C2.749-2.038 1.47-2.358 0-2.344"
        transform="matrix(1.33333 0 0 -1.33333 852.67 825.273)"
        clipPath="url(#e)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar number "2" - Fill */}
      <Path
        d="M0 0v1.918l7.202 7.883a49.958 49.958 0 012.088 2.408c.547.689.951 1.335 1.214 1.939.27.611.405 1.25.405 1.918 0 .766-.185 1.431-.554 1.992a3.588 3.588 0 01-1.491 1.299c-.633.306-1.342.459-2.131.459-.838 0-1.569-.174-2.195-.522A3.601 3.601 0 013.1 17.855c-.333-.618-.5-1.342-.5-2.173H.085c0 1.278.295 2.4.885 3.366a6.145 6.145 0 002.407 2.259c1.023.54 2.17.81 3.441.81 1.279 0 2.412-.27 3.399-.81.987-.54 1.761-1.268 2.322-2.184.561-.917.842-1.935.842-3.057 0-.803-.146-1.588-.437-2.355-.284-.76-.781-1.609-1.491-2.546-.703-.931-1.68-2.067-2.93-3.409L3.622 2.514v-.17h10.143V0z"
        transform="matrix(1.33333 0 0 -1.33333 715.011 732)"
        clipPath="url(#f)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar number "3" - Fill */}
      <Path
        d="M0 0c-1.406 0-2.66.241-3.761.724-1.094.483-1.964 1.155-2.61 2.014-.639.866-.987 1.871-1.044 3.015h2.685c.057-.703.298-1.311.724-1.822.426-.504.984-.895 1.673-1.172.689-.277 1.452-.415 2.29-.415.938 0 1.769.163 2.493.49.725.326 1.293.781 1.705 1.364.412.582.618 1.257.618 2.023 0 .803-.199 1.51-.597 2.121-.398.617-.98 1.1-1.747 1.448-.767.348-1.704.522-2.812.522h-1.748v2.344h1.748c.866 0 1.626.156 2.28.469.66.313 1.175.753 1.544 1.321.377.568.565 1.236.565 2.003 0 .739-.164 1.381-.49 1.928a3.347 3.347 0 01-1.385 1.278c-.59.306-1.286.459-2.088.459a5.507 5.507 0 01-2.131-.416 3.961 3.961 0 01-1.619-1.183c-.419-.511-.647-1.129-.682-1.853h-2.557c.042 1.144.387 2.145 1.034 3.004.646.867 1.491 1.541 2.535 2.024 1.051.483 2.205.725 3.462.725 1.35 0 2.508-.274 3.474-.82.965-.54 1.707-1.254 2.226-2.142a5.603 5.603 0 00.778-2.876c0-1.229-.324-2.277-.97-3.143-.639-.866-1.509-1.467-2.61-1.801v-.17c1.378-.227 2.454-.813 3.228-1.758.774-.937 1.161-2.099 1.161-3.484 0-1.186-.323-2.251-.969-3.196C5.764 2.088 4.89 1.349 3.782.809 2.674.27 1.414 0 0 0"
        transform="matrix(1.33333 0 0 -1.33333 852.84 732.398)"
        clipPath="url(#g)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Calendar Border - Stroke only */}
      <Path
        d="M0 0v54.428h-54.428c-6.592 0-11.986 5.394-11.986 11.986h-168.239c-14.171-.001-25.563-11.252-25.563-25.012V-97.556c0-13.761 11.392-25.011 25.563-25.012h221.076c14.172.001 25.563 11.251 25.563 25.012v85.57C5.394-11.986 0-6.592 0 0z"
        transform="matrix(1.33333 0 0 -1.33333 890.051 688.138)"
        clipPath="url(#h)"
        fill="none"
        stroke={fillColor}
        strokeWidth={6}
        strokeLinecap="butt"
        strokeLinejoin="miter"
        strokeMiterlimit={4}
        strokeOpacity={opacity}
      />
      
      {/* Plus Sign - Fill */}
      <Path
        d="M0 0h-39.106v39.106c0 4.736-3.875 8.612-8.612 8.612-4.736 0-8.611-3.876-8.611-8.612V0h-39.106c-4.737 0-8.612-3.875-8.612-8.612 0-4.736 3.875-8.612 8.612-8.612h39.106v-39.105c0-4.737 3.875-8.612 8.611-8.612 4.737 0 8.612 3.875 8.612 8.612v39.105H0c4.736 0 8.612 3.876 8.612 8.612C8.612-3.875 4.736 0 0 0"
        transform="matrix(1.33333 0 0 -1.33333 969.656 588.104)"
        clipPath="url(#i)"
        fill={fillColor}
        fillOpacity={opacity}
        stroke="none"
      />
      
      {/* Bottom line - Stroke only */}
      <Path
        d="M0 0l260.218-.002"
        transform="matrix(1.33333 0 0 -1.33333 543.695 630.706)"
        clipPath="url(#j)"
        fill="none"
        stroke={fillColor}
        strokeWidth={6}
        strokeLinecap="butt"
        strokeLinejoin="miter"
        strokeMiterlimit={10}
        strokeOpacity={opacity}
      />
    </Svg>
  );
};

export default AddEventIcon;