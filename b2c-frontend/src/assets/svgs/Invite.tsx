import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { G, Path, Defs, LinearGradient, Stop, ClipPath } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Invite: React.FC<FilledIconPropsI> = ({ width = 4, height = 4, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      {...props}
    >
      <G clipPath="url(#clip0_5916_5171)">
        <Path
          d="M22.668 4a4 4 0 013.993 3.765l.007.235v5.8l.792-.352c.818-.363 1.763.2 1.866 1.063l.009.156v10.666a2.667 2.667 0 01-2.468 2.66l-.199.007H5.335a2.667 2.667 0 01-2.66-2.468l-.007-.199V14.667c0-.896.9-1.53 1.73-1.273l.145.054.792.352V8A4 4 0 019.1 4.007L9.335 4h13.333zm0 2.667H9.335C8.598 6.667 8 7.264 8 8v6.985l8 3.556 8-3.556V8c0-.736-.597-1.333-1.333-1.333zm-6.667 4a1.333 1.333 0 01.156 2.657l-.156.01h-2.666a1.333 1.333 0 01-.156-2.658l.156-.01H16z"
          fill="url(#paint0_linear_5916_5171)"
        />
      </G>
      <Defs>
        <LinearGradient
          id="paint0_linear_5916_5171"
          x1={16.0013}
          y1={4}
          x2={16.0013}
          y2={28}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#D6AE00" />
          <Stop offset={1} stopColor="#F4AE00" />
        </LinearGradient>
        <ClipPath id="clip0_5916_5171">
          <Path fill="#fff" d="M0 0H32V32H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default Invite;
