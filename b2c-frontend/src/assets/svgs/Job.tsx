import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Job: React.FC<OutlinedIconPropsI> = ({ width = 4, height = 4, fill = 'none', ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 29 29"
      fill={fill}
      {...props}
    >
      <Path
        d="M11 8.667H6.333A2.333 2.333 0 004 11v10.5a2.333 2.333 0 002.333 2.333h16.334A2.333 2.333 0 0025 21.5V11a2.333 2.333 0 00-2.333-2.333H18m-7 0V6.333A2.333 2.333 0 0113.333 4h2.334A2.333 2.333 0 0118 6.333v2.334m-7 0h7"
        stroke="#000"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </Svg>
  );
};

export default Job;
