  /*
  Copyright (c) 2025-present Navicater Solutions

  This source code is licensed under the license found in the
  LICENSE file in the root directory of this source tree.
  */
  import React from 'react';
  import { RFPercentage } from 'react-native-responsive-fontsize';
  import { FilledIconPropsI } from './types';
  import Svg, { Defs, Path, SvgProps } from 'react-native-svg';

  const CreateQuestionIcon: React.FC<FilledIconPropsI> = ({
      width = 10,
      height = 10,
      fill = '#000',
      color,
      disabled,
      accessibilityLabel = 'Create Question Icon',
      ...props
  }) => {
    const strokeColor = '#fff';
    const opacity = disabled ? 0.5 : 1;

    return (
      <Svg
        width={RFPercentage(width)}
        height={RFPercentage(height)}
        viewBox="0 0 1080 1080"
        fill="none"
        opacity={opacity}
        accessibilityLabel={accessibilityLabel}
        {...props}
      >
        {/* Chat bubble - cls-1 style */}
        <Path
          d="M653.42,627.09c-47.43,0-89.5-22.9-115.78-58.25h-84.42c-6.6,0-12-5.4-12-12v-4.83c0-6.6,5.4-12,12-12h67.78c-1.7-3.94-3.23-7.97-4.59-12.09-9.08-1.66-15.87-6.51-16.79-12.41h-46.4c-6.6,0-12-5.4-12-12v-2.68c0-6.6,5.4-12,12-12h46.66v-13.03h-45.81c-6.6,0-12-5.4-12-12v-8.88c0-6.6,5.4-12,12-12h45.81v-8.47h-63.44c-27.5,0-50,22.5-50,50v142.26c0,24.59,18,45.17,41.47,49.25l-3.91,56.66,81.35-55.91h148.45c27.5,0,50-22.5,50-50v-8.67c-15.69,5.85-32.66,9.06-50.39,9.06ZM559.62,613.44c0,6.6-5.4,12-12,12h-93.55c-6.6,0-12-5.4-12-12v-4.89c0-6.6,5.4-12,12-12h93.55c6.6,0,12,5.4,12,12v4.89Z"
          fill="none"
          stroke={strokeColor}
          strokeMiterlimit={10}
          strokeWidth={8}
        />
        {/* Plus sign - cls-2 style */}
        <Path
          d="M565.88,449.41h-39.11v-39.11c0-4.74-3.88-8.61-8.61-8.61h0c-4.74,0-8.61,3.88-8.61,8.61v39.11h-39.11c-4.74,0-8.61,3.88-8.61,8.61s3.88,8.61,8.61,8.61h39.11v39.11c0,4.74,3.88,8.61,8.61,8.61h0c4.74,0,8.61-3.88,8.61-8.61v-39.11h39.11c4.74,0,8.61-3.88,8.61-8.61s-3.88-8.61-8.61-8.61Z"
          fill={strokeColor}
          stroke="none"
        />
        {/* Question mark - cls-1 style */}
        <Path
          d="M656.13,359.7c-53.65,0-99.14,34.89-115.04,83.21h33.83c6.6,0,12,5.4,12,12v8.75c0,6.6-5.4,12-12,12h-39.76c-.07,1.7-.12,3.41-.12,5.12,0,66.88,54.21,121.09,121.09,121.09s121.09-54.21,121.09-121.09-54.21-121.09-121.09-121.09ZM649.19,561.98c-7.92,0-14.52-5.72-14.52-13.64s6.6-13.86,14.52-13.86,14.3,5.94,14.3,13.86-6.38,13.64-14.3,13.64ZM674.71,491.14c-10.12,2.86-14.96,5.72-15.62,16.06l-.22,3.3c0,5.28-4.4,9.9-9.9,9.9s-9.68-4.62-9.68-9.9v-3.3c0-21.34,12.54-29.7,29.48-34.98,9.24-2.64,18.04-12.32,18.04-22.66,0-14.3-12.54-23.32-25.52-23.32-11.22,0-21.56,7.7-23.98,20.46-1.1,5.28-6.38,8.58-11.44,7.04-5.06-1.1-7.92-6.38-6.82-11.44,4.18-22.22,21.78-35.64,42.24-35.64,23.76,0,45.1,16.5,45.1,42.9,0,21.34-14.3,36.74-31.68,41.58Z"
          fill="none"
          stroke={strokeColor}
          strokeMiterlimit={10}
          strokeWidth={8}
        />
      </Svg>
    );
  };

  export default CreateQuestionIcon;