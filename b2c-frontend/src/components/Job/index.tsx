import { Pressable, Text, View } from "react-native"
import { JobPropsI } from "./types"
import UserAvatar from "../UserAvatar"
import { formatElapsedTime } from "@/src/utilities/datetime"
import { RFPercentage } from "react-native-responsive-fontsize"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import { CareerStackParamListI } from "@/src/navigation/types"
import Match from "@/src/assets/svgs/Match"

const Job = ({ job }: JobPropsI) => {
    const timeAgo = formatElapsedTime(job.createdAt)
    const navigation = useNavigation<StackNavigationProp<CareerStackParamListI>>()
    const handleJobNavigation = () => {
        navigation.navigate("CareerDetails", { jobPostId: job.id })
    }
    return (
        <Pressable onPress={handleJobNavigation} className="py-2 flex-row gap-3 px-4 border-b border-[#BDBDBD] ">
            <UserAvatar avatarUri={job?.creator?.avatar} name={job?.creator?.name} width={RFPercentage(3.5)} height={RFPercentage(3.5)} className="pt-3" />
            <View className="py-2">
                <Text className="font-semibold text-base">
                    {job?.designation?.name}
                </Text>
                <Text>
                    {job?.entity?.name}
                </Text>
                <View className="flex-row gap-2 mt-1 items-center">
                    <Text className="text-[#525252]">
                        {timeAgo}
                    </Text>
                    <View className='flex-row items-center bg-backgroundGrayDark gap-1 rounded-full px-2 py-1.5 border-[#EAEAEA] border'>
                        <Match />
                        <Text className='text-base font-semibold text-black'>{job.matching ?? 0}%</Text>
                    </View>
                </View>
            </View>
        </Pressable>
    )
}

export default Job;
