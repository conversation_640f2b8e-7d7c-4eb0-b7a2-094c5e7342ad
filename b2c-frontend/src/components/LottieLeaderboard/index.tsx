// src/components/LottieLeaderboard/index.tsx
import React, { useEffect, useRef } from 'react';
import { View } from 'react-native';
import LottieView from 'lottie-react-native';

// Adjust path only if your folder structure differs
const src = require('../../assets/animations/leaderboard.json');

type Props = {
  width?: number; // dp
  height?: number; // dp
  isActive?: boolean; // animate (loop) while true
  speed?: number;
};

const LottieLeaderboard: React.FC<Props> = ({
  width = 24,
  height = 24,
  isActive = false,
  speed = 1,
}) => {
  const ref = useRef<LottieView>(null);
  const prev = useRef(isActive);

  useEffect(() => {
    const view = ref.current;
    if (!view) return;

    // Act only on transitions to avoid redundant resets
    if (!prev.current && isActive) {
      // false -> true: start animation
      view.reset();
      view.play();
    } else if (prev.current && !isActive) {
      // true -> false: stop animation
      view.pause();
      view.reset();
    }
    prev.current = isActive;
  }, [isActive]);

  return (
    <View style={{ width, height, alignItems: 'center', justifyContent: 'center' }}>
      <LottieView
        ref={ref}
        source={src}
        autoPlay={false} // controlled via effect
        loop={isActive} // keeps playing until user taps/you mark seen
        speed={speed}
        style={{ width, height }}
        resizeMode="contain"
      />
    </View>
  );
};

export default LottieLeaderboard;
