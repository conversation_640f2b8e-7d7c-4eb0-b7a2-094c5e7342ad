import React, { useEffect, useState } from 'react';
import { View, Text, Modal, TouchableOpacity, FlatList, Pressable } from 'react-native';
import LottieView from 'lottie-react-native';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { clearNearbyFilters } from '@/src/redux/slices/announcement/announcementSlice';
import {
  resetEntityProfileState,
  setEntityProfileBasicData,
} from '@/src/redux/slices/entityprofile/entityProfileSlice';
import { setUserActive, setUserInactive } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import AddItem from '@/src/assets/svgs/AddItem';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import Close from '@/src/assets/svgs/Close';
import { updateProfileTypeAPI } from '@/src/networks/source';
import TextView from '../TextView';
import UserAvatar from '../UserAvatar';
import { SwitchProfileModalPropsI, SwitchProfileTypeI } from './types';

const SwitchProfileModal: React.FC<SwitchProfileModalPropsI> = ({
  visible,
  onClose,
  profiles,
  modalNavigation,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const navigation = modalNavigation;
  const [showSwitch, setShowSwitch] = useState(false);
  const [showErrorToast, setShowErrorToast] = useState(false);

  const selectedId = currentUser.isActive ? currentUser.profileId : currentEntity.entityProfileId;

  useEffect(() => {
    if (!showSwitch && showErrorToast) {
      showToast({
        type: 'error',
        message: 'Could not Switch Profiles. Try Again later.',
      });
      setShowErrorToast(false);
    }
  }, [showSwitch, showErrorToast]);

  const handleUserSelection = async () => {
    try {
      await updateProfileTypeAPI({ type: 'USER' });
      dispatch(setUserActive());
      dispatch(resetEntityProfileState());
      dispatch(clearNearbyFilters());
    } catch (e) {
      setShowErrorToast(true);
      throw e;
    }
  };

  const handleEntitySelection = async (entityProfile: SwitchProfileTypeI) => {
    try {
      await updateProfileTypeAPI({ type: 'ENTITY' });
      dispatch(setEntityProfileBasicData(entityProfile));
      dispatch(setUserInactive());
      dispatch(clearNearbyFilters());
    } catch (e) {
      setShowErrorToast(true);
      throw e;
    }
  };

  const handleSelection = async (profile: SwitchProfileTypeI) => {
    try {
      setShowSwitch(true);
      if (profile.type === 'USER') {
        handleUserSelection();
      } else {
        handleEntitySelection(profile);
      }
    } catch (_e) {
    } finally {
      onClose();
      setTimeout(() => {
        setShowSwitch(false);
      }, 2300);
    }
  };

  const handleCreateEntityProfilePress = () => {
    navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: { screen: 'CreateEntityProfile' },
    });
    onClose();
  };

  const footerComponent = () => (
    <Pressable
      onPress={handleCreateEntityProfilePress}
      className="flex-row align-center py-3 gap-4 pl-8"
    >
      <AddItem />
      <TextView
        subtitle="Create Organization Profile"
        subtitleClassName="text-[#448600] font-bold"
      />
    </Pressable>
  );

  if (showSwitch) {
    return (
      <Modal visible transparent animationType="fade">
        <View className="flex-1 justify-center items-center bg-white">
          <LottieView
            source={require('@/src/assets/animations/profileSwitch.json')}
            autoPlay
            loop={true}
            style={{ height: 100, width: 100 }}
          />
        </View>
      </Modal>
    );
  }

  const renderItem = ({ item }: { item: SwitchProfileTypeI }) => {
    const selected = item.id === selectedId;
    return (
      <TouchableOpacity
        onPress={() => handleSelection(item)}
        className={`flex-row items-center px-4 py-3 ${selected ? 'bg-green-100' : ''}`}
      >
        <UserAvatar avatarUri={item.avatar} name={item.name} />
        <Text className="ml-3 flex-1 text-base text-gray-900">{item.name}</Text>
        {selected ? (
          <View className="w-2 h-2 bg-green-500 rounded-full" />
        ) : (
          <ChevronRight height={3} width={3} color="#6b7280" />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View className="flex-1 justify-center items-center bg-black/50">
        <View className="bg-white rounded-2xl w-[90%] max-w-xl">
          <View className="flex-row justify-between items-center px-4 py-3 border-b border-gray-200">
            <Text className="text-lg font-semibold">Switch profile</Text>
            <TouchableOpacity onPress={onClose}>
              <Close height={3} width={3} color="#000" />
            </TouchableOpacity>
          </View>

          <FlatList
            data={profiles}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderItem}
            contentContainerStyle={{ paddingBottom: 8 }}
            ListFooterComponent={footerComponent}
          />
        </View>
      </View>
    </Modal>
  );
};

export default SwitchProfileModal;
