import { DrawerNavigationProp } from '@react-navigation/drawer';
import { EntityProfileI } from '@/src/redux/slices/entityprofile/types';
import { RootDrawerParamListI } from '@/src/navigation/types';

export type SwitchProfileModalPropsI = {
  visible: boolean;
  onClose: () => void;
  profiles: SwitchProfileTypeI[];
  modalNavigation: DrawerNavigationProp<RootDrawerParamListI>;
};

export type SwitchProfileTypeI = Pick<EntityProfileI, 'id' | 'name' | 'avatar'> & {
  isVerified?: boolean;
  role?: string;
  type: string;
};
