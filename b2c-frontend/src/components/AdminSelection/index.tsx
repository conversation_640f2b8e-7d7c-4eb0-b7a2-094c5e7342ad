import { Pressable, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import Close from '@/src/assets/svgs/Close';
import EntitySearch from '../EntitySearch';
import UserAvatar from '../UserAvatar';
import { AdminSelectionPropsI } from './types';

const AdminSelection = ({ admins, onDelete, classname }: AdminSelectionPropsI) => {
  const currentUserProfileId = useSelector(selectCurrentUser).profileId;

  return (
    <View className={classname}>
      {admins &&
        admins.map((admin) => (
          <View key={admin.id} className="flex-row items-center justify-between w-40 mb-3 px-5">
            <View className="flex-row items-center gap-3 flex-1 min-w-0">
              <UserAvatar avatarUri={admin.avatar} name={admin.username} width={30} height={30} />
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="flex-1 min-w-0"
                style={{ maxWidth: '80%' }}
              >
                {admin.username}
              </Text>
            </View>
            {currentUserProfileId !== admin.id && (
              <Pressable onPress={() => onDelete(admin.id)} className="ml-2">
                <Close width={1.5} height={1.5} />
              </Pressable>
            )}
          </View>
        ))}
    </View>
  );
};

export default AdminSelection;
