/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { FlatList, Pressable, Text, View, RefreshControl, ActivityIndicator } from 'react-native';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import UserAvatar from '../UserAvatar';
import { UserItemPropsI, UsersListPropsI } from './types';

const UserItem = ({ item, onPress, isLast, renderActions, renderCustom }: UserItemPropsI) => {
  const isEntityProfile = Boolean(item.EntityProfile?.id);

  const profile = item.Profile;
  const name = profile?.name || 'Anonymous User';
  const avatar = profile?.avatar;
  const title = profile?.designation?.name;
  const company = profile?.entity?.name;

  const entityProfile = item.EntityProfile;
  const entityProfileName = entityProfile?.name;
  const entityProfileAvatar = entityProfile?.avatar;

  return (
    <View className={`py-4 px-5 ${isLast ? '' : 'border-b border-gray-100'}`}>
      <View className="flex-row items-center justify-between">
        <Pressable
          className="flex-row items-center flex-1"
          onPress={() => onPress?.(item)}
          android_ripple={{ color: 'transparent' }}
        >
          <UserAvatar
            avatarUri={isEntityProfile ? (entityProfileAvatar ?? '') : avatar}
            name={isEntityProfile ? entityProfileName : name}
          />
          <View className="ml-4 flex-1">
            <Text className="text-lg font-semibold text-gray-900" numberOfLines={1}>
              {isEntityProfile ? entityProfileName : name}
            </Text>
            {!isEntityProfile && title && (
              <Text className="text-sm text-gray-500" numberOfLines={1} ellipsizeMode="tail">
                {title}
                {title.toLowerCase() !== 'aspirant' && company ? ` at ${company}` : ''}
              </Text>
            )}
            {item.status ? (
              <Text className="text-xs text-gray-500 mt-1">
                {capitalizeFirstLetter(item.status)}
              </Text>
            ) : null}
          </View>
        </Pressable>

        {renderCustom && <View className="ml-2">{renderCustom(item)}</View>}
        {renderActions && <View className="ml-2">{renderActions(item)}</View>}
      </View>
    </View>
  );
};

const UsersList = ({
  data,
  onPress,
  refreshing = false,
  onRefresh,
  onLoadMore,
  loading = false,
  renderActions,
  renderCustom,
}: UsersListPropsI) => {
  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  };

  const handleEndReached = () => {
    if (!loading && onLoadMore) {
      onLoadMore();
    }
  };

  const renderEmptyComponent = () => {
    if (loading) return null;

    return (
      <View className="flex-1 justify-center items-center py-10">
        <Text className="text-gray-500 text-base">It's empty here</Text>
      </View>
    );
  };

  return (
    <FlatList
      data={data}
      renderItem={({ item, index }) => (
        <UserItem
          item={item}
          onPress={onPress}
          isLast={index === data.length - 1}
          renderActions={renderActions}
          renderCustom={renderCustom}
        />
      )}
      keyExtractor={(item, index) => item.Profile?.id || index.toString()}
      refreshControl={
        onRefresh ? (
          <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
        ) : undefined
      }
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.8}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmptyComponent}
      contentContainerStyle={{
        flexGrow: 1,
        backgroundColor: 'white',
      }}
      initialNumToRender={10}
      maxToRenderPerBatch={10}
      windowSize={10}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default UsersList;
