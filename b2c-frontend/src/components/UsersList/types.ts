/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';

export interface ListItem extends ObjUnknownI {
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designation: SearchResultI | null;
    entity: SearchResultI | null;
  };
  EntityProfile: {
    id: string | undefined;
    name: string | undefined;
    avatar: string | null | undefined;
  } | null;
  status?: string;
}

export interface UserItemPropsI {
  item: ListItem;
  onPress?: (item: ListItem) => void;
  isLast: boolean;
  renderActions?: (item: ListItem) => React.ReactNode;
  renderCustom?: (item: ListItem) => React.ReactNode;
}

export interface UsersListPropsI {
  data: ListItem[];
  onPress?: (item: ListItem) => void;
  refreshing?: boolean;
  loading?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  renderActions?: (item: ListItem) => React.ReactNode;
  renderCustom?: (item: ListItem) => React.ReactNode;
}
