import { EntityProfileStateI } from '@/src/redux/slices/entityprofile/types';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { UserState } from '@/src/redux/slices/user/types';
import { EntityProfileBaseI } from '@/src/networks/entityProfile/types';

export type AnnouncementItemI = {
  announcementId: string;
  cursorId: number;
  title: string;
  description: string;
  latitude?: number;
  longitude?: number;
  addressRawData: {
    id: string;
    text: string;
  };
  cityId: string | null;
  cityRawDataId: string | null;
  countryIso2: string | null;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  totalAttendees: number;
  createdAt: string;
  updatedAt: string;
  isRSVPed: boolean;
  city?: SearchResultI;
  topAttendeesRaw:
    | {
        id: string;
        name: string;
        avatar: null | string;
      }[]
    | null;
};

export type ProfileItemI = {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI;
  entity: SearchResultI | null;
};

export interface AnnouncementProps {
  announcement: AnnouncementItemI;
  isUserActive: boolean;
  user: ProfileItemI;
  entity: EntityProfileBaseI | null;
  currentUser: Pick<UserState, 'profileId' | 'fullName' | 'avatar'>;
  currentEntity: Pick<EntityProfileStateI, 'entityProfileId' | 'name' | 'avatar'>;
  onDelete: (id: string) => void;
}
