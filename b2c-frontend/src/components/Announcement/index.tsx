import { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { formatDate } from '@/src/utilities/datetime';
import { showToast } from '@/src/utilities/toast';
import { NearbyStackParamListI } from '@/src/navigation/types';
import Clock from '@/src/assets/svgs/Clock';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Location from '@/src/assets/svgs/Location';
import Tick from '@/src/assets/svgs/Tick';
import TrashBin from '@/src/assets/svgs/TrashBin';
import { attendUnattendEventAPI, deleteAnnouncementAPI } from '@/src/networks/nearby/announcement';
import BottomSheet from '../Bottomsheet';
import Button from '../Button';
import CustomModal from '../Modal';
import { OptionItem, OptionsMenu } from '../OptionsMenu';
import StackedAvatar from '../StackedAvatar';
import UserAvatar from '../UserAvatar';
import { AnnouncementProps } from './types';

export const Announcement = ({
  announcement,
  isUserActive,
  user,
  entity,
  currentUser,
  currentEntity,
  onDelete,
}: AnnouncementProps) => {
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();
  const [isJoining, setIsJoining] = useState(announcement.isRSVPed || false);
  const [isVisible, setIsVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [count, setCount] = useState(announcement.totalAttendees);
  const [topAttendees, setTopAttendees] = useState(announcement.topAttendeesRaw);

  const isEntityAnnouncement = Boolean(entity?.id);
  const isOwnAnnouncement = currentUser.profileId === user.id;
  const isOwnEntityAnnouncement = currentEntity.entityProfileId === entity?.id;

  const handleJoin = async () => {
    try {
      const payload = {
        id: announcement.announcementId,
        status: isJoining ? 'UNATTEND' : 'ATTEND',
      };
      await attendUnattendEventAPI(payload);
      setIsJoining(!isJoining);
      setCount((prev) => prev + (isJoining ? -1 : 1));
      setTopAttendees((prev) => {
        if (isJoining) {
          return (
            prev?.filter((attendee) =>
              isUserActive
                ? attendee.id !== currentUser.profileId
                : attendee.id !== currentEntity.entityProfileId,
            ) ?? null
          );
        } else {
          const newAttendee = isUserActive
            ? {
                id: currentUser.profileId,
                name: currentUser.fullName,
                avatar: currentUser.avatar,
              }
            : {
                id: currentEntity.entityProfileId,
                name: currentEntity.name,
                avatar: currentEntity.avatar,
              };

          if (prev?.some((a) => a.id === newAttendee.id)) {
            return prev;
          }

          return [newAttendee, ...(prev || [])];
        }
      });
      showToast({
        type: 'success',
        message: isJoining
          ? 'Your attendance has been cancelled'
          : 'Your attendance has been confirmed',
      });
    } catch (e) {
      showToast({
        type: 'error',
        message: isJoining
          ? 'Cannot cancel attendance from your own event'
          : 'Not able to Join now. Please try later',
      });
    }
  };

  const confirmDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteAnnouncementAPI(announcement.announcementId);
      onDelete(announcement.announcementId);
      setDeleteModalVisible(false);
    } catch (e) {
      showToast({
        type: 'error',
        message: 'Not able to delete announcement. Try again after some time',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const formatTime = (utcTimeString: string) => {
    try {
      const date = new Date(utcTimeString);
      if (isNaN(date.getTime())) return 'Invalid time';
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const ampm = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours % 12 || 12;
      const displayMinutes = minutes.toString().padStart(2, '0');
      return `${displayHours}:${displayMinutes} ${ampm}`;
    } catch {
      return 'Invalid time';
    }
  };

  return (
    <View className="pt-4">
      <View className="flex-row justify-between items-start">
        {isEntityAnnouncement ? (
          <View className="flex-row items-start gap-3 flex-shrink">
            <UserAvatar avatarUri={entity?.avatar || ''} name={entity?.name} />
            <View className="flex-1 pr-2">
              <Text className="font-medium text-base text-gray-900">{entity?.name}</Text>
              <Text className="text-gray-500 text-sm">{formatDate(announcement.createdAt)}</Text>
            </View>
          </View>
        ) : (
          <View className="flex-row items-start gap-3 flex-shrink">
            <UserAvatar avatarUri={user.avatar} name={user.name} />
            <View className="flex-1 pr-2">
              <Text className="font-medium text-base text-gray-900">{user.name}</Text>
              <Text
                className="text-gray-500 text-sm max-w-[240px]"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {user.designation.name} {user.entity?.name ? `at ${user.entity.name}` : ''}
              </Text>
              <Text className="text-gray-500 text-sm">{formatDate(announcement.createdAt)}</Text>
            </View>
          </View>
        )}
        {isEntityAnnouncement
          ? isOwnEntityAnnouncement && (
              <Pressable onPress={() => setIsVisible(!isVisible)} className="pl-2">
                <HorizontalEllipsis />
              </Pressable>
            )
          : isUserActive &&
            isOwnAnnouncement && (
              <Pressable onPress={() => setIsVisible(!isVisible)} className="pl-2">
                <HorizontalEllipsis />
              </Pressable>
            )}
      </View>

      <Text className="text-base text-gray-800 w-full whitespace-normal pb-4">
        {announcement.description}
      </Text>

      <View className="border border-borderGrayLight p-4 rounded-lg flex gap-1">
        <Text className="text-lg font-medium">{announcement.title}</Text>

        <Pressable
          className="flex-row gap-2 items-center"
          onPress={() =>
            navigation.navigate('PeopleAttending', { announcementId: announcement.announcementId })
          }
        >
          <StackedAvatar users={topAttendees} showCount={false} maxDisplayed={3} />
          <Text className="text-sm leading-4">{count} attending</Text>
        </Pressable>

        <View className="flex gap-1 my-4">
          {announcement.latitude && announcement.longitude && (
            <View className="flex-row items-center gap-1 flex-wrap">
              <Location width={2.2} height={2.2} />
              <Text>{announcement.city?.name || announcement.addressRawData.text}</Text>
            </View>
          )}
          <View className="flex-row items-center gap-1 flex-wrap">
            <Clock width={3} height={3} />
            <Text className="flex-1">
              {formatTime(announcement.startTime)} - {formatTime(announcement.endTime)}
            </Text>
          </View>
          <Text className="text-sm text-gray-600">
            {formatDate(announcement.startDate)} - {formatDate(announcement.endDate)}
          </Text>
        </View>

        <Button
          variant={isJoining ? 'primaryOutline' : 'outline'}
          className="rounded-lg mb-1"
          label={isJoining ? 'I am attending' : 'Join now'}
          prefixIcon={isJoining ? <Tick /> : undefined}
          onPress={handleJoin}
        />
      </View>

      <BottomSheet visible={isVisible} onClose={() => setIsVisible(false)} onModalHide={() => {}}>
        <OptionsMenu>
          {isOwnAnnouncement && (
            <OptionItem
              icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
              label="Delete announcement"
              textClassName="text-red-500"
              onPress={() => {
                setIsVisible(false);
                setTimeout(() => setDeleteModalVisible(true), 2000);
              }}
            />
          )}
        </OptionsMenu>
      </BottomSheet>

      <CustomModal
        isVisible={deleteModalVisible}
        title="Delete Event"
        description="Are you sure you want to delete this event? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={() => setDeleteModalVisible(false)}
        isConfirming={isDeleting}
      />
    </View>
  );
};
