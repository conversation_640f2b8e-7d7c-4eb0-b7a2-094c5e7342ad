import React from 'react';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { FilterTabItemProps, FilterTabsProps } from './types';

const FilterTabItem: React.FC<FilterTabItemProps> = ({ tab, isActive, onPress }) => {
  return (
    <Pressable onPress={onPress} className="px-4">
      <View className="flex-row items-center gap-2">
        <Text
          className={`text-base font-medium text-center ${isActive ? 'text-green-700' : 'text-gray-500'}`}
        >
          {tab.label}
        </Text>
        {tab.appliedCount > 0 && (
          <View className="rounded-full bg-green-700 px-2 py-0.5">
            <Text className="text-white text-xs font-semibold">
              {tab.appliedCount}
            </Text>
          </View>
        )}
      </View>
      <View className={`w-full h-0.5 mt-1 ${isActive ? 'bg-green-800' : 'bg-gray-100'}`} />
    </Pressable>
  );
};


const FilterTabs: React.FC<FilterTabsProps> = ({ tabs, activeTab, onTabChange }) => {
  const fewTabs = tabs.length <= 4;

  return (
    <View className="h-auto">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={fewTabs ? { flexGrow: 1 } : undefined}
      >
        <View className={fewTabs ? 'flex-row flex-1' : 'flex-row'}>
          {tabs.map((tab) => (
            <FilterTabItem
              key={tab.id}
              tab={tab}
              isActive={activeTab === tab.id}
              onPress={() => onTabChange(tab.id)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default FilterTabs;
