import { Pressable, Text, View } from 'react-native';
import { ToggleSelectionPropsI } from './types';

const ToggleSelection = ({ options, onPress, selectedOption }: ToggleSelectionPropsI) => {
  return (
    <View className="flex-row items-center bg-gray-100 rounded-full p-1">
      {options.map((option) => (
        <Pressable
          key={option.id}
          className={`px-4 py-2 rounded-full ${selectedOption.id === option.id ? 'bg-primaryGreen' : 'bg-transparent'}`}
          onPress={() => onPress(option)}
        >
          <Text
            className={`text-sm font-medium ${selectedOption.id === option.id ? 'text-white' : 'text-gray-600'}`}
          >
            {option.name}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

export default ToggleSelection;
