/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ReactNode } from 'react';

export interface OptionItemProps {
  icon?: React.ReactNode;
  label: string;
  onPress: () => void;
  textClassName?: string;
  disabled?: boolean;
  rightIcon?: React.ReactNode;
}

export interface OptionsMenuProps {
  children: ReactNode;
}
