/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { OptionItemProps, OptionsMenuProps } from './types';

export const OptionsMenu: React.FC<OptionsMenuProps> = ({ children }) => {
  return <View className="px-3 py-5 bg-white">{children}</View>;
};

export const OptionItem: React.FC<OptionItemProps> = ({
  icon,
  label,
  onPress,
  textClassName = 'text-gray-700',
  disabled = false,
  rightIcon,
}) => {
  return (
    <Pressable
      className={`flex-row items-center justify-between py-3.5 px-4 rounded-lg ${disabled ? 'opacity-50' : 'active:bg-gray-100'}`}
      onPress={onPress}
      disabled={disabled}
    >
      <View className="flex-row items-center">
        <View className="mr-4">{icon}</View>
        <Text className={`text-base font-medium ${textClassName}`}>{label}</Text>
      </View>
      {rightIcon && <View className="ml-4">{rightIcon}</View>}
    </Pressable>
  );
};
