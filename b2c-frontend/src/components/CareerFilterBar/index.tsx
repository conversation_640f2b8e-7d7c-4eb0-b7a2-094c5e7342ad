import { useEffect, useState } from 'react';
import { View, Pressable, Text } from 'react-native';
import CareerFilter from '@/src/assets/svgs/CareerFilter';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import BottomSheet from '../Bottomsheet';
import Chip from '../Chip';
import TextInput from '../TextInput';
import { CareerFilterBarPropsI } from './types';
import FilterTabs from '../FilterTabs';
import { useDispatch, useSelector } from 'react-redux';
import { selectFilters } from '@/src/redux/selectors/careers';
import { FilterMap } from './utils';
import { toggleFilter } from '@/src/redux/slices/career/careerSlice';
import { FilterCategory } from '@/src/redux/slices/career/types';
import Button from '../Button';

const CareerFilterBar = ({
    page,
  ellipsesVisible,
  onSearchTextChange,
  searchTextValue,
  bottomSheetItems = [],
  className,
  filterTabs = [],
  onFilterPress,
  onApplyPress
}: CareerFilterBarPropsI) => {
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [activeFilterTab, setActiveFilterTab] = useState(filterTabs[0]?.id ?? '');
  const filterValues = useSelector(selectFilters(page,FilterMap[activeFilterTab as keyof typeof FilterMap]))

  const [isApplyActive,setIsApplyActive] = useState(false);

  const dispatch = useDispatch();

  const handleItemPress = (item: any) => {
    item.onPress();
    setBottomSheetVisible(false);
  };

  const handleFilterPress = () => {
    onFilterPress();
    setFilterVisible(true)
  }

  const handleValuePress = (value: any) => {
    dispatch(toggleFilter({
        page,
        filter:FilterMap[activeFilterTab as keyof typeof FilterMap],
        value,
        selected:value.isSelected
    }))
    setIsApplyActive(true)
  };

  const handleApply = () => {
    setFilterVisible(false)
    setIsApplyActive(false)
    onApplyPress()
  }
  

  return (
    <View className={className}>
      <View className="flex-row items-center w-full gap-3 px-4">
        <View className="flex-1">
          <TextInput
            placeholder="Search"
            className="w-full"
            inputClassName="w-full"
            onChangeText={onSearchTextChange}
            value={searchTextValue}
          />
        </View>
        <Pressable onPress={handleFilterPress}>
          <CareerFilter width={3.5} height={3.5}/>
        </Pressable>
        {ellipsesVisible && (
          <Pressable onPress={() => setBottomSheetVisible(true)}>
            <HorizontalEllipsis width={3.5} height={3.5} />
          </Pressable>
        )}
      </View>
      <BottomSheet
        visible={bottomSheetVisible}
        onClose={() => setBottomSheetVisible(false)}
        onModalHide={() => setBottomSheetVisible(false)}
      >
        <View className="bg-white gap-2 p-4">
          {bottomSheetItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <Pressable
                key={item.id}
                className="flex-row items-center gap-3 p-3 rounded-lg active:bg-gray-100"
                onPress={() => handleItemPress(item)}
              >
                <IconComponent />
                <Text className="text-base text-gray-800">{item.label}</Text>
              </Pressable>
            );
          })}
        </View>
      </BottomSheet>
      <BottomSheet
        visible={filterVisible}
        onClose={() => setFilterVisible(false)}
        onModalHide={() => setFilterVisible(false)}
      >
        <View className="bg-white py-5 min-h-[350] pb-2">
          <FilterTabs tabs={filterTabs} activeTab={activeFilterTab} onTabChange={setActiveFilterTab} />
          <View className="flex-row flex-wrap gap-2.5 m-2">
            {filterValues
              .map((value) => (
                <Chip
                  key={value.id}
                  label={`${value.label}(${value.count})`}
                  className={`my-1.5 mx-0.5 ${value.isSelected ? 'bg-[#8CC653]' : 'bg-gray-200'}`}
                  labelClassName={`font-semibold`}
                  onPress={() => handleValuePress(value)}
                />
              ))}
          </View>
          <View className="absolute bottom-10 left-20 right-20">
            <Button onPress={handleApply} label='Apply' disabled={!isApplyActive} className='w-50'/>
          </View>
        </View>
      </BottomSheet>
    </View>
  );
};

export default CareerFilterBar;
