import { IdLabelI } from '@/src/types/common/data';
import { bottomSheetItemPropsI } from '@/src/screens/Careers/components/Careers/types';

export type CareerFilterBarPropsI = {
    page:string;
  ellipsesVisible: boolean;
  onSearchTextChange: (text: string) => void;
  searchTextValue: string;
  bottomSheetItems?: bottomSheetItemPropsI[];
  className?: string;
  filterTabs: IdLabelAppliedCountI[];
  onFilterPress: () => void;
  onApplyPress:() => void;
};

export type IdLabelAppliedCountI = IdLabelI & { appliedCount: number }
