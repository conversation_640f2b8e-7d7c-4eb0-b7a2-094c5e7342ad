name: 'Build iOS App'
description: 'Builds an iOS app for device distribution with manual signing (uses Info.plist values)'

inputs:
  configuration:
    description: 'Build configuration'
    required: false
    default: 'Release'
  p12-certificate-base64:
    description: 'Base64-encoded P12 certificate file'
    required: true
  p12-password:
    description: 'Password for the P12 certificate'
    required: true
  provisioning-profile-base64:
    description: 'Base64-encoded provisioning profile'
    required: true
  team-id:
    description: 'Apple Developer Team ID'
    required: true
  provisioning-profile-name:
    description: 'Provisioning profile name'
    required: false
    default: 'navicater-profile'
  bundle-identifier:
    description: 'App Bundle Identifier'
    required: false
    default: 'com.navicater'
  release-track:
    description: 'Release track (testflight or app-store)'
    required: false
    default: 'testflight'

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'

    - name: Debug Environment
      shell: bash
      run: |
        echo "DEBUG: Starting iOS build process"
        echo "DEBUG: Current directory: $(pwd)"
        echo "DEBUG: Directory listing: $(ls -la)"
        echo "DEBUG: Node version: $(node -v)"
        echo "DEBUG: NPM version: $(npm -v)"

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.0'
        bundler-cache: true

    - name: Install React Native dependencies
      shell: bash
      working-directory: ios
      run: |
        echo "DEBUG: Installing React Native dependencies"
        echo "DEBUG: Current directory before cd: $(pwd)"
        npm install --prefix ../ react-native
        echo "DEBUG: React Native installation complete"
        echo "DEBUG: npm list --prefix ../ react-native"

    - name: Create .env file
      shell: bash
      run: |
        echo "🔐 Creating .env file"
        cat <<EOF > .env
        API_KEY=${{ env.API_KEY }}
        BASE_URL=${{ env.BASE_URL }}
        AI_URL=${{ env.AI_URL }}
        FIREBASE_ANDROID_APP_ID=${{ env.FIREBASE_ANDROID_APP_ID }}
        FIREBASE_API_KEY=${{ env.FIREBASE_API_KEY }}
        FIREBASE_AUTH_DOMAIN=${{ env.FIREBASE_AUTH_DOMAIN }}
        FIREBASE_DATABASE_URL=${{ env.FIREBASE_DATABASE_URL }}
        FIREBASE_IOS_APP_ID=${{ env.FIREBASE_IOS_APP_ID }}
        FIREBASE_MESSAGING_SENDER_ID=${{ env.FIREBASE_MESSAGING_SENDER_ID }}
        FIREBASE_PROJECT_ID=${{ env.FIREBASE_PROJECT_ID }}
        FIREBASE_STORAGE_BUCKET=${{ env.FIREBASE_STORAGE_BUCKET }}
        IOS_CLIENT_ID=${{ env.IOS_CLIENT_ID }}
        SENTRY_DSN_URL=${{ env.SENTRY_DSN_URL }}
        WEB_CLIENT_ID=${{ env.WEB_CLIENT_ID }}
        ENV=${{ env.ENV }}
        MAPBOX_ACCESS_TOKEN=${{ env.MAPBOX_ACCESS_TOKEN }}
        EOF
        cat .env

    - name: Install CocoaPods
      shell: bash
      run: |
        echo "DEBUG: Installing CocoaPods"
        gem install cocoapods
        echo "DEBUG: CocoaPods version: $(pod --version)"
        cd ios
        echo "DEBUG: Current directory after cd: $(pwd)"
        echo "DEBUG: Cleaning old Pods"
        echo "DEBUG: Directory before cleaning: $(ls -la)"
        rm -rf Pods Podfile.lock
        echo "DEBUG: Directory after cleaning: $(ls -la)"
        echo "DEBUG: Installing pods"
        pod install --repo-update
        echo "DEBUG: Pod installation complete"
        echo "DEBUG: Final directory listing: $(ls -la)"

    - name: Install Apple certificate
      uses: apple-actions/import-codesign-certs@v2
      with:
        p12-file-base64: ${{ inputs.p12-certificate-base64 }}
        p12-password: ${{ inputs.p12-password }}

    - name: Debug certificate installation
      shell: bash
      run: |
        echo "DEBUG: Certificate installation complete"
        echo "DEBUG: Installed certificates:"
        security find-identity -v -p codesigning

    - name: Install provisioning profile
      shell: bash
      run: |
        echo "DEBUG: Installing provisioning profile"
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
        PROVISIONING_PROFILE_PATH=~/Library/MobileDevice/Provisioning\ Profiles/${{ inputs.provisioning-profile-name }}.mobileprovision
        echo "${{ inputs.provisioning-profile-base64 }}" | base64 --decode > "$PROVISIONING_PROFILE_PATH"
        echo "DEBUG: Profile path: $PROVISIONING_PROFILE_PATH"
        echo "DEBUG: Profile size: $(ls -la "$PROVISIONING_PROFILE_PATH" | awk '{print $5}') bytes"
        echo "DEBUG: Provisioning profiles directory content:"
        ls -la ~/Library/MobileDevice/Provisioning\ Profiles/
        echo "DEBUG: Profile UUID:"
        /usr/libexec/PlistBuddy -c 'Print :UUID' /dev/stdin <<< $(security cms -D -i "$PROVISIONING_PROFILE_PATH")

    # 🔒 No version stamping here. We rely on Info.plist literal values you commit.

    - name: Read version from Info.plist (manual control)
      shell: bash
      run: |
        V=$(/usr/libexec/PlistBuddy -c 'Print :CFBundleShortVersionString' ios/navicater/Info.plist)
        B=$(/usr/libexec/PlistBuddy -c 'Print :CFBundleVersion' ios/navicater/Info.plist)
        if [ -z "$V" ] || [ -z "$B" ]; then
          echo "❌ Could not read CFBundleShortVersionString/CFBundleVersion from ios/navicater/Info.plist"
          exit 1
        fi
        echo "📦 Info.plist → version=$V build=$B"

    - name: Clean Xcode Derived Data
      shell: bash
      run: |
        echo "DEBUG: Cleaning DerivedData"
        echo "DEBUG: DerivedData size before cleaning: $(du -sh ~/Library/Developer/Xcode/DerivedData 2>/dev/null || echo 'not found')"
        rm -rf ~/Library/Developer/Xcode/DerivedData
        echo "DEBUG: DerivedData cleaned"

    - name: Generate exportOptions.plist
      shell: bash
      run: |
        echo "DEBUG: Generating exportOptions.plist for manual signing"
        cd ios
        echo "DEBUG: Using TEAM_ID: ${{ inputs.team-id }}"
        echo "DEBUG: Using Provisioning profile: ${{ inputs.provisioning-profile-name }}"
        echo "DEBUG: Using Bundle ID: ${{ inputs.bundle-identifier }}"

        cat <<EOF > exportOptions.plist
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
          <key>method</key>
          <string>app-store-connect</string>
          <key>uploadBitcode</key>
          <false/>
          <key>uploadSymbols</key>
          <true/>
          <key>compileBitcode</key>
          <false/>
          <key>destination</key>
          <string>export</string>
          <key>signingStyle</key>
          <string>manual</string>
          <key>teamID</key>
          <string>${{ inputs.team-id }}</string>
          <key>stripSwiftSymbols</key>
          <true/>
          <key>provisioningProfiles</key>
          <dict>
            <key>${{ inputs.bundle-identifier }}</key>
            <string>${{ inputs.provisioning-profile-name }}</string>
          </dict>
        </dict>
        </plist>
        EOF
        echo "DEBUG: exportOptions.plist created:"
        cat exportOptions.plist

    - name: Archive and Export IPA
      shell: bash
      run: |
        set -e
        cd ios
        echo "DEBUG: Starting archive process with manual signing"
        echo "DEBUG: Using configuration: ${{ inputs.configuration }}"
        echo "DEBUG: Using provisioning profile: ${{ inputs.provisioning-profile-name }}"
        echo "DEBUG: Using team ID: ${{ inputs.team-id }}"
        echo "DEBUG: Using bundle identifier: ${{ inputs.bundle-identifier }}"

        echo "DEBUG: Xcode version:" && xcodebuild -version
        echo "DEBUG: Available schemes:" && xcodebuild -workspace navicater.xcworkspace -list
        echo "DEBUG: Available SDKs:" && xcodebuild -showsdks
        echo "DEBUG: Available signing identities:" && security find-identity -v -p codesigning

        mkdir -p build

        echo "DEBUG: xcodebuild archive"
        xcodebuild archive \
          -workspace navicater.xcworkspace \
          -scheme navicater \
          -configuration ${{ inputs.configuration }} \
          -sdk iphoneos \
          -destination 'generic/platform=iOS' \
          -archivePath build/navicater.xcarchive \
          CODE_SIGN_STYLE=Manual \
          DEVELOPMENT_TEAM="${{ inputs.team-id }}" \
          PROVISIONING_PROFILE_SPECIFIER="${{ inputs.provisioning-profile-name }}" \
          -verbose 2>&1 | tee archive.log

        ARCHIVE_EXIT_CODE=${PIPESTATUS[0]}
        echo "DEBUG: Archive command exit code: $ARCHIVE_EXIT_CODE"
        if [ $ARCHIVE_EXIT_CODE -ne 0 ]; then
          echo "ERROR: Archive failed with exit code $ARCHIVE_EXIT_CODE"
          echo "DEBUG: Last 100 lines of archive log:" && tail -100 archive.log
          exit $ARCHIVE_EXIT_CODE
        fi

        echo "DEBUG: xcodebuild -exportArchive"
        xcodebuild -exportArchive \
          -archivePath build/navicater.xcarchive \
          -exportOptionsPlist exportOptions.plist \
          -exportPath build/export \
          -verbose 2>&1 | tee export.log

        EXPORT_EXIT_CODE=${PIPESTATUS[0]}
        echo "DEBUG: Export command exit code: $EXPORT_EXIT_CODE"
        if [ $EXPORT_EXIT_CODE -ne 0 ]; then
          echo "ERROR: Export failed with exit code $EXPORT_EXIT_CODE"
          echo "DEBUG: Last 100 lines of export log:" && tail -100 export.log
          exit $EXPORT_EXIT_CODE
        fi

        echo "DEBUG: Looking for IPA file"
        IPA_PATH=$(find build/export -name "*.ipa" | head -n 1)
        if [ -n "$IPA_PATH" ]; then
          echo "DEBUG: IPA found at $IPA_PATH"
          cp "$IPA_PATH" navicater.ipa
          ls -la navicater.ipa
        else
          echo "ERROR: IPA not found"
          find build/export -type f -exec ls -la {} \;
          exit 1
        fi

    - name: Display Archive Logs on Failure
      if: failure()
      shell: bash
      run: |
        echo "=== DISPLAYING FULL LOGS DUE TO FAILURE ==="
        cd ios
        [ -f archive.log ] && (echo "=== ARCHIVE LOG START ==="; cat archive.log; echo "=== ARCHIVE LOG END ===") || echo "Archive log not found"
        [ -f export.log ] && (echo "=== EXPORT LOG START ==="; cat export.log; echo "=== EXPORT LOG END ===") || echo "Export log not found"

    - name: Upload xcodebuild logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: xcodebuild-logs
        path: |
          ios/archive.log
          ios/export.log

    - name: Upload iOS build artifact
      if: success()
      uses: actions/upload-artifact@v4
      with:
        name: ios-build
        path: ios/navicater.ipa
        retention-days: 7
