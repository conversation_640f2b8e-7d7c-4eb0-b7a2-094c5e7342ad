import { prismaPG } from '@config/db';
import type {
  NotificationFetchManyResultI,
  NotificationFetchManyResultItemI,
} from '@interfaces/communication/notification';
import type { PostForNotificationI } from '@interfaces/feed/post';

import {
  NotificationCreateOneSchema,
  type NotificationCreateOneI,
  type NotificationFetchManyI,
} from '@schemas/communication/notification';
import AppError from '@classes/AppError';
import CoreCommunicationModule from './communication';
import { toAny } from '@utils/data/object';
import { FastifyStateI } from '@interfaces/common/declaration';
import Company from '@modules/company';
import { UUIDI } from '@schemas/common/common';
import { uniqueArrayStr } from '@utils/data/array';
import User from '@modules/user';
import { NotificationProfileTypeI } from '@consts/communication/notification';

const NotificationModule = {
  createOne: async (params: NotificationCreateOneI) => {
    try {
      const { error: notificationCreateOneError } = NotificationCreateOneSchema.safeParse(params);

      if (notificationCreateOneError) {
        throw new AppError('NF001');
      }
      const notificationCreateOneData = toAny(params);
      let profileIds: UUIDI[] = [];
      let actorProfileName: string = notificationCreateOneData?.actorProfileName;
      if (params?.actorProfileType === 'ENTITY') {
        if (!actorProfileName?.length) {
          const actorProfileTemp = await Company.EntityProfileModule.fetchByEntity(
            notificationCreateOneData?.actorProfileId,
            {
              name: true,
            },
          );
          if (actorProfileTemp?.name) {
            actorProfileName = actorProfileTemp.name;
          }
        }
      } else {
        if (!actorProfileName?.length) {
          const actorProfileTemp = await User.ProfileModule.fetchData({ id: params.actorProfileId }, { name: true });
          if (actorProfileTemp?.name) {
            actorProfileName = actorProfileTemp.name;
          }
        }
      }
      if ((['ALL', 'ENTITY'] as NotificationProfileTypeI[]).includes(params?.receiverProfileType)) {
        const profileIdsTemp: UUIDI[] = await Company.EntityMemberModule.fetchAllProfileIds(
          params.receiverEntityProfileId,
          params?.actorProfileType === 'ENTITY' ? params?.actorProfileId : null,
        );
        profileIds.push(...profileIdsTemp);
      }
      if (notificationCreateOneData?.profileIds?.length) {
        profileIds.push(...(notificationCreateOneData?.profileIds ?? []));
      }
      if (profileIds?.length) {
        profileIds = uniqueArrayStr(profileIds);
      }
      await CoreCommunicationModule.createOne(notificationCreateOneData?.topic, {
        category: 'NOTIFICATION',
        profileId: notificationCreateOneData?.profileId,
        profileIds: profileIds?.length ? profileIds : undefined,
        firebaseTopic: notificationCreateOneData?.firebaseTopic,
        notification: {
          actorProfileId: notificationCreateOneData?.actorProfileId,
          actorProfileName: actorProfileName,
          chatType: notificationCreateOneData?.chatType,
          commentId: notificationCreateOneData?.commentId,
          parentCommentId: notificationCreateOneData?.parentCommentId,
          postId: notificationCreateOneData?.postId,
          postText: notificationCreateOneData?.postText,
          receiverProfileId: notificationCreateOneData?.receiverProfileId,
          receiverProfileIds: notificationCreateOneData?.receiverProfileIds,
          screen: notificationCreateOneData?.screen,
          type: notificationCreateOneData?.type,
          requestId: notificationCreateOneData?.requestId,
          voteType: notificationCreateOneData?.voteType,
          questionId: notificationCreateOneData?.questionId,
          questionsCount: notificationCreateOneData?.questionCount,
          questionTitle: notificationCreateOneData?.questionTitle,
          answerId: notificationCreateOneData?.answerId,
          answerText: notificationCreateOneData?.answerText,
        },
      });
    } catch (_error) {
      //
    }
  },
  fetchMany: async (_state: FastifyStateI, data: NotificationFetchManyI): Promise<NotificationFetchManyResultI> => {
    const actorUserProfileIds: UUIDI[] = data.items.map((item) => item.actorProfileId);

    const postIds = Array.from(new Set(data.items.map((item) => item.postId).filter(Boolean)));

    const [userProfiles, posts] = await Promise.all([
      actorUserProfileIds?.length
        ? prismaPG.profile.findMany({
            select: {
              id: true,
              avatar: true,
              name: true,
            },
            where: {
              id: { in: actorUserProfileIds },
            },
          })
        : [],
      postIds.length
        ? prismaPG.$queryRaw<PostForNotificationI[]>`
            SELECT
              p.id,
              LEFT(p.caption, 20) AS "caption",
              (
                SELECT pm."fileUrl"
                FROM "feed"."PostMedia" pm
                WHERE pm."postId" = p.id
                ORDER BY pm."createdAt" ASC
                LIMIT 1
              ) AS "image"
            FROM
              "feed"."Post" p
            WHERE
              p.id = ANY(${postIds}::uuid[])
          `
        : [],
    ]);
    const foundUserProfileIds = new Set<UUIDI>(userProfiles?.map(({ id }) => id));

    const entityProfileIdsSet = new Set<UUIDI>();

    actorUserProfileIds?.forEach((profileId: UUIDI) => {
      if (!foundUserProfileIds.has(profileId)) {
        entityProfileIdsSet.add(profileId);
      }
    });
    const entityProfiles = entityProfileIdsSet?.size
      ? await prismaPG.entityProfile.findMany({
          select: {
            id: true,
            avatar: true,
            name: true,
          },
          where: {
            id: {
              in: Array.from(entityProfileIdsSet),
            },
          },
        })
      : [];
    return {
      profiles: [
        ...userProfiles.map((profile) => ({ ...profile, type: 'USER' }) as NotificationFetchManyResultItemI),
        ...entityProfiles.map((profile) => ({ ...profile, type: 'ENTITY' }) as NotificationFetchManyResultItemI),
      ] as NotificationFetchManyResultItemI[],
      posts,
    };
  },
};
export default NotificationModule;
