import AppError from "@classes/AppError";
import { prismaPG } from "@config/db";
import type { DBDataTypeI } from "@consts/common/data";
import { PAGINATION } from "@consts/common/pagination";
import type { DocumentCategoryI } from "@consts/document/documentType";
import { ObjUnknownI, TotalDataI } from "@interfaces/common/data";
import type {
    DocumentTypeClientI,
    DocumentTypeCreateParamsI,
    DocumentTypeUpdateParamsI,
} from '@interfaces/document/documentType';
import type { IdNameSelectI } from "@consts/master/common";
import { IdTypeI } from "@schemas/common/common";
import { Prisma } from "@prisma/postgres";

export const DocumentTypeModule = {
    // Fetch By id
    fetchById: async (
        filters: IdTypeI,
        select: IdNameSelectI = { id: true, name: true },
        _isThrowingError: boolean = true,
    ): Promise<DocumentTypeClientI> => {

        if (filters.dataType === 'raw') {
            const documentTypeRawDataResult = await prismaPG.documentTypeRawData.findFirst({
                where: { id: filters.id },
                select,
            });

            if (documentTypeRawDataResult) {
                return {
                    ...(documentTypeRawDataResult as ObjUnknownI),
                    dataType: 'raw',
                } as DocumentTypeClientI;
            }
        } else if (filters.dataType === 'master') {
            const documentTypeResult = await
                prismaPG.documentType.findFirst({
                    where: { id: filters.id },
                    select,
                });

            if (documentTypeResult) {
                return {
                    ...(documentTypeResult as ObjUnknownI),
                    dataType: 'master',
                } as DocumentTypeClientI;
            }
        } else {
            // Try both if dataType not specified
            const masterType = await prismaPG.documentType.findFirst({
                where: { id: filters.id },
                select,
            });

            if (masterType) {
                return {
                    ...(masterType as ObjUnknownI), dataType:
                        'master'
                } as DocumentTypeClientI;
            }

            const rawType = await
                prismaPG.documentTypeRawData.findFirst({
                    where: { id: filters.id },
                    select,
                });

            if (rawType) {
                return { ...(rawType as ObjUnknownI), dataType: 'raw' } as DocumentTypeClientI;
            }
        }

        if (_isThrowingError) {
            throw new AppError('DOC001');
        }
    },
    // fetch by category
    fetchForClient: async (
        search?: string,
        category?: DocumentCategoryI,
        { page, pageSize } = PAGINATION,
    ): Promise<TotalDataI<DocumentTypeClientI>> => {
        search = search?.trim()?.toLowerCase();

        const [documentTypesResultTemp, documentTypesTotalResult] =
          await Promise.all([
            prismaPG.$queryRaw<DocumentTypeClientI[]>`
            SELECT
              dt."id",
              dt."name",
              dt."category",
              dt."dataType"
            FROM
            (
              (SELECT
                dt."id",
                dt."name",
                dt."category",
                'master' AS "dataType"
              FROM
                "document"."DocumentType" dt
              WHERE
                dt."name" ILIKE ${ '%' + search + '%'}
                ${category ? Prisma.sql`AND dt."category" = ${category}::"document"."DocumentCategoryE"` : Prisma.empty})

                UNION ALL

                (SELECT
                dtrw."id",
                dtrw."name",
                dtrw."category",
                'raw' AS "dataType"
              FROM
                "rawData"."DocumentTypeRawData" dtrw
              WHERE
                dtrw."name" ILIKE ${search + '%'}
                ${category ? Prisma.sql`AND dtrw."category" = ${category}::"document"."DocumentCategoryE"` : Prisma.empty})
            ) dt
            ORDER BY
              dt."name" ASC
            OFFSET ${page * pageSize}
            LIMIT ${pageSize}
            `,
            prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_documentType AS (
                SELECT 1
                FROM "document"."DocumentType" dt
                WHERE
                  dt."name" ILIKE ${search + '%'}
                  ${category ? Prisma.sql`AND dt."category" = ${category}::"document"."DocumentCategoryE"` : Prisma.empty}
              ),
              raw_documentType AS (
                SELECT 1
                FROM "rawData"."DocumentTypeRawData" dtrw
                WHERE
                  dtrw."name" ILIKE ${search + '%'}
                  ${category ? Prisma.sql`AND dtrw."category" = ${category}::"document"."DocumentCategoryE"` : Prisma.empty}
              ),
              combined AS (
                SELECT * FROM master_documentType
                UNION ALL
                SELECT * FROM raw_documentType
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `,
          ]);

        return {
            data: documentTypesResultTemp,
            total: documentTypesTotalResult[0]?.total || 0,
        };
    },
    // create raw document type
    fetchsert: async (params: DocumentTypeCreateParamsI):
        Promise<DocumentTypeClientI> => {
        const cleanName = params.name.trim().toLowerCase();
        const existingDocumentTypeResult = await
            prismaPG.$queryRaw<DocumentTypeClientI[]>`
      SELECT *
      FROM (
        (SELECT
          dt."id",
          dt."name",
          dt."category"::text,
          dt."createdAt",
          dt."updatedAt",
          NULL as "dataStatus",
          'master' AS "dataType"
        FROM "document"."DocumentType" dt
        WHERE
          dt."name" ILIKE ${cleanName}
          AND dt."category" = ${params.category}::"document"."DocumentCategoryE"
        LIMIT 1)

        UNION ALL

        (SELECT
          dtrw."id",
          dtrw."name",
          dtrw."category"::text,
          dtrw."createdAt",
          dtrw."updatedAt",
          dtrw."dataStatus"::text,
          'raw' AS "dataType"
        FROM "rawData"."DocumentTypeRawData" dtrw
        WHERE
          dtrw."name" ILIKE ${cleanName}
          AND dtrw."category" = ${params.category}::"document"."DocumentCategoryE"
          AND dtrw."dataStatus" IN ('PENDING', 'APPROVED')
        LIMIT 1)
      ) AS combined
      LIMIT 1
    `;
        const existingDocumentType = existingDocumentTypeResult[0];

        if (existingDocumentType) {
            return existingDocumentType;
        }

        const result = await prismaPG.documentTypeRawData.create({
            data: {
                name: cleanName,
                category: params.category,
                dataStatus: 'PENDING',
            },
        });

        if (!result?.id) {
            throw new AppError('DOC005');
        }

        return {
            id: result.id,
            name: result.name,
            category: result.category,
            dataType: 'raw' as DBDataTypeI,
        };
    },

    // Update raw document type
    updateRawDocumentType: async (
        id: string,
        params: DocumentTypeUpdateParamsI
    ): Promise<DocumentTypeClientI> => {
        const existingRawType = await
            prismaPG.documentTypeRawData.findFirst({
                where: { id },
            });

        if (!existingRawType) {
            throw new AppError('DOC001');
        }

        if (existingRawType.dataStatus === 'APPROVED') {
            throw new AppError('DOC003');
        }

        const updatedType = await
            prismaPG.documentTypeRawData.update({
                where: { id },
                data: params,
            });

        return { ...updatedType, dataType: 'raw' };
    },
}
