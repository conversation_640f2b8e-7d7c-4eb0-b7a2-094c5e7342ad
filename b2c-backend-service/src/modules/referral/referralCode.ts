import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { REWARD_TYPE_IDS } from '@consts/reward/reward';
import RewardModule from '@modules/reward';
import { randomBytes } from 'crypto';

const generateReferralCode = (length = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const bytes = randomBytes(length);
  return Array.from(bytes)
    .map((b) => chars[b % chars.length])
    .join('');
};

export const ReferralCodeModule = {
  findByProfileId: async (profileId: string) => {
    return prismaPG.referralCodes.findFirst({
      where: { profileId },
    });
  },

  findByCode: async (code: string) => {
    return prismaPG.referralCodes.findFirst({
      where: { code },
    });
  },

  createReferralCode: async (profileId: string, code: string) => {
    return prismaPG.referralCodes.create({
      data: {
        profileId,
        code,
      },
    });
  },

  ensureReferralCode: async (profileId: string) => {
    const existing = await ReferralCodeModule.findByProfileId(profileId);
    if (existing) return existing.code;

    let code = '';
    let isUnique = false;

    while (!isUnique) {
      code = generateReferralCode(8);
      const found = await ReferralCodeModule.findByCode(code);
      if (!found) isUnique = true;
    }

    return (await ReferralCodeModule.createReferralCode(profileId, code)).code;
  },

  createReferralStatus: async (referredProfileId: string, referralCode: string) => {
    const referralCodeRecord = await prismaPG.referralCodes.findUnique({
      where: { code: referralCode },
    });

    if (!referralCodeRecord) {
      throw new AppError('REF001');
    }

    return prismaPG.referralStatus.create({
      data: {
        referredProfileId,
        referrerProfileId: referralCodeRecord.profileId,
        referralCodeId: referralCodeRecord.id,
        isReferrerRewarded: false,
      },
    });
  },

  completeOnboarding: async (referredProfileId: string) => {
    const referral = await prismaPG.referralStatus.findUnique({
      where: { referredProfileId },
      select: {
        isReferrerRewarded: true,
        isReferredRewarded: true,
        referrerProfileId: true,
      },
    });

    if (!referral) {
      throw new AppError('REF001');
    }

    const updates: {
      isReferrerRewarded?: boolean;
      isReferredRewarded?: boolean;
    } = {};

    const rewardPromises: Promise<unknown>[] = [];

    if (!referral.isReferrerRewarded && referral.referrerProfileId) {
      rewardPromises.push(
        RewardModule.RewardActionModule.assignReward({
          profileId: referral.referrerProfileId,
          rewardId: REWARD_TYPE_IDS.INVITE_CONTRIBUTION,
        }),
      );
      updates.isReferrerRewarded = true;
    }

    if (!referral.isReferredRewarded) {
      rewardPromises.push(
        RewardModule.RewardActionModule.assignReward({
          profileId: referredProfileId,
          rewardId: REWARD_TYPE_IDS.INVITE_CONTRIBUTION,
        }),
      );
      updates.isReferredRewarded = true;
    }

    if (rewardPromises.length > 0) {
      await Promise.all(rewardPromises);
      await prismaPG.referralStatus.update({
        where: { referredProfileId },
        data: updates,
      });
    }

    return true;
  },

  getRewardedReferralsByReferrerId: async (referrerProfileId: string, page = 1, pageSize = 10) => {
    const referralCode = await prismaPG.referralCodes.findFirst({
      where: { profileId: referrerProfileId },
    });

    if (!referralCode) {
      return {
        total: 0,
        data: [],
        totalPointsEarned: 0,
      };
    }

    const [total, referralStatuses] = await Promise.all([
      prismaPG.referralStatus.count({
        where: {
          referralCodeId: referralCode.id,
          isReferrerRewarded: true,
        },
      }),
      prismaPG.referralStatus.findMany({
        where: {
          referralCodeId: referralCode.id,
          isReferrerRewarded: true,
        },
        select: {
          referredProfileId: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
    ]);

    const referredProfileIds = referralStatuses.map((r) => r.referredProfileId);

    const profiles = await prismaPG.profile.findMany({
      where: {
        id: {
          in: referredProfileIds,
        },
      },
      select: {
        id: true,
        name: true,
        avatar: true,
        designationAlternativeId: true,
        designationRawDataId: true,
        designationText: true,
        entityId: true,
        entityRawDataId: true,
        entityText: true,
      },
    });

    const formattedProfiles = profiles.map((item) => ({
      id: item.id,
      name: item.name,
      avatar: item.avatar,
      designation: item.designationAlternativeId
        ? {
            id: item.designationAlternativeId,
            name: item.designationText,
            dataType: 'master',
          }
        : item.designationRawDataId
          ? {
              id: item.designationRawDataId,
              name: item.designationText,
              dataType: 'raw',
            }
          : null,
      entity: item.entityId
        ? {
            id: item.entityId,
            name: item.entityText,
            dataType: 'master',
          }
        : item.entityRawDataId
          ? {
              id: item.entityRawDataId,
              name: item.entityText,
              dataType: 'raw',
            }
          : null,
    }));

    const pointsPerReferral = 10;

    return {
      total,
      data: formattedProfiles,
      totalPointsEarned: total * pointsPerReferral,
    };
  },
  isReferredProfile: async (profileId: string): Promise<boolean> => {
    const referralStatus = await prismaPG.referralStatus.findUnique({
      where: { referredProfileId: profileId },
      select: { referredProfileId: true },
    });

    return !!referralStatus;
  },
};
