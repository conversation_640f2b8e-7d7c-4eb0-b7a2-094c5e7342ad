import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FetchCommunityMemberI, CommunityMemberUpsertResultI } from '@interfaces/forum/member';
import { Prisma, MemberTypeE, CommunityMember } from '@prisma/postgres';
import {
  CommunityMemberFetchForClientI,
  CommunityMemberUpsertI,
  CommunityMembersFetchManyI,
} from '@schemas/forum/member';
import { errorHandler } from '@utils/errors/handler';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { CommunityModule } from './community/community';
const CommunityMemberModule = {
  fetchMember: async (
    { communityId, profileId, entityProfileId }: CommunityMemberFetchForClientI,
    throwsError: boolean = true,
  ): Promise<Pick<CommunityMember, 'type'> | null> => {
    const memberResult = await prismaPG.communityMember.findFirst({
      select: {
        type: true,
      },
      where: entityProfileId
        ? {
            communityId,
            entityProfileId,
            profileId: null,
          }
        : {
            communityId,
            entityProfileId: null,
            profileId,
          },
    });
    if (throwsError && !memberResult) {
      throw new AppError('FMMB007');
    }
    return memberResult;
  },
  fetchMembersForCommunity: async (
    state: FastifyStateI,
    { communityId, cursorId, pageSize, type, search }: CommunityMembersFetchManyI,
  ): Promise<{
    total: number;
    nextCursorId: number | null;
    data: Array<{
      Profile: {
        id: string;
        avatar: string | null;
        name: string;
        designationText: string | null;
        designationAlternativeId: string | null;
        designationRawDataId: string | null;
        entityText: string | null;
        entityId: string | null;
        entityRawDataId: string | null;
      };
      role: MemberTypeE;
    }>;
  }> => {
    try {
      const selfProfileId = state.profileId;
      if (!selfProfileId) throw new AppError('AUTH020');
      await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
      const adminMember = await prismaPG.communityMember.findFirst({
        where: { communityId, profileId: selfProfileId, type: MemberTypeE.ADMIN },
      });
      if (!adminMember) throw new AppError('CMTY004');

      const whereConditions: Prisma.CommunityMemberWhereInput = {
        communityId,
      };

      if (type && type !== 'ALL') {
        whereConditions.type = type;
      }

      const profileWhere: Prisma.ProfileWhereInput = {};
      if (search && search.trim()) {
        const searchTerm = search.trim();
        profileWhere.OR = [
          { name: { contains: searchTerm, mode: 'insensitive' } },
          { username: { contains: searchTerm, mode: 'insensitive' } },
        ];
      }

      if (cursorId !== null && cursorId !== undefined && typeof cursorId === 'number') {
        profileWhere.cursorId = { lt: cursorId };
      }

      const baseWhere = {
        ...whereConditions,
        Profile: profileWhere,
      };

      const [total, members] = await Promise.all([
        prismaPG.communityMember.count({
          where: {
            ...whereConditions,
            Profile:
              search && search.trim()
                ? {
                    OR: [
                      { name: { contains: search.trim(), mode: 'insensitive' } },
                      { username: { contains: search.trim(), mode: 'insensitive' } },
                    ],
                  }
                : {},
          },
        }),

        prismaPG.communityMember.findMany({
          where: baseWhere,
          include: {
            Profile: {
              select: {
                id: true,
                cursorId: true,
                avatar: true,
                name: true,
                designationText: true,
                designationAlternativeId: true,
                designationRawDataId: true,
                entityText: true,
                entityId: true,
                entityRawDataId: true,
              },
            },
          },
          orderBy: {
            Profile: {
              cursorId: 'desc',
            },
          },
          take: pageSize,
        }),
      ]);

      const nextCursorId =
        members.length === pageSize && members.length > 0
          ? Number(members[members.length - 1]?.Profile?.cursorId)
          : null;

      return {
        total,
        nextCursorId,
        data: members.map((member) => ({
          Profile: {
            id: member.Profile.id,
            avatar: member.Profile.avatar,
            name: member.Profile.name,
            designationText: member.Profile.designationText,
            designationAlternativeId: member.Profile.designationAlternativeId,
            designationRawDataId: member.Profile.designationRawDataId,
            entityText: member.Profile.entityText,
            entityId: member.Profile.entityId,
            entityRawDataId: member.Profile.entityRawDataId,
          },
          role: member.type,
        })),
      };
    } catch (error) {
      console.error('Error in fetchMembersForCommunity:', error);
      errorHandler(error);
      throw error;
    }
  },
  fetchMemberForClient: async ({
    profileId,
    communityId,
  }: CommunityMemberFetchForClientI): Promise<FetchCommunityMemberI> => {
    const result = await prismaPG.$queryRaw<FetchCommunityMemberI>`
      SELECT
        cm."communityId",
        cm."profileId",
        cm."type",
        p."username",
        p."name" AS "profileName",
        p."avatar",
        c."name" AS "communityName"
      FROM "forum"."CommunityMember" cm
      JOIN "user"."Profile" p ON cm."profileId" = p."id"
      JOIN "forum"."Community" c ON cm."communityId" = c."id"
      WHERE
        cm."profileId" = ${profileId}::uuid
        ${communityId ? Prisma.sql`AND cm."communityId" = ${communityId}::uuid` : Prisma.empty}
    `;

    if (!result) {
      throw new AppError('FMMB001');
    }

    return {
      type: result[0].type,
      profile: {
        id: result[0].profileId,
        username: result[0].username,
        name: result[0].profileName,
        avatar: result[0].avatar,
      },
      community: {
        id: result[0].communityId,
        name: result[0].communityName,
      },
    };
  },
  leaveCommunity: async ({
    profileId,
    communityId,
    entityProfileId,
  }: CommunityMemberFetchForClientI): Promise<void> => {
    try {
      const member = await prismaPG.communityMember.findFirst({
        where: { profileId, communityId },
        select: {
          type: true,
        },
      });
      if (!member) {
        throw new AppError('FMMB001');
      }

      if (member.type === MemberTypeE.ADMIN) {
        const adminCount = await prismaPG.communityMember.count({
          where: {
            communityId,
            type: MemberTypeE.ADMIN,
          },
        });

        if (adminCount <= 1) {
          throw new AppError('FMMB003');
        }
      }
      await prismaPG.communityMember.delete({
        where: (entityProfileId
          ? {
              communityId,
              entityProfileId,
              profileId: null,
            }
          : {
              communityId,
              entityProfileId: null,
              profileId,
            }) as Prisma.CommunityMemberWhereUniqueInput,
      });
      await prismaPG.community.update({
        where: { id: communityId },
        data: {
          memberCount: {
            decrement: 1,
          },
        },
      });
    } catch (error) {
      errorHandler(error);
    }
  },
  upsertMember: async (
    state: FastifyStateI,
    { communityId, profileId, type }: CommunityMemberUpsertI,
  ): Promise<CommunityMemberUpsertResultI> => {
    try {
      const { profileId: selfProfileId, entityProfileId: selfEntityProfileId } = state;
      const community = await prismaPG.community.findUnique({
        where: { id: communityId },
      });
      if (!community) throw new AppError('CMTY007');
      const profile = await prismaPG.profile.findUnique({
        where: { id: profileId },
      });
      if (!profile) throw new AppError('PFL001');
      const adminMember = await prismaPG.communityMember.findFirst({
        where: selfEntityProfileId
          ? {
              communityId,
              entityProfileId: selfEntityProfileId,
              profileId: null,
            }
          : {
              communityId,
              entityProfileId: null,
              profileId: selfProfileId,
            },
      });
      if (!adminMember || adminMember.type !== MemberTypeE.ADMIN) {
        throw new AppError('FMMB002');
      }
      let action: 'created' | 'updated' = 'updated';
      await prismaPG.$transaction(async (txn) => {
        const updated = await txn.communityMember.updateMany({
          where: {
            communityId,
            profileId,
          },
          data: { type },
        });

        if (updated.count === 0) {
          await txn.communityMember.create({
            data: {
              communityId,
              profileId,
              type,
            },
          });

          await txn.community.update({
            where: { id: communityId },
            data: {
              memberCount: {
                increment: 1,
              },
            },
          });
          action = 'created';
        }
      });
      return {
        action,
        member: {
          profileId,
          type,
        },
        communityId,
      };
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  },
};

export default CommunityMemberModule;
