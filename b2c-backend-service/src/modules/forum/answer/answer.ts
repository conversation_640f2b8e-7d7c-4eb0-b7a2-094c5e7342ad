import type { FastifyStateI } from '@interfaces/common/declaration';
import type { AnswerUpdateStatusI, ForumAnswerCreateOneI, ForumAnswerFetchManyI } from '@schemas/forum/answer';
import { CommunityModule } from '../community/community';
import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import { Answer, Prisma } from '@prisma/postgres';
import { QuestionModule } from '../question/question';
import type { RouteParamsI } from '@schemas/common/common';
import type {
  ForumAnswerFetchManyResultI,
  ForumAnswerI,
  ForumAnswerWithProfileI,
  ForumAnswerWithProfileSQLI,
} from '@interfaces/forum/answer';
import { pick } from '@utils/data/object';
import CommunityMemberModule from '../member';
import { MemberTypeI } from '@consts/forum/member';
import { TotalI } from '@interfaces/common/db';
import { generateSlug } from '@utils/data/slugger';
import CommunicationModule from '@modules/communication';
import { ellipsis } from '@utils/data/string';
import { REWARD_TYPE_IDS } from '@consts/reward/reward';
import RewardModule from '@modules/reward';

export const AnswerModule = {
  fetchById: async (
    { id }: Pick<Prisma.AnswerWhereUniqueInput, 'id'>,
    select: Prisma.AnswerSelect = {
      id: true,
      slug: true,
    },
  ) => {
    const answerResult = await prismaPG.answer.findUnique({
      select,
      where: {
        id,
      },
    });
    if (!answerResult) {
      throw new AppError('FMANS002');
    }
    return answerResult;
  },
  fetchBySlug: async (
    { slug }: Pick<Prisma.AnswerWhereUniqueInput, 'slug'>,
    select: Prisma.AnswerSelect = {
      id: true,
    },
  ): Promise<Answer> => {
    const answerResult = await prismaPG.answer.findUnique({
      select,
      where: {
        slug,
      },
    });
    if (!answerResult) {
      throw new AppError('FMANS002');
    }
    return answerResult;
  },
  createOne: async (
    state: FastifyStateI,
    { text, questionId, files }: ForumAnswerCreateOneI,
  ): Promise<ForumAnswerI> => {
    const selfProfileId = state.profileId;
    const questionResult = await QuestionModule.fetchById(
      { id: questionId },
      { communityId: true, profileId: true, title: true, isLive: true, liveStartedAt: true, type: true },
    );
    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const input: Prisma.AnswerUncheckedCreateInput = {
      profileId: selfProfileId,
      communityId,
      text,
      questionId,
      slug: await generateSlug(`answer-to-${questionResult.title}`, { maxLength: 50 }),
    };
    if (files?.length) {
      input.AnswerMedia = {
        createMany: {
          data: files.map((file) => ({ communityId, fileUrl: file.fileUrl, fileExtension: file.fileExtension })),
        },
      };
    }
    const [answerResult, _questionResult, selfProfileResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.answer.create({
            data: input,
            select: { id: true, cursorId: true, slug: true, text: true, createdAt: true },
          }),
          txn.question.update({
            data: {
              answerCount: {
                increment: 1,
              },
            },
            where: {
              id: questionId,
            },
            select: { id: true },
          }),
          txn.profile.findUnique({
            where: {
              id: selfProfileId,
            },
            select: {
              name: true,
            },
          }),
        ]),
      {
        timeout: 30000,
      },
    );

    if (!answerResult) {
      throw new AppError('FMANS001');
    }

    const isNormalQuestion = questionResult.type === 'NORMAL';

    const baseRewardId = isNormalQuestion ? REWARD_TYPE_IDS.QNA_ANSWER : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER;

    await RewardModule.RewardActionModule.assignReward({
      profileId: selfProfileId,
      rewardId: baseRewardId,
    });

    if (selfProfileId !== questionResult?.profileId)
      await CommunicationModule.NotificationModule.createOne({
        actorProfileId: selfProfileId,
        actorProfileName: selfProfileResult?.name ?? 'Unknown',
        answerId: answerResult.id,
        answerText: answerResult.text,
        questionId,
        questionTitle: ellipsis(questionResult?.title),
        receiverProfileId: questionResult?.profileId,
        receiverProfileType: 'USER',
        topic: 'communication_topic',
        type: 'FORUM_ANSWER',
      });
    return { cursorId: Number(answerResult.cursorId), id: answerResult.id };
  },
  deleteOne: async (state: FastifyStateI, { id: answerId }: RouteParamsI): Promise<void> => {
    const selfProfileId = state.profileId;
    const answerResult = await prismaPG.answer.findUnique({
      where: { id: answerId },
      select: {
        profileId: true,
        questionId: true,
        Community: {
          select: {
            CommunityMember: {
              select: {
                profileId: true,
              },
              where: {
                profileId: selfProfileId,
                type: {
                  in: ['ADMIN', 'CONTRIBUTOR'],
                },
              },
            },
          },
        },
      },
    });
    const isAnswerAuthor = answerResult.profileId === selfProfileId;
    const isCommunityAdminOrModerator = answerResult.Community.CommunityMember.length > 0;
    if (!(isAnswerAuthor || isCommunityAdminOrModerator)) {
      throw new AppError('FMANS009');
    }

    const deletedAnswerResult = await prismaPG.$transaction(async (txn) => {
      const [deletedAnswerResult, questionResult] = await Promise.all([
        txn.answer.delete({
          select: { id: true },
          where: { id: answerId },
        }),
        txn.question.findUnique({
          select: { answerCount: true },
          where: {
            id: answerResult.questionId,
          },
        }),
      ]);
      if (questionResult?.answerCount > 0) {
        const _updatedQuestionResult = await txn.question.update({
          data: {
            answerCount: {
              decrement: 1,
            },
          },
          where: {
            id: answerResult.questionId,
          },
          select: { id: true },
        });
      }
      return deletedAnswerResult;
    });

    if (!deletedAnswerResult) {
      throw new AppError('FMANS003');
    }
  },
  fetchMany: async (
    state: FastifyStateI,
    { cursorId: cursorIdP, pageSize, questionId }: ForumAnswerFetchManyI,
  ): Promise<ForumAnswerFetchManyResultI> => {
    const selfProfileId = state.profileId;

    let cursorId: number | null = null;
    if (typeof cursorIdP === 'number' && cursorIdP > 0) {
      cursorId = Number(cursorIdP);
    }
    const questionResult = await QuestionModule.fetchById({ id: questionId }, { communityId: true, profileId: true });

    const communityId = questionResult.communityId;

    const [_communityResult, memberResult] = await Promise.all([
      CommunityModule.fetchById({
        id: communityId,
      }),
      CommunityMemberModule.fetchMember(
        {
          communityId,
          profileId: selfProfileId,
        },
        false,
      ),
    ]);
    const isQuestionAuthorOrAdminModerator =
      questionResult.profileId === selfProfileId ||
      (['ADMIN', 'MODERATOR'] as MemberTypeI[]).includes(memberResult?.type);

    const [answersTotalResult, answersResultTemp] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
            SELECT
              COUNT(1) AS "total"
            FROM
              "forum"."Answer" a
            INNER JOIN "user"."Profile" u
              ON u."id" = a."profileId"
              AND u."status" = 'ACTIVE'
            LEFT JOIN "network"."BlockedProfile" b1
              ON b1."blockerId" = ${selfProfileId}::uuid
              AND b1."blockedId" = u."id"
            LEFT JOIN "network"."BlockedProfile" b2
              ON b2."blockerId" = u."id"
              AND b2."blockedId" = ${selfProfileId}::uuid
            WHERE
              a."questionId" = ${questionId}::uuid
              AND b1."blockerId" IS NULL
              AND b2."blockerId" IS NULL
          `,
      prismaPG.$queryRaw<ForumAnswerWithProfileSQLI[]>`
          SELECT
            a."id",
            a."cursorId",
             a."slug",
            CASE
              WHEN LENGTH(a."text") > 150
              THEN LEFT(a."text", 150) || '...'
              ELSE a."text"
              END AS "text",
              LENGTH(a."text") > 150 AS "isTextTruncated",
            a."upvoteCount",
            a."downvoteCount",
            a."commentCount",
            a."status",
            (
              SELECT v."type" FROM "forum"."AnswerVote" v
              WHERE v."answerId" = a."id"
              AND v."profileId" = ${selfProfileId}::uuid
              LIMIT 1
            ) AS "vote",
            (
              SELECT json_agg(
                json_build_object(
                  'fileUrl', m."fileUrl",
                  'fileExtension', m."fileExtension"
                )
              )
              FROM "forum"."AnswerMedia" m
              WHERE m."answerId" = a."id"
            ) AS "media",
            u."id" AS "profileId",
            u."name" AS "profileName",
            u."avatar" AS "profileAvatar"
          FROM
          "forum"."Answer" a
          INNER JOIN
          "user"."Profile" u
          ON
          a."profileId" = u."id"
          AND u."status" = 'ACTIVE'
          LEFT JOIN
          "network"."BlockedProfile" b1
          ON
          b1."blockerId" = ${selfProfileId}::uuid
          AND
          b1."blockedId" = u."id"
          LEFT JOIN
          "network"."BlockedProfile" b2
          ON
          b2."blockerId" = u."id"
          AND
          b2."blockedId" = ${selfProfileId}::uuid
          WHERE
          a."questionId" = ${questionId}::uuid
          AND ${
            cursorId
              ? Prisma.sql`
          a."cursorId" < ${cursorId}
          AND
          `
              : Prisma.empty
          }
          b1."blockerId" IS NULL
          AND
          b2."blockerId" IS NULL
          ORDER BY
          a."createdAt" DESC
          LIMIT ${pageSize}
        `,
    ]);

    const answersResult: ForumAnswerWithProfileI[] = [];
    let nextCursorId = null;
    if (answersResultTemp?.length) {
      answersResult.push(
        ...answersResultTemp.map(
          (item) =>
            ({
              ...pick(item, [
                'id',
                'upvoteCount',
                'downvoteCount',
                'commentCount',
                'text',
                'isTextTruncated',
                'vote',
                'status',
                'media',
              ]),
              cursorId: Number(item.cursorId),
              canModify: item.profileId === selfProfileId,
              canUpdateStatus: item.profileId === selfProfileId ? false : isQuestionAuthorOrAdminModerator,
              profile: {
                id: item.profileId,
                name: item.profileName,
                avatar: item.profileAvatar,
              },
            }) as ForumAnswerWithProfileI,
        ),
      );
      const lastAnswer = answersResultTemp[answersResultTemp.length - 1];
      nextCursorId =
        answersResult.length > 0 && lastAnswer
          ? Number(lastAnswer.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: answersResult,
      total: Number(answersTotalResult?.[0]?.total || 0),
      nextCursorId,
    };
  },
  updateStatus: async (
    state: FastifyStateI,
    { id: answerId, status: requestedStatus }: AnswerUpdateStatusI,
  ): Promise<void> => {
    const selfProfileId = state.profileId;

    const answer = await prismaPG.answer.findUnique({
      where: { id: answerId },
      select: {
        status: true,
        questionId: true,
        profileId: true,
        text: true,
        createdAt: true,
        Community: {
          select: {
            id: true,
            CommunityMember: {
              select: {
                type: true,
              },
              where: {
                profileId: selfProfileId,
                type: {
                  in: ['ADMIN', 'MODERATOR'],
                },
              },
            },
          },
        },
        Question: {
          select: { profileId: true, title: true, type: true, isLive: true, liveStartedAt: true },
        },
      },
    });
    if (answer.status === requestedStatus) {
      throw new AppError('FMANS007');
    }
    if (selfProfileId === answer.profileId) {
      throw new AppError('FMANS008');
    }

    const question = await QuestionModule.fetchById(
      { id: answer.questionId },
      {
        profileId: true,
        communityId: true,
      },
    );
    const questionId = answer.questionId;

    const isQuestionAuthor = question.profileId === selfProfileId;
    const isCommunityAdminOrModerator = answer.Community.CommunityMember.length > 0;
    if (!(isQuestionAuthor || isCommunityAdminOrModerator)) {
      throw new AppError('FMANS009');
    }

    const [selfProfile] = await prismaPG.$transaction(async (txn) => {
      const [_updatedAnswer, _updatedQuestion, selfProfile] = await Promise.all([
        txn.answer.update({
          data: {
            status: requestedStatus,
          },
          where: { id: answerId },
        }),
        txn.question.update({
          data: {
            isSolved: requestedStatus === 'VERIFIED_SOLUTION',
          },
          where: {
            id: questionId,
          },
        }),
        requestedStatus === 'VERIFIED_SOLUTION'
          ? txn.profile.findUnique({
              where: {
                id: selfProfileId,
              },
              select: {
                name: true,
              },
            })
          : null,
      ]);
      return [selfProfile];
    });

    if (requestedStatus === 'VERIFIED_SOLUTION') {
      const isNormalQuestion = answer.Question.type === 'NORMAL';
      const verifiedRewardId = isNormalQuestion
        ? REWARD_TYPE_IDS.QNA_ANSWER_VERIFIED
        : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER_VERIFIED;

      if (answer.Question.liveStartedAt) {
        const answerTime = answer.createdAt;
        const liveStartTime = answer.Question.liveStartedAt;
        const timeDiffHours = (answerTime.getTime() - liveStartTime.getTime()) / (1000 * 60 * 60);

        let responseTimeRewardId: string | null = null;

        if (timeDiffHours < 2) {
          responseTimeRewardId = isNormalQuestion
            ? REWARD_TYPE_IDS.QNA_ANSWER_LESS_THAN_TWO_HOURS
            : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER_LESS_THAN_TWO_HOURS;
        } else if (timeDiffHours < 4) {
          responseTimeRewardId = isNormalQuestion
            ? REWARD_TYPE_IDS.QNA_ANSWER_TWO_TO_FOUR_HOURS
            : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER_TWO_TO_FOUR_HOURS;
        } else if (timeDiffHours < 6) {
          responseTimeRewardId = isNormalQuestion
            ? REWARD_TYPE_IDS.QNA_ANSWER_FOUR_TO_SIX_HOURS
            : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER_FOUR_TO_SIX_HOURS;
        } else if (timeDiffHours < 8) {
          responseTimeRewardId = isNormalQuestion
            ? REWARD_TYPE_IDS.QNA_ANSWER_SIX_TO_EIGHT_HOURS
            : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER_SIX_TO_EIGHT_HOURS;
        } else if (timeDiffHours < 24) {
          responseTimeRewardId = isNormalQuestion
            ? REWARD_TYPE_IDS.QNA_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS
            : REWARD_TYPE_IDS.TROUBLESHOOT_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS;
        }

        if (responseTimeRewardId) {
          await RewardModule.RewardActionModule.assignReward({
            profileId: answer.profileId,
            rewardId: responseTimeRewardId,
          });
        }
      } else {
        await RewardModule.RewardActionModule.assignReward({
          profileId: answer.profileId,
          rewardId: verifiedRewardId,
        });
      }

      const profileIdsSet = new Set<string>([]);
      if (selfProfileId !== answer.profileId) {
        profileIdsSet.add(answer.profileId);
      }
      if (selfProfileId !== answer.Question.profileId && !profileIdsSet.has(answer.Question.profileId)) {
        profileIdsSet.add(answer.Question.profileId);
      }
      if (profileIdsSet.size) {
        await CommunicationModule.NotificationModule.createOne({
          actorProfileId: selfProfileId,
          actorProfileName: selfProfile?.name ?? 'Unknown',
          answerId,
          questionId: answer.questionId,
          questionTitle: ellipsis(answer.Question.title),
          receiverProfileIds: Array.from(profileIdsSet),
          answerText: answer.text,
          topic: 'communication_topic',
          type: 'FORUM_ANSWER',
        });
      }
    }
  },
  fetchOne: async (
    state: FastifyStateI,
    { id }: Pick<Prisma.AnswerWhereUniqueInput, 'id'>,
    select: Prisma.AnswerSelect = {
      id: true,
      slug: true,
    },
  ): Promise<Answer> => {
    const selfProfileId = state.profileId;
    const answer = await AnswerModule.fetchById({ id }, { ...select, communityId: true });
    await CommunityModule.isPermitted({ communityId: answer.communityId, profileId: selfProfileId });
    return answer;
  },
};
