import { prismaPG } from '@config/db';
import { Prisma, QuestionComment } from '@prisma/postgres';
import type { FastifyStateI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import type {
  QuestionCommentEditOneResultI,
  QuestionCommentFetchManySQLI,
  QuestionCommentFetchManyResultI,
  QuestionCommentFetchManyReplySQLI,
  QuestionCommentFetchManyCoreReplyItemI,
} from '@interfaces/forum/questionComment';
import { errorHandler } from '@utils/errors/handler';
import {
  QuestionCommentCreateOneI,
  QuestionCommentDeleteOneI,
  QuestionCommentEditOneI,
  QuestionCommentFetchManyI,
  QuestionCommentFetchRepliesI,
} from '@schemas/forum/questionComment';
import { QuestionModule } from './question';
import { IdCursorIdI, NullableI, TotalCursorDataI } from '@interfaces/common/data';
import { CommunityModule } from '../community/community';
import { pick } from '@utils/data/object';
import { TotalI } from '@interfaces/common/db';
import { isNullUndefined } from '@utils/data/data';
import CommunicationModule from '@modules/communication';
import { ellipsis } from '@utils/data/string';
import { ProfileIdI } from '@interfaces/user/profile';

export const QuestionCommentModule = {
  fetchById: async (
    filters: Prisma.QuestionCommentWhereUniqueInput,
    select: Prisma.QuestionCommentSelect = {
      id: true,
    },
  ) => {
    const questionCommentResult = await prismaPG.questionComment.findUnique({
      select,
      where: filters,
    });
    if (!questionCommentResult) {
      throw new AppError('FQCM003');
    }
    return questionCommentResult;
  },
  createOne: async (
    state: FastifyStateI,
    { questionId, text, parentCommentId }: QuestionCommentCreateOneI,
  ): Promise<IdCursorIdI> => {
    const { profileId: selfProfileId, entityProfileId: _selfEntityProfileId } = state;
    const questionResult = await QuestionModule.fetchById(
      { id: questionId },
      { communityId: true, isAnonymous: true, profileId: true },
    );
    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const input: Prisma.QuestionCommentUncheckedCreateInput = {
      questionId,
      profileId: selfProfileId,
      text,
    };
    if (selfProfileId === questionResult.profileId && questionResult.isAnonymous) {
      input.isAnonymous = true;
    }
    let parentComment: NullableI<Pick<QuestionComment, 'id' | 'replyCount' | 'profileId'>> = null;
    if (parentCommentId) {
      parentComment = await prismaPG.questionComment.findUnique({
        select: {
          id: true,
          replyCount: true,
          profileId: true,
        },
        where: {
          id: parentCommentId,
          questionId,
        },
      });
      input.parentCommentId = parentCommentId;
      if (!parentComment) {
        throw new AppError('FQCM009');
      }
    }
    const [comment, _questionResult, _updatedParentComment, _reply, selfProfileResult] = await prismaPG.$transaction(
      async (txn) => {
        return Promise.all([
          txn.questionComment.create({
            data: input,
            select: { id: true, cursorId: true },
          }),
          txn.question.update({
            select: {
              commentCount: true,
              title: true,
              entityProfileId: true,
              profileId: true,
            },
            data: {
              commentCount: {
                increment: 1,
              },
            },
            where: {
              id: questionId,
            },
          }),
          parentComment
            ? txn.questionComment.update({
                select: { replyCount: true, id: true },
                data: !isNullUndefined(parentComment?.replyCount)
                  ? {
                      replyCount: {
                        increment: 1,
                      },
                    }
                  : { replyCount: 1 },
                where: { id: parentCommentId },
              })
            : null,
          parentCommentId
            ? txn.questionComment.update({
                data: { replyCount: !isNullUndefined(parentComment?.replyCount) ? { increment: 1 } : 1 },
                where: { id: parentCommentId },
              })
            : null,
          txn.profile.findUnique({
            where: {
              id: selfProfileId,
            },
            select: {
              name: true,
            },
          }),
        ]);
      },
    );
    if (!comment) {
      throw new AppError('FQCM002');
    }

    const profileIdsSet = new Set<string>([]);
    if (parentCommentId) {
      const profilesResult = await prismaPG.$queryRaw<ProfileIdI[]>`
      SELECT
        DISTINCT u."id"
      FROM "user"."Profile" u
      INNER JOIN "forum"."QuestionComment" c
        ON c."questionId" = ${questionId}
        AND c."parentCommentId" = ${parentCommentId}
        AND c."profileId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}
      WHERE u."id" != ${selfProfileId}
    `;
      if (profilesResult?.length) {
        profilesResult.forEach((profile) => profileIdsSet.add(profile.id));
      }
      if (selfProfileId !== parentComment.profileId && !profileIdsSet.has(parentComment.profileId)) {
        profileIdsSet.add(parentComment.profileId);
      }
    }
    if (selfProfileId !== questionResult?.profileId && !profileIdsSet.has(questionResult.profileId)) {
      profileIdsSet.add(questionResult.profileId);
    }

    if (profileIdsSet.size) {
      await CommunicationModule.NotificationModule.createOne({
        actorProfileId: selfProfileId,
        actorProfileName: selfProfileResult?.name ?? 'Unknown',
        receiverProfileIds: Array.from(profileIdsSet),
        questionId,
        questionTitle: ellipsis(_questionResult.title),
        commentId: comment.id,
        ...(parentCommentId ? { parentCommentId } : {}),
        topic: 'communication_topic',
        type: parentCommentId ? 'FORUM_QUESTION_REPLY' : 'FORUM_QUESTION_COMMENT',
      });
      return {
        id: comment.id,
        cursorId: Number(comment.cursorId),
      };
    }
  },
  fetchMany: async (
    state: FastifyStateI,
    { questionId, cursorId, pageSize }: QuestionCommentFetchManyI,
  ): Promise<TotalCursorDataI<QuestionCommentFetchManyResultI>> => {
    const selfProfileId = state.profileId;
    const questionResult = await QuestionModule.fetchById(
      { id: questionId },
      { communityId: true, isAnonymous: true, profileId: true },
    );
    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }

    const [commentsTemp, commentsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<QuestionCommentFetchManySQLI[]>`
      SELECT
        c."id",
        c."cursorId",
        c."text",
        c."isAnonymous",
        c."replyCount",
        c."createdAt",
        c."profileId",
        u."name" AS "profileName",
        u."avatar" AS "profileAvatar",
        (
          SELECT json_agg(reply_data)
          FROM (
            SELECT
              r."id",
              r."cursorId",
              r."text",
              r."isAnonymous",
              r."replyCount",
              r."createdAt",
              r."profileId",
              ru."name" AS "profileName",
              ru."avatar" AS "profileAvatar"
            FROM "forum"."QuestionComment" r
            INNER JOIN "user"."Profile" ru ON ru."id" = r."profileId"
            LEFT JOIN "network"."BlockedProfile" br1
              ON br1."blockerId" = ${selfProfileId}::uuid
              AND br1."blockedId" = ru."id"
            LEFT JOIN "network"."BlockedProfile" br2
              ON br2."blockerId" = ru."id"
              AND br2."blockedId" = ${selfProfileId}::uuid
            WHERE
              r."parentCommentId" = c."id"
              AND br1."blockerId" IS NULL
              AND br2."blockerId" IS NULL
            ORDER BY r."createdAt" DESC
            LIMIT 2
          ) AS reply_data
        ) AS replies
      FROM
        "forum"."QuestionComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."questionId" = ${questionId}::uuid
        AND c."parentCommentId" IS NULL
        ${cursorId ? Prisma.sql` AND c."cursorId" < ${cursorId}` : Prisma.empty}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      ORDER BY c."createdAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1) AS "total"
      FROM
        "forum"."QuestionComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."questionId" = ${questionId}::uuid
        AND c."parentCommentId" IS NULL
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
    `,
    ]);

    const commentsResult: QuestionCommentFetchManyResultI[] = [];
    let nextCursorId = null;
    if (commentsTemp?.length) {
      commentsResult.push(
        ...commentsTemp.map(
          (item) =>
            ({
              ...pick(item, ['id', 'text', 'isAnonymous', 'replyCount', 'createdAt']),
              cursorId: Number(item.cursorId),
              canDelete: item.profileId === selfProfileId,
              profile: !item.isAnonymous
                ? {
                    id: item.profileId,
                    name: item.profileName,
                    avatar: item.profileAvatar,
                  }
                : null,
              replies:
                item?.replies?.map(
                  (reply) =>
                    ({
                      id: reply.id,
                      text: reply.text,
                      canDelete: reply.profileId === selfProfileId,
                      cursorId: reply.cursorId,
                      createdAt: reply.createdAt,
                      isAnonymous: reply.isAnonymous,
                      profile: !reply.isAnonymous
                        ? {
                            id: reply.profileId,
                            name: reply.profileName,
                            avatar: reply.profileAvatar,
                          }
                        : null,
                    }) as QuestionCommentFetchManyCoreReplyItemI,
                ) || null,
            }) as QuestionCommentFetchManyResultI,
        ),
      );
      const lastComment = commentsTemp[commentsTemp.length - 1];
      nextCursorId =
        commentsResult.length > 0 && lastComment
          ? Number(lastComment.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: commentsResult,
      total: Number(commentsTotalResult?.[0]?.total || 0),
      nextCursorId,
    };
  },
  fetchReplies: async (
    state: FastifyStateI,
    { questionId, parentCommentId, cursorId, pageSize }: QuestionCommentFetchRepliesI,
  ): Promise<TotalCursorDataI<QuestionCommentFetchManyCoreReplyItemI>> => {
    const selfProfileId = state.profileId;

    const [questionResult, _parentCommentResult] = await Promise.all([
      QuestionModule.fetchById({ id: questionId }, { communityId: true, isAnonymous: true, profileId: true }),
      parentCommentId ? QuestionCommentModule.fetchById({ id: parentCommentId }) : null,
    ]);

    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }

    const [commentsTemp, commentsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<QuestionCommentFetchManyReplySQLI[]>`
      SELECT
        c."id",
        c."cursorId",
        c."text",
        c."isAnonymous",
        c."createdAt",
        c."profileId",
        u."name" AS "profileName",
        u."avatar" AS "profileAvatar"
      FROM
        "forum"."QuestionComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      WHERE
        c."parentCommentId" = ${parentCommentId}::uuid
        ${cursorId ? Prisma.sql` AND c."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY c."createdAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1) AS "total"
      FROM
        "forum"."QuestionComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."parentCommentId" = ${parentCommentId}::uuid
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
    `,
    ]);
    const commentsResult: QuestionCommentFetchManyCoreReplyItemI[] = [];
    let nextCursorId = null;
    if (commentsTemp?.length) {
      commentsResult.push(
        ...commentsTemp.map(
          (item) =>
            ({
              id: item.id,
              text: item.text,
              isAnonymous: item.isAnonymous,
              createdAt: item.createdAt,
              canDelete: item.profileId === selfProfileId,
              cursorId: Number(item.cursorId),
              profile: !item.isAnonymous
                ? {
                    id: item.profileId,
                    name: item.profileName,
                    avatar: item.profileAvatar,
                  }
                : null,
            }) as QuestionCommentFetchManyCoreReplyItemI,
        ),
      );
      const lastComment = commentsTemp[commentsTemp.length - 1];
      nextCursorId =
        commentsResult.length > 0 && lastComment
          ? Number(lastComment.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: commentsResult,
      total: Number(commentsTotalResult?.[0]?.total || 0),
      nextCursorId,
    };
  },
  editOne: async (
    state: FastifyStateI,
    { commentId, newComment }: QuestionCommentEditOneI,
  ): Promise<QuestionCommentEditOneResultI> => {
    try {
      const profileId = state.profileId;

      const comment = await prismaPG.questionComment.findUnique({
        where: { id: commentId },
        select: {
          profileId: true,
          text: true,
          parentCommentId: true,
        },
      });
      if (!comment) {
        throw new AppError('FQCM003');
      }

      if (comment.profileId !== profileId) {
        throw new AppError('FQCM004');
      }

      if (comment.text === newComment) {
        throw new AppError('FQCM006');
      }

      const updatedComment = await prismaPG.questionComment.update({
        where: { id: commentId },
        data: {
          text: newComment,
          updatedAt: new Date(),
        },
      });

      if (!updatedComment) {
        throw new AppError('FQCM007');
      }

      return {
        ...updatedComment,
        cursorId: String(updatedComment.cursorId),
      };
    } catch (error) {
      errorHandler(error);
      throw new AppError('DB004');
    }
  },
  deleteOne: async (state: FastifyStateI, { id: commentId }: QuestionCommentDeleteOneI): Promise<void> => {
    try {
      const selfProfileId = state.profileId;
      const commentResult = await prismaPG.questionComment.findUnique({
        where: { id: commentId },
        select: {
          profileId: true,
          parentCommentId: true,
          replyCount: true,
          Question: {
            select: {
              id: true,
              commentCount: true,
              Community: {
                select: {
                  CommunityMember: {
                    select: { type: true },
                    where: {
                      profileId: selfProfileId,
                      type: {
                        in: ['ADMIN', 'MODERATOR'],
                      },
                    },
                  },
                },
              },
            },
          },
          Parent: {
            select: { replyCount: true },
          },
        },
      });

      if (!commentResult) {
        throw new AppError('FQCM003');
      }

      const isCommentAuthor = commentResult.profileId === selfProfileId;

      const isCommunityAdminOrModerator = commentResult.Question.Community.CommunityMember.length > 0;

      if (!(isCommentAuthor || isCommunityAdminOrModerator)) {
        throw new AppError('FQCM007');
      }
      const [deletedCommentResult, _updatedQuestionResult, _updatedParentCommentResult] = await prismaPG.$transaction(
        async (txn) =>
          await Promise.all([
            txn.questionComment.delete({
              select: { id: true },
              where: { id: commentId },
            }),
            commentResult.Question?.commentCount > 0
              ? txn.question.update({
                  data: {
                    commentCount: { decrement: 1 },
                  },
                  select: { commentCount: true },
                  where: {
                    id: commentResult.Question.id,
                  },
                })
              : null,
            commentResult?.parentCommentId && commentResult?.Parent?.replyCount > 0
              ? txn.questionComment.update({
                  data: {
                    replyCount: { decrement: 1 },
                  },
                  where: {
                    id: commentResult.parentCommentId,
                  },
                })
              : null,
          ]),
      );
      if (!deletedCommentResult) {
        throw new AppError('FQCM008');
      }
    } catch (error) {
      errorHandler(error);
    }
  },
};
