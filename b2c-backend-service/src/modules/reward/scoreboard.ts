import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { ScoreboardFetchForClientResultI, RewardProfileI } from '@interfaces/reward/scoreboard';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';

type MaxScoresI = {
  maxQnaAnswerScore: number | null;
  maxTroubleshootAnswerScore: number | null;
  maxContributionScore: number | null;
};

export const ScoreboardModule = {
  fetch: async (request: FastifyRequestI): Promise<ScoreboardFetchForClientResultI> => {
    const profileId = request.profileId;

    if (!profileId) {
      throw new AppError('AUTH019');
    }

    // Get user's reward profile
    const userResult = await prismaPG.$queryRaw<RewardProfileI[]>(
      Prisma.sql`
        SELECT * FROM "score"."RewardProfile"
        WHERE "profileId" = ${profileId}::uuid
      `,
    );

    if (!userResult || userResult.length === 0) {
      const defaultReward = {
        contributionPerformanceIndex: 0,
        contributionScore: 0,
        createdAt: new Date('2025-08-01'),
        profileId: profileId,
        qnaAnswerScore: 0,
        qnaPerformanceIndex: 0,
        totalScore: 0,
        troubleshootAnswerScore: 0,
        troubleshootPerformanceIndex: 0,
        updatedAt: new Date('2025-08-01'),
      };
      return defaultReward;
    }

    const userProfile = userResult[0];

    // Get highest scores for each category from materialized views (Overall)
    type SingleScoreRow = { score: number };

    const [qnaTop] = await prismaPG.$queryRaw<SingleScoreRow[]>(
      Prisma.sql`
        SELECT "score" FROM "leaderboard"."QnAAnswerOverallLeaderboard"
        ORDER BY "score" DESC
        LIMIT 1
      `,
    );

    const [troubleshootTop] = await prismaPG.$queryRaw<SingleScoreRow[]>(
      Prisma.sql`
        SELECT "score" FROM "leaderboard"."TroubleshootOverallLeaderboard"
        ORDER BY "score" DESC
        LIMIT 1
      `,
    );

    const [contributionTop] = await prismaPG.$queryRaw<SingleScoreRow[]>(
      Prisma.sql`
        SELECT "score" FROM "leaderboard"."ContributionOverallLeaderboard"
        ORDER BY "score" DESC
        LIMIT 1
      `,
    );

    const maxScores: MaxScoresI = {
      maxQnaAnswerScore: qnaTop?.score ?? 0,
      maxTroubleshootAnswerScore: troubleshootTop?.score ?? 0,
      maxContributionScore: contributionTop?.score ?? 0,
    };

    // Calculate performance indices: (user score / highest score) * 100
    const calculatePerformanceIndex = (userScore: number, maxScore: number | null): number => {
      if (!maxScore || maxScore === 0) return 0;
      return Math.round((userScore / maxScore) * 100);
    };

    // Add performance indices to the result
    const result = {
      ...userProfile,
      qnaPerformanceIndex: calculatePerformanceIndex(userProfile.qnaAnswerScore, maxScores.maxQnaAnswerScore),
      troubleshootPerformanceIndex: calculatePerformanceIndex(
        userProfile.troubleshootAnswerScore,
        maxScores.maxTroubleshootAnswerScore,
      ),
      contributionPerformanceIndex: calculatePerformanceIndex(
        userProfile.contributionScore,
        maxScores.maxContributionScore,
      ),
    };

    return result;
  },
};
