import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameTypeI, NullableI, TotalDataI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  EquipmentManufacturerNestedClientI,
  EquipmentManufacturerTransformParamsI,
} from '@interfaces/ship/equipmentManufacturer';
import { Prisma, EquipmentManufacturer } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type {
  EquipmentManufacturerFetchForClientI,
  EquipmentManufacturerFetchsertI,
} from '@schemas/ship/equipmentManufacturer';

export const EquipmentManufacturerModule = {
  isIdsExists: async (idTypes: IdTypeI[]): Promise<boolean> => {
    const { masterIds, rawDataIds } = idTypes.reduce(
      (acc, curr) => {
        if (curr.dataType === 'master') {
          acc.masterIds.push(curr.id);
        } else if (curr.dataType === 'raw') {
          acc.rawDataIds.push(curr.id);
        }
        return acc;
      },
      {
        masterIds: [],
        rawDataIds: [],
      } as {
        masterIds: string[];
        rawDataIds: string[];
      },
    );
    if (!masterIds?.length && !rawDataIds?.length) {
      return false;
    }
    const [equipmentManufacturer, equipmentManufacturerRawData] = await Promise.all([
      masterIds?.length
        ? prismaPG.equipmentManufacturer.findMany({
            select: { id: true },
            where: {
              id: {
                in: masterIds,
              },
            },
          })
        : null,
      rawDataIds?.length
        ? prismaPG.equipmentManufacturerRawData.findMany({
            select: { id: true },
            where: {
              id: {
                in: rawDataIds,
              },
            },
          })
        : null,
    ]);

    return (
      (!masterIds?.length || (masterIds?.length && masterIds.length === equipmentManufacturer.length)) &&
      (!rawDataIds?.length || (rawDataIds?.length && rawDataIds.length === equipmentManufacturerRawData.length))
    );
  },
  fetchById: async (
    { id, dataType }: IdTypeI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EquipmentManufacturerNestedClientI> => {
    const equipmentManufacturerResultTemp = await txn.$queryRaw<Pick<EquipmentManufacturer, 'id' | 'name'>>`
    ${
      dataType === 'master'
        ? Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "ship"."EquipmentManufacturer" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
        : Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "rawData"."EquipmentManufacturerRawData" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
    }
    `;
    if (!equipmentManufacturerResultTemp) {
      throw new AppError('EQMNF001');
    }
    return {
      ...equipmentManufacturerResultTemp,
      dataType,
    } as EquipmentManufacturerNestedClientI;
  },
  fetchForClient: async (
    filtersP: EquipmentManufacturerFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<TotalDataI<EquipmentManufacturerNestedClientI>> => {
    filtersP.search = filtersP.search?.trim()?.toLowerCase();

    const category = filtersP.equipmentCategory;

    const masterFilters: Prisma.Sql[] = [];
    const rawFilters: Prisma.Sql[] = [];

    if (filtersP.search) {
      masterFilters.push(Prisma.sql`e."name" ILIKE ${'%' + filtersP.search + '%'}`);
      rawFilters.push(Prisma.sql`erw."name" ILIKE ${'%' + filtersP.search + '%'}`);
    }

    if (category) {
      if (category.dataType === 'master') {
        masterFilters.push(Prisma.sql`EXISTS (
          SELECT 1 FROM "forum"."Question" q
          WHERE q."equipmentManufacturerId" = e."id"
          AND q."equipmentCategoryId" = ${category.id}::uuid
        )`);
        rawFilters.push(Prisma.sql`EXISTS (
          SELECT 1 FROM "forum"."Question" q
          WHERE q."equipmentManufacturerRawDataId" = erw."id"
          AND q."equipmentCategoryId" = ${category.id}::uuid
        )`);
      } else if (category.dataType === 'raw') {
        masterFilters.push(Prisma.sql`EXISTS (
          SELECT 1 FROM "forum"."Question" q
          WHERE q."equipmentManufacturerId" = e."id"
          AND q."equipmentCategoryRawDataId" = ${category.id}::uuid
        )`);
        rawFilters.push(Prisma.sql`EXISTS (
          SELECT 1 FROM "forum"."Question" q
          WHERE q."equipmentManufacturerRawDataId" = erw."id"
          AND q."equipmentCategoryRawDataId" = ${category.id}::uuid
        )`);
      }
    }

    const masterWhere = masterFilters.length ? Prisma.sql`WHERE ${Prisma.join(masterFilters, ' AND ')}` : Prisma.empty;
    const rawWhere = rawFilters.length ? Prisma.sql`WHERE ${Prisma.join(rawFilters, ' AND ')}` : Prisma.empty;

    const [equipmentManufacturersResult, equipmentManufacturersTotalResult] = await Promise.all([
      prismaPG.$queryRaw<EquipmentManufacturerNestedClientI[]>`
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentManufacturer" e
      ${masterWhere}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentManufacturerRawData" erw
      ${rawWhere}

      ORDER BY
        "dataType" ASC,
        "name" ASC
      LIMIT ${pagination.pageSize}
      OFFSET ${pagination.page * pagination.pageSize}
    `,
      prismaPG.$queryRaw<{ total: number }[]>`
      SELECT (
        (SELECT COUNT(*) FROM "ship"."EquipmentManufacturer" e
         ${masterWhere}) +
        (SELECT COUNT(*) FROM "rawData"."EquipmentManufacturerRawData" erw
         ${rawWhere})
      )::INTEGER AS total
    `,
    ]);
    return {
      data: equipmentManufacturersResult,
      total: Number(equipmentManufacturersTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async ({ name }: EquipmentManufacturerFetchsertI): Promise<EquipmentManufacturerNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const results = await prismaPG.$queryRaw<EquipmentManufacturerNestedClientI[]>`
    SELECT * FROM (
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentManufacturer" e
      WHERE
        e."name" = ${name}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentManufacturerRawData" erw
      WHERE
        erw."name" = ${name}
    ) AS combinedResult
    ORDER BY
      combinedResult."dataType" ASC,
      combinedResult."name" ASC
    LIMIT 1
  `;

    if (results && results.length > 0) {
      return results[0];
    }

    const equipmentManufacturerResultTemp = await prismaPG.equipmentManufacturerRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!equipmentManufacturerResultTemp) {
      throw new AppError('EQMNF002', 'Failed to create equipment category');
    }

    return {
      ...equipmentManufacturerResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as EquipmentManufacturerNestedClientI;
  },
  transform: ({
    equipmentManufacturerId,
    equipmentManufacturerName,
    equipmentManufacturerRawDataId,
    equipmentManufacturerRawDataName,
  }: EquipmentManufacturerTransformParamsI): NullableI<IdNameTypeI> =>
    equipmentManufacturerId
      ? { id: equipmentManufacturerId, name: equipmentManufacturerName, type: 'master' }
      : equipmentManufacturerRawDataId
        ? { id: equipmentManufacturerRawDataId, name: equipmentManufacturerRawDataName, type: 'raw' }
        : null,
};
