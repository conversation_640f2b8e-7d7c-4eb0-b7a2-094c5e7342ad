import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameTypeI, NullableI, TotalDataI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import { EquipmentModelNestedClientI, EquipmentModelTransformParamsI } from '@interfaces/ship/equipmentModel';
import { Prisma, EquipmentModel } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { EquipmentModelFetchForClientI, EquipmentModelFetchsertI } from '@schemas/ship/equipmentModel';

export const EquipmentModelModule = {
  isIdsExists: async (idTypes: IdTypeI[]): Promise<boolean> => {
    const { masterIds, rawDataIds } = idTypes.reduce(
      (acc, curr) => {
        if (curr.dataType === 'master') {
          acc.masterIds.push(curr.id);
        } else if (curr.dataType === 'raw') {
          acc.rawDataIds.push(curr.id);
        }
        return acc;
      },
      {
        masterIds: [],
        rawDataIds: [],
      } as {
        masterIds: string[];
        rawDataIds: string[];
      },
    );
    if (!masterIds?.length && !rawDataIds?.length) {
      return false;
    }
    const [equipmentModel, equipmentModelRawData] = await Promise.all([
      masterIds?.length
        ? prismaPG.equipmentModel.findMany({
            select: { id: true },
            where: {
              id: {
                in: masterIds,
              },
            },
          })
        : null,
      rawDataIds?.length
        ? prismaPG.equipmentModelRawData.findMany({
            select: { id: true },
            where: {
              id: {
                in: rawDataIds,
              },
            },
          })
        : null,
    ]);

    return (
      (!masterIds?.length || (masterIds?.length && masterIds.length === equipmentModel.length)) &&
      (!rawDataIds?.length || (rawDataIds?.length && rawDataIds.length === equipmentModelRawData.length))
    );
  },
  fetchById: async ({ id, dataType }: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<EquipmentModelNestedClientI> => {
    const equipmentModelResultTemp = await txn.$queryRaw<Pick<EquipmentModel, 'id' | 'name'>>`
    ${
      dataType === 'master'
        ? Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "ship"."EquipmentModel" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
        : Prisma.sql`
          SELECT
            e."id",
            e."name"
          FROM
            "rawData"."EquipmentModelRawData" e
          WHERE
            e."id" = ${id}::uuid
          LIMIT 1
        `
    }
    `;
    if (!equipmentModelResultTemp) {
      throw new AppError('EQMDL001');
    }
    return {
      ...equipmentModelResultTemp,
      dataType,
    } as EquipmentModelNestedClientI;
  },

  fetchForClient: async (
    filtersP: EquipmentModelFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<TotalDataI<EquipmentModelNestedClientI>> => {
    filtersP.search = filtersP.search?.trim()?.toLowerCase();

    const category = filtersP.equipmentCategory;
    const manufacturer = filtersP.equipmentManufacturer;

    const masterJoins: Prisma.Sql[] = [];
    const rawJoins: Prisma.Sql[] = [];

    if (category || manufacturer) {
      masterJoins.push(Prisma.sql`LEFT JOIN "forum"."Question" q ON q."equipmentModelId" = e."id"`);
      rawJoins.push(Prisma.sql`LEFT JOIN "forum"."Question" q ON q."equipmentModelRawDataId" = erw."id"`);
    }

    const masterFilters: Prisma.Sql[] = [];
    const rawFilters: Prisma.Sql[] = [];

    if (filtersP.search) {
      masterFilters.push(Prisma.sql`e."name" ILIKE ${'%' + filtersP.search + '%'}`);
      rawFilters.push(Prisma.sql`erw."name" ILIKE ${'%' + filtersP.search + '%'}`);
    }

    if (category) {
      if (category.dataType === 'master') {
        masterFilters.push(Prisma.sql`q."equipmentCategoryId" = ${category.id}::uuid`);
        rawFilters.push(Prisma.sql`q."equipmentCategoryId" = ${category.id}::uuid`);
      } else if (category.dataType === 'raw') {
        masterFilters.push(Prisma.sql`q."equipmentCategoryRawDataId" = ${category.id}::uuid`);
        rawFilters.push(Prisma.sql`q."equipmentCategoryRawDataId" = ${category.id}::uuid`);
      }
    }

    if (manufacturer) {
      if (manufacturer.dataType === 'master') {
        masterFilters.push(Prisma.sql`q."equipmentManufacturerId" = ${manufacturer.id}::uuid`);
        rawFilters.push(Prisma.sql`q."equipmentManufacturerId" = ${manufacturer.id}::uuid`);
      } else if (manufacturer.dataType === 'raw') {
        masterFilters.push(Prisma.sql`q."equipmentManufacturerRawDataId" = ${manufacturer.id}::uuid`);
        rawFilters.push(Prisma.sql`q."equipmentManufacturerRawDataId" = ${manufacturer.id}::uuid`);
      }
    }

    const masterJoin = masterJoins.length ? Prisma.join(masterJoins, '\n') : Prisma.empty;
    const rawJoin = rawJoins.length ? Prisma.join(rawJoins, '\n') : Prisma.empty;

    const masterWhere = masterFilters.length ? Prisma.sql`WHERE ${Prisma.join(masterFilters, ' AND ')}` : Prisma.empty;
    const rawWhere = rawFilters.length ? Prisma.sql`WHERE ${Prisma.join(rawFilters, ' AND ')}` : Prisma.empty;

    const [equipmentModelsResult, equipmentModelsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<EquipmentModelNestedClientI[]>`
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentModel" e
      ${masterJoin}
      ${masterWhere}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentModelRawData" erw
      ${rawJoin}
      ${rawWhere}

      ORDER BY
        "dataType" ASC,
        "name" ASC
      LIMIT ${pagination.pageSize}
      OFFSET ${pagination.page * pagination.pageSize}
    `,
      prismaPG.$queryRaw<{ total: number }[]>`
      SELECT (
        (SELECT COUNT(*) FROM "ship"."EquipmentModel" e
         ${masterJoin}
         ${masterWhere}) +
        (SELECT COUNT(*) FROM "rawData"."EquipmentModelRawData" erw
         ${rawJoin}
         ${rawWhere})
      )::INTEGER AS total
    `,
    ]);
    return {
      data: equipmentModelsResult,
      total: Number(equipmentModelsTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async ({ name }: EquipmentModelFetchsertI): Promise<EquipmentModelNestedClientI> => {
    name = name?.toLowerCase()?.trim();
    const results = await prismaPG.$queryRaw<EquipmentModelNestedClientI[]>`
    SELECT * FROM (
      SELECT
        e."id",
        e."name",
        'master' AS "dataType"
      FROM
        "ship"."EquipmentModel" e
      WHERE
        e."name" = ${name}

      UNION

      SELECT
        erw."id",
        erw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."EquipmentModelRawData" erw
      WHERE
        erw."name" = ${name}
    ) AS combinedResult
    ORDER BY
      combinedResult."dataType" ASC,
      combinedResult."name" ASC
    LIMIT 1
  `;

    if (results && results.length > 0) {
      return results[0];
    }

    const equipmentModelResultTemp = await prismaPG.equipmentModelRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!equipmentModelResultTemp) {
      throw new AppError('EQMDL002', 'Failed to create equipment category');
    }

    return {
      ...equipmentModelResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as EquipmentModelNestedClientI;
  },
  transform: ({
    equipmentModelId,
    equipmentModelName,
    equipmentModelRawDataId,
    equipmentModelRawDataName,
  }: EquipmentModelTransformParamsI): NullableI<IdNameTypeI> =>
    equipmentModelId
      ? { id: equipmentModelId, name: equipmentModelName, type: 'master' }
      : equipmentModelRawDataId
        ? { id: equipmentModelRawDataId, name: equipmentModelRawDataName, type: 'raw' }
        : null,
};
