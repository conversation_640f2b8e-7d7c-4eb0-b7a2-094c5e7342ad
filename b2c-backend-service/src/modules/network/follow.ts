import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { NotificationProfileTypeI } from '@consts/communication/notification';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { EntityProfileExternalI } from '@interfaces/company/entityProfile';
import type { FollowDataI, FollowExternalI } from '@interfaces/network/follow';
import type { ProfileNetworkExternalI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';
import { Prisma, Follow } from '@prisma/postgres';
import type { ProfileIdCursorPaginationI } from '@schemas/common/common';
import type { FollowOneParamsI } from '@schemas/network/follow';
import { errorHandler } from '@utils/errors/handler';

export const FollowModule = {
  followOne: async (
    state: FastifyStateI,
    { followeeProfileId }: FollowOneParamsI,
  ): Promise<Omit<Follow, 'cursorId'> & { cursorId: number }> => {
    const { profileId: selfProfileId, profileType, entityProfileId: selfEntityProfileId } = state;
    const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
      select: { blockedId: true },
      where: {
        Blocked: {
          status: 'ACTIVE',
        },
        OR: [
          {
            blockerId: followeeProfileId,
            blockedId: profileType === 'ENTITY' ? selfEntityProfileId : selfProfileId,
          },
          {
            blockerId: profileType === 'ENTITY' ? selfEntityProfileId : selfProfileId,
            blockedId: followeeProfileId,
          },
        ],
      },
    });
    if (blockedProfileResult) {
      throw new AppError('PFL001');
    }
    const followeeEntityProfileResult = await prismaPG.entityProfile.findUnique({
      where: {
        id: followeeProfileId,
      },
      select: {
        id: true,
      },
    });
    const isReceiverEntityFollowee: boolean = Boolean(followeeEntityProfileResult?.id);

    const followFilter: Prisma.FollowWhereUniqueInput = (
      profileType === 'ENTITY'
        ? isReceiverEntityFollowee
          ? {
              followeeEntityProfileId: followeeProfileId,
              followerEntityProfileId: selfEntityProfileId,
              followeeProfileId: null,
              followerProfileId: null,
            }
          : {
              followeeProfileId: followeeProfileId,
              followerEntityProfileId: selfEntityProfileId,
              followeeEntityProfileId: null,
              followerProfileId: null,
            }
        : isReceiverEntityFollowee
          ? {
              followeeEntityProfileId: followeeProfileId,
              followerProfileId: selfProfileId,
              followeeProfileId: null,
              followerEntityProfileId: null,
            }
          : {
              followeeProfileId: followeeProfileId,
              followerProfileId: selfProfileId,
              followeeEntityProfileId: null,
              followerEntityProfileId: null,
            }
    ) as Prisma.FollowWhereUniqueInput;

    const existingFollowResult = await prismaPG.follow.findFirst({
      where: followFilter,
    });

    if (existingFollowResult) {
      throw new AppError('FLW005');
    }

    const followCreateInput: Prisma.FollowUncheckedCreateInput =
      profileType === 'ENTITY'
        ? { followerEntityProfileId: selfEntityProfileId, followeeProfileId }
        : { followerProfileId: selfProfileId, followeeProfileId };

    const [followResult, _followeeResult, _followerProfileResult] = await prismaPG.$transaction([
      prismaPG.follow.create({
        data: followCreateInput,
      }),
      prismaPG.profile.update({
        data: {
          followersCount: {
            increment: 1,
          },
        },
        select: {
          id: true,
          followersCount: true,
        },
        where: {
          id: followeeProfileId,
        },
      }),
      profileType === 'ENTITY'
        ? prismaPG.entityProfile.update({
            where: {
              id: selfEntityProfileId,
            },
            data: {
              followingsCount: { increment: 1 },
            },
            select: {
              id: true,
              followersCount: true,
            },
          })
        : prismaPG.profile.update({
            data: {
              followingsCount: {
                increment: 1,
              },
            },
            select: {
              id: true,
              followersCount: true,
            },
            where: {
              id: state.profileId,
            },
          }),
    ]);
    if (!followResult) {
      throw new AppError('FLW002');
    }
    const receiverProfileType: NotificationProfileTypeI = isReceiverEntityFollowee ? 'ENTITY' : 'USER';
    await CommunicationModule.NotificationModule.createOne({
      actorProfileId: selfProfileId,
      actorProfileType: profileType === 'ENTITY' ? 'ENTITY' : 'USER',
      ...(isReceiverEntityFollowee
        ? {
            receiverEntityProfileId: followeeProfileId,
          }
        : { profileId: followeeProfileId }),
      receiverProfileType,
      topic: 'communication_topic',
      type: 'FOLLOWER',
    });

    return {
      ...followResult,
      cursorId: Number(followResult.cursorId),
    };
  },
  unfollowOne: async (state: FastifyStateI, { followeeProfileId }: FollowOneParamsI): Promise<void> => {
    try {
      const { profileId: selfProfileId, profileType, entityProfileId: selfEntityProfileId } = state;
      const isEntityProfile = profileType === 'ENTITY';

      const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
        select: { blockedId: true },
        where: {
          Blocked: {
            status: 'ACTIVE',
          },
          OR: [
            {
              blockerId: followeeProfileId,
              blockedId: isEntityProfile ? selfEntityProfileId : selfProfileId,
            },
            {
              blockerId: isEntityProfile ? selfEntityProfileId : selfProfileId,
              blockedId: followeeProfileId,
            },
          ],
        },
      });
      if (blockedProfileResult) {
        throw new AppError('PFL001');
      }

      const followDeletedata = isEntityProfile
        ? { followerEntityProfileId: selfEntityProfileId, followeeProfileId }
        : { followerProfileId: selfProfileId, followeeProfileId };

      const [unfollowResult, _followeeProfileResult, _followerProfileResult] = await prismaPG.$transaction([
        prismaPG.follow.deleteMany({
          where: followDeletedata,
        }),
        prismaPG.profile.update({
          data: {
            followersCount: {
              decrement: 1,
            },
          },
          select: {
            id: true,
          },
          where: {
            id: followeeProfileId,
          },
        }),
        isEntityProfile
          ? prismaPG.entityProfile.update({
              data: {
                followingsCount: {
                  decrement: 1,
                },
              },
              select: {
                id: true,
              },
              where: {
                id: selfEntityProfileId,
              },
            })
          : prismaPG.profile.update({
              data: {
                followingsCount: {
                  decrement: 1,
                },
              },
              select: {
                id: true,
              },
              where: {
                id: selfProfileId,
              },
            }),
      ]);
      if (!unfollowResult) {
        throw new AppError('PFL001');
      }
    } catch (error) {
      errorHandler(error, {
        RECORD_NOT_FOUND: 'FLW006',
      });
    }
  },
  followStatus: async (
    state: FastifyStateI,
    { followeeProfileId }: FollowOneParamsI,
  ): Promise<{ isFollowing: boolean }> => {
    const selfProfileId = state.profileId;
    const existingFollowResult = await prismaPG.follow.count({
      where: {
        followerProfileId: selfProfileId,
        followeeProfileId,
      },
    });
    return { isFollowing: Boolean(existingFollowResult) };
  },
  fetchManyFollowers: async (
    state: FastifyStateI,
    { cursorId, pageSize, profileId }: ProfileIdCursorPaginationI,
  ): Promise<{ data: FollowExternalI[]; total: number }> => {
    try {
      const { profileId: selfProfileId, profileType, entityProfileId: selfEntityProfileId } = state;
      const isSelfEntityProfile = profileType === 'ENTITY';

      const followers: FollowExternalI[] = [];

      const [followersResultTemp, totalFollowers] = await Promise.all([
        prismaPG.$queryRaw<FollowDataI[]>`
          SELECT
            f."cursorId" AS "cursorId",

            CASE WHEN u."id" IS NOT NULL THEN
              json_build_object(
                'id', u."id",
                'name', u."name",
                'avatar', u."avatar",
                'designationText', u."designationText",
                'designationAlternativeId', u."designationAlternativeId",
                'designationRawDataId', u."designationRawDataId",
                'entityText', u."entityText",
                'entityId', u."entityId",
                'entityRawDataId', u."entityRawDataId"
                ${
                  !isSelfEntityProfile && selfProfileId !== profileId
                    ? Prisma.sql`,
                  'isConnected', EXISTS (
                    SELECT 1 FROM "network"."Connection" c
                    WHERE c."profileId" = ${selfProfileId}::uuid
                      AND c."connectedId" = u."id"
                  ),
                  'isFollowing', EXISTS (
                    SELECT 1 FROM "network"."Follow" f2
                    WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                      AND f2."followeeProfileId" = u."id"
                  )`
                    : Prisma.empty
                }
              )
            END AS "Profile",

            CASE WHEN ep."id" IS NOT NULL THEN
              json_build_object(
                'id', ep."id",
                'name', ep."name",
                'avatar', ep."avatar"
                ${
                  isSelfEntityProfile
                    ? Prisma.sql`,
                  'isFollowing', EXISTS (
                    SELECT 1 FROM "network"."Follow" f2
                    WHERE f2."followerEntityProfileId" = ${selfEntityProfileId}::uuid
                      AND f2."followeeProfileId" = ${profileId}::uuid
                  )`
                    : Prisma.empty
                }
              )
            END AS "EntityProfile"

          FROM "network"."Follow" f
          LEFT JOIN "user"."Profile" u
            ON f."followerProfileId" = u."id"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          LEFT JOIN "company"."EntityProfile" ep
            ON f."followerEntityProfileId" = ep."id"
            AND ep."status" = 'ACTIVE'::"user"."ProfileStatusE"

          WHERE f."followeeProfileId" = ${profileId}::uuid
            AND NOT EXISTS (
              SELECT 1 FROM "network"."BlockedProfile" b
              WHERE (
                (b."blockerId" = ${selfProfileId}::uuid
                AND b."blockedId" = COALESCE(f."followerProfileId", f."followerEntityProfileId"))
                OR
                (b."blockerId" = COALESCE(f."followerProfileId", f."followerEntityProfileId")
                AND b."blockedId" = ${selfProfileId}::uuid)
              )
            )
            ${typeof cursorId === 'number' ? Prisma.sql`AND f."cursorId" < ${cursorId}` : Prisma.empty}

          ORDER BY f."createdAt" DESC
          LIMIT ${pageSize}
        `,
        prismaPG.follow.count({
          where: {
            followeeProfileId: profileId,
            FolloweeProfile: {
              status: 'ACTIVE',
            },
            NOT: {
              OR: [
                {
                  FollowerProfile: {
                    BlockedByProfile: { some: { blockerId: selfProfileId } },
                  },
                },
                {
                  FollowerProfile: {
                    BlockedProfile: { some: { blockedId: selfProfileId } },
                  },
                },
              ],
            },
          },
        }),
      ]);

      if (followersResultTemp?.length) {
        followers.push(
          ...followersResultTemp.map((item) =>
            item.Profile
              ? ({
                  cursorId: Number(item.cursorId),
                  Profile: {
                    id: item.Profile.id,
                    name: item.Profile.name,
                    avatar: item.Profile.avatar,
                    designation: item.Profile?.designationAlternativeId
                      ? {
                          id: item.Profile.designationAlternativeId,
                          name: item.Profile.designationText,
                          dataType: 'master',
                        }
                      : item.Profile?.designationRawDataId
                        ? {
                            id: item.Profile.designationRawDataId,
                            name: item.Profile.designationText,
                            dataType: 'raw',
                          }
                        : null,
                    entity: item.Profile?.entityId
                      ? {
                          id: item.Profile.entityId,
                          name: item.Profile.entityText,
                          dataType: 'master',
                        }
                      : item.Profile?.entityRawDataId
                        ? {
                            id: item.Profile.entityRawDataId,
                            name: item.Profile.entityText,
                            dataType: 'raw',
                          }
                        : null,
                    isConnected: item.isConnected,
                    isFollowing: item.isFollowing,
                  } as ProfileNetworkExternalI,
                } as FollowExternalI)
              : ({
                  cursorId: Number(item.cursorId),
                  EntityProfile: {
                    id: item.EntityProfile.id,
                    name: item.EntityProfile.name,
                    avatar: item.EntityProfile.avatar,
                    isFollowing: item.isFollowing,
                  } as EntityProfileExternalI,
                } as FollowExternalI),
          ),
        );
      }
      return {
        data: followers,
        total: totalFollowers,
      };
    } catch (error) {
      throw new AppError('FLW001', error);
    }
  },
  fetchManyFollowings: async (
    state: FastifyStateI,
    { cursorId, pageSize, profileId }: ProfileIdCursorPaginationI,
  ): Promise<{ data: FollowExternalI[]; total: number }> => {
    try {
      const { profileId: selfProfileId, profileType } = state;
      const isSelfEntityProfile = profileType === 'ENTITY';

      const followings: FollowExternalI[] = [];

      const [followingsResultTemp, followingsCountResult] = await Promise.all([
        prismaPG.$queryRaw<FollowDataI[]>`
          SELECT
          f."cursorId" AS "cursorId",
          CASE WHEN f."followeeProfileId" IS NOT NULL THEN
            json_build_object(
              'id', u."id",
              'name', u."name",
              'avatar', u."avatar",
              'designationText', u."designationText",
              'designationAlternativeId', u."designationAlternativeId",
              'designationRawDataId', u."designationRawDataId",
              'entityText', u."entityText",
              'entityId', u."entityId",
              'entityRawDataId', u."entityRawDataId"
              ${
                !isSelfEntityProfile && selfProfileId !== profileId
                  ? Prisma.sql`,
                  'isConnected', EXISTS (
                    SELECT 1 FROM "network"."Connection" c
                    WHERE c."profileId" = ${selfProfileId}::uuid
                    AND c."connectedId" = u."id"
                  ),
                  'isFollowing', EXISTS (
                    SELECT 1 FROM "network"."Follow" f2
                    WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                    AND f2."followeeProfileId" = u."id"
                  )`
                  : Prisma.empty
              }
            )
          END AS "Profile",
          CASE WHEN f."followeeEntityProfileId" IS NOT NULL THEN
            json_build_object(
              'id', ep."id",
              'name', ep."name",
              'avatar', ep."avatar"
              ${
                isSelfEntityProfile
                  ? Prisma.sql`,
                  'isFollowing', EXISTS (
                    SELECT 1 FROM "network"."Follow" f2
                    WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                    AND f2."followeeEntityProfileId" = ep."id"
                  )`
                  : Prisma.empty
              }
            )
          END AS "EntityProfile"
        FROM "network"."Follow" f
        LEFT JOIN "user"."Profile" u
          ON f."followeeProfileId" = u."id"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN "company"."EntityProfile" ep
          ON f."followeeEntityProfileId" = ep."id"
          AND ep."status" = 'ACTIVE'::"user"."ProfileStatusE"
        WHERE f."followerProfileId" = ${profileId}::uuid
        AND NOT EXISTS (
          SELECT 1 FROM "network"."BlockedProfile" b
          WHERE (
            (b."blockerId" = ${selfProfileId}::uuid
            AND b."blockedId" = COALESCE(f."followeeProfileId", f."followeeEntityProfileId"))
            OR
            (b."blockerId" = COALESCE(f."followeeProfileId", f."followeeEntityProfileId")
            AND b."blockedId" = ${selfProfileId}::uuid)
          )
        )
        ${typeof cursorId === 'number' ? Prisma.sql`AND f."cursorId" < ${cursorId}` : Prisma.empty}
        ORDER BY f."createdAt" DESC
        LIMIT ${pageSize}
        `,
        prismaPG.follow.count({
          where: {
            followerProfileId: profileId,
            FollowerProfile: {
              status: 'ACTIVE',
            },
            NOT: {
              OR: [
                { FolloweeProfile: { BlockedByProfile: { some: { blockerId: selfProfileId } } } },
                { FolloweeProfile: { BlockedProfile: { some: { blockedId: selfProfileId } } } },
              ],
            },
          },
        }),
      ]);

      if (followingsResultTemp?.length) {
        followings.push(
          ...followingsResultTemp.map((item) =>
            item.Profile
              ? ({
                  cursorId: Number(item.cursorId),
                  Profile: {
                    id: item.Profile.id,
                    name: item.Profile.name,
                    avatar: item.Profile.avatar,
                    designation: item.Profile?.designationAlternativeId
                      ? {
                          id: item.Profile.designationAlternativeId,
                          name: item.Profile.designationText,
                          dataType: 'master',
                        }
                      : item.Profile?.designationRawDataId
                        ? {
                            id: item.Profile.designationRawDataId,
                            name: item.Profile.designationText,
                            dataType: 'raw',
                          }
                        : null,
                    entity: item.Profile?.entityId
                      ? {
                          id: item.Profile.entityId,
                          name: item.Profile.entityText,
                          dataType: 'master',
                        }
                      : item.Profile?.entityRawDataId
                        ? {
                            id: item.Profile.entityRawDataId,
                            name: item.Profile.entityText,
                            dataType: 'raw',
                          }
                        : null,
                    isConnected: item.isConnected,
                    isFollowing: item.isFollowing,
                  } as ProfileNetworkExternalI,
                } as FollowExternalI)
              : ({
                  cursorId: Number(item.cursorId),
                  EntityProfile: {
                    id: item.EntityProfile.id,
                    name: item.EntityProfile.name,
                    avatar: item.EntityProfile.avatar,
                    isFollowing: item.isFollowing,
                  } as EntityProfileExternalI,
                } as FollowExternalI),
          ),
        );
      }

      return { data: followings, total: followingsCountResult };
    } catch (error) {
      throw new AppError('FLW002', error);
    }
  },
};

export default FollowModule;
