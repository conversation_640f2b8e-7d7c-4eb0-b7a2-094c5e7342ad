import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { RequestExternalI, RequestFetchManyResultI } from '@interfaces/network/request';
import { ProfileExternalI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';
import { Prisma } from '@prisma/postgres';
import { RequestUpsertOneForSenderI, RequestFetchManyI, RequestUpsertOneForReceiverI } from '@schemas/network/request';
import { isFilled } from '@utils/data/object';

export const RequestModule = {
  upsertOneForSender: async (
    state: FastifyStateI,
    { receiverProfileId, requestedStatus }: RequestUpsertOneForSenderI,
  ): Promise<void> => {
    const selfProfileId = state.profileId;
    console.log('1 upsertOneForSender');

    const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
      where: {
        OR: [
          { blockedId: selfProfileId, blockerId: receiverProfileId },
          { blockedId: receiverProfileId, blockerId: selfProfileId },
        ],
      },
    });
    if (blockedProfileResult) throw new AppError('REQ009');

    const connectionResult = await prismaPG.connection.findFirst({
      select: { connectedId: true },
      where: {
        OR: [
          { profileId: selfProfileId, connectedId: receiverProfileId },
          { profileId: receiverProfileId, connectedId: selfProfileId },
        ],
      },
    });

    if (connectionResult) throw new AppError('REQ011');

    const [sentResult, receivedResult] = await Promise.all([
      prismaPG.request.findFirst({
        select: { id: true, count: true, status: true },
        where: { senderProfileId: selfProfileId, receiverProfileId },
      }),
      prismaPG.request.findFirst({
        select: { id: true, count: true, status: true },
        where: { senderProfileId: receiverProfileId, receiverProfileId: selfProfileId },
      }),
    ]);
    console.log({ requestedStatus });

    if (receivedResult?.status === 'PENDING') throw new AppError('REQ012');
    if (requestedStatus === sentResult?.status) throw new AppError('REQ017');

    switch (requestedStatus) {
      case 'PENDING': {
        const [receiverProfileMetaResult, followResult, selfProfileResult] = await Promise.all([
          prismaPG.profileMeta.findUnique({
            select: { receivedRequestCount: true },
            where: { profileId: receiverProfileId },
          }),
          prismaPG.follow.findFirst({
            select: { cursorId: true },
            where: {
              followeeProfileId: receiverProfileId,
              followerProfileId: selfProfileId,
              followeeEntityProfileId: null,
              followerEntityProfileId: null,
            },
          }),
          prismaPG.profile.findUnique({ select: { name: true }, where: { id: selfProfileId } }),
        ]);

        if (!sentResult) {
          const updatedSenderProfileParams: Prisma.ProfileUncheckedUpdateInput = {};
          const updatedReceiverProfileParams: Prisma.ProfileUncheckedUpdateInput = {};
          const updatedSenderProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {
            sentRequestCount: { increment: 1 },
          };
          const updatedReceiverProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {};

          if (receiverProfileMetaResult?.receivedRequestCount > 0) {
            updatedReceiverProfileMetaParams.receivedRequestCount = { decrement: 1 };
          }

          if (!followResult?.cursorId) {
            updatedSenderProfileParams.followingsCount = { increment: 1 };
            updatedReceiverProfileParams.followersCount = { increment: 1 };
          }

          const [_senderProfile, _receiverProfile, _senderProfileMeta, _receiverProfileMeta, _follow, _request] =
            await prismaPG.$transaction(async (txn) => {
              const result = await Promise.all([
                isFilled(updatedSenderProfileParams)
                  ? txn.profile.update({
                      data: updatedSenderProfileParams,
                      where: { id: selfProfileId },
                    })
                  : null,
                isFilled(updatedReceiverProfileParams)
                  ? txn.profile.update({
                      data: updatedReceiverProfileParams,
                      where: { id: receiverProfileId },
                    })
                  : null,
                txn.profileMeta.update({
                  data: updatedSenderProfileMetaParams,
                  where: { profileId: selfProfileId },
                }),
                isFilled(updatedReceiverProfileMetaParams)
                  ? txn.profileMeta.update({
                      data: updatedReceiverProfileMetaParams,
                      where: { profileId: receiverProfileId },
                    })
                  : null,
                !followResult?.cursorId
                  ? txn.follow.create({
                      data: {
                        followeeProfileId: receiverProfileId,
                        followerProfileId: selfProfileId,
                      },
                    })
                  : null,
                txn.request.create({
                  data: {
                    senderProfileId: selfProfileId,
                    receiverProfileId,
                  },
                  select: {
                    id: true,
                  },
                }),
              ]);
              return result;
            });
          try {
            await CommunicationModule.NotificationModule.createOne({
              actorProfileId: selfProfileId,
              actorProfileName: selfProfileResult?.name ?? 'Unknown',
              receiverProfileId: receiverProfileId,
              topic: 'communication_topic',
              type: 'REQUEST_RECEIVED',
              requestId: _request.id,
            });
          } catch (_error) {
            //
          }
        } else {
          await prismaPG.$transaction(async (txn) => {
            await Promise.all([
              txn.profileMeta.update({
                data: { sentRequestCount: { increment: 1 } },
                where: { profileId: selfProfileId },
              }),
              receiverProfileMetaResult?.receivedRequestCount > 0
                ? txn.profileMeta.update({
                    data: { receivedRequestCount: { decrement: 1 } },
                    where: { profileId: receiverProfileId },
                  })
                : null,
              txn.request.update({
                data: {
                  status: 'PENDING',
                  requestSentAt: new Date(),
                  count: { increment: 1 },
                },
                where: {
                  senderProfileId_receiverProfileId: {
                    senderProfileId: selfProfileId,
                    receiverProfileId,
                  },
                },
              }),
            ]);
          });

          const selfProfileResult = await prismaPG.profile.findUnique({
            select: { name: true },
            where: { id: selfProfileId },
          });
          await CommunicationModule.NotificationModule.createOne({
            actorProfileId: selfProfileId,
            actorProfileName: selfProfileResult?.name ?? 'Unknown',
            receiverProfileId: receiverProfileId,
            topic: 'communication_topic',
            type: 'REQUEST_RECEIVED',
            requestId: sentResult.id,
          });
        }

        break;
      }

      case 'REVOKED': {
        const [receiverProfileMetaResult, senderProfileMetaResult] = await Promise.all([
          prismaPG.profileMeta.findUnique({
            select: { receivedRequestCount: true },
            where: { profileId: receiverProfileId },
          }),
          prismaPG.profileMeta.findUnique({
            select: { sentRequestCount: true },
            where: { profileId: selfProfileId },
          }),
        ]);

        await prismaPG.$transaction(async (txn) => {
          await Promise.all([
            receiverProfileMetaResult?.receivedRequestCount > 0
              ? txn.profileMeta.update({
                  data: { receivedRequestCount: { decrement: 1 } },
                  where: { profileId: receiverProfileId },
                })
              : null,
            senderProfileMetaResult?.sentRequestCount > 0
              ? txn.profileMeta.update({
                  data: { sentRequestCount: { decrement: 1 } },
                  where: { profileId: selfProfileId },
                })
              : null,
            txn.request.update({
              data: { status: 'REVOKED' },
              select: { id: true },
              where: {
                senderProfileId_receiverProfileId: {
                  senderProfileId: selfProfileId,
                  receiverProfileId,
                },
              },
            }),
          ]);
        });

        break;
      }

      default:
        throw new AppError('REQ014');
    }
  },
  upsertOneForReceiver: async (
    state: FastifyStateI,
    { senderProfileId, requestedStatus }: RequestUpsertOneForReceiverI,
  ): Promise<void> => {
    const selfProfileId = state.profileId;

    const blockedProfileResult = await prismaPG.blockedProfile.findFirst({
      where: {
        OR: [
          { blockedId: selfProfileId, blockerId: senderProfileId },
          { blockedId: senderProfileId, blockerId: selfProfileId },
        ],
      },
    });
    if (blockedProfileResult) throw new AppError('REQ009');

    const connectionResult = await prismaPG.connection.findFirst({
      where: {
        OR: [
          { profileId: selfProfileId, connectedId: senderProfileId },
          { profileId: senderProfileId, connectedId: selfProfileId },
        ],
      },
    });
    if (connectionResult) throw new AppError('REQ011');

    const [receivedResult, sentResult] = await Promise.all([
      prismaPG.request.findUnique({
        select: { id: true, count: true, status: true },
        where: {
          senderProfileId_receiverProfileId: {
            senderProfileId,
            receiverProfileId: selfProfileId,
          },
        },
      }),
      prismaPG.request.findUnique({
        select: { id: true, count: true, status: true },
        where: {
          senderProfileId_receiverProfileId: {
            senderProfileId: selfProfileId,
            receiverProfileId: senderProfileId,
          },
        },
      }),
    ]);

    if (!receivedResult) throw new AppError('REQ001');
    if (sentResult?.status === 'PENDING') throw new AppError('REQ013');
    if (requestedStatus === receivedResult?.status) throw new AppError('REQ017');

    switch (requestedStatus) {
      case 'ACCEPTED': {
        const [followResult, selfProfileResult] = await Promise.all([
          prismaPG.follow.findFirst({
            select: { cursorId: true },
            where: {
              followeeProfileId: senderProfileId,
              followerProfileId: selfProfileId,
              followeeEntityProfileId: null,
              followerEntityProfileId: null,
            },
          }),
          prismaPG.profile.findUnique({ select: { name: true }, where: { id: selfProfileId } }),
        ]);

        const updateReceiverProfileParams: Prisma.ProfileUncheckedUpdateInput = {
          connectionsCount: { increment: 1 },
        };
        const updateSenderProfileParams: Prisma.ProfileUncheckedUpdateInput = {
          connectionsCount: { increment: 1 },
        };

        if (!followResult?.cursorId) {
          updateReceiverProfileParams.followingsCount = { increment: 1 };
          updateSenderProfileParams.followersCount = { increment: 1 };
        }

        await prismaPG.$transaction(async (txn) => {
          await Promise.all([
            txn.profile.update({
              data: updateReceiverProfileParams,
              where: { id: selfProfileId },
            }),
            txn.profile.update({
              data: updateSenderProfileParams,
              where: { id: senderProfileId },
            }),
            txn.request.update({
              data: { status: 'ACCEPTED' },
              select: { id: true },
              where: {
                senderProfileId_receiverProfileId: {
                  senderProfileId,
                  receiverProfileId: selfProfileId,
                },
              },
            }),
            !followResult?.cursorId
              ? txn.follow.create({
                  data: {
                    followeeProfileId: senderProfileId,
                    followerProfileId: selfProfileId,
                  },
                })
              : null,
            txn.connection.create({
              select: { cursorId: true },
              data: {
                profileId: selfProfileId,
                connectedId: senderProfileId,
              },
            }),
            txn.connection.create({
              select: { cursorId: true },
              data: {
                profileId: senderProfileId,
                connectedId: selfProfileId,
              },
            }),
          ]);
        });

        await CommunicationModule.NotificationModule.createOne({
          actorProfileId: selfProfileId,
          actorProfileName: selfProfileResult?.name ?? 'Unknown',
          receiverProfileId: senderProfileId,
          receiverProfileType: 'USER',
          topic: 'communication_topic',
          type: 'REQUEST_ACCEPTED',
          requestId: receivedResult.id,
        });

        break;
      }

      case 'REJECTED': {
        const [receiverProfileMetaResult, senderProfileMetaResult] = await Promise.all([
          prismaPG.profileMeta.findUnique({
            select: { receivedRequestCount: true },
            where: { profileId: selfProfileId },
          }),
          prismaPG.profileMeta.findUnique({
            select: { sentRequestCount: true },
            where: { profileId: senderProfileId },
          }),
        ]);

        await prismaPG.$transaction(async (txn) => {
          await Promise.all([
            receiverProfileMetaResult?.receivedRequestCount > 0
              ? txn.profileMeta.update({
                  data: { receivedRequestCount: { decrement: 1 } },
                  where: { profileId: selfProfileId },
                })
              : null,
            senderProfileMetaResult?.sentRequestCount > 0
              ? txn.profileMeta.update({
                  data: { sentRequestCount: { decrement: 1 } },
                  where: { profileId: senderProfileId },
                })
              : null,
            txn.request.update({
              data: { status: 'REJECTED' },
              select: { id: true },
              where: {
                senderProfileId_receiverProfileId: {
                  senderProfileId,
                  receiverProfileId: selfProfileId,
                },
              },
            }),
          ]);
        });

        break;
      }

      default:
        throw new AppError('REQ014');
    }
  },
  fetchManySent: async (
    state: FastifyStateI,
    { page, pageSize }: RequestFetchManyI,
  ): Promise<RequestFetchManyResultI> => {
    const filters: Prisma.RequestWhereInput = {
      senderProfileId: state.profileId,
      status: 'PENDING',
      ReceiverProfile: {
        status: 'ACTIVE',
      },
      NOT: {
        ReceiverProfile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: state.profileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: state.profileId,
                },
              },
            },
          ],
        },
      },
    };

    const [sentRequestsCountResult, sentRequestsResultTemp] = await Promise.all([
      prismaPG.request.count({
        where: filters,
      }),
      prismaPG.request.findMany({
        select: {
          status: true,
          ReceiverProfile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
        },
        where: filters,
        orderBy: {
          updatedAt: 'desc',
        },
        skip: page,
        take: pageSize,
      }),
    ]);

    const sentRequestsResult: RequestExternalI[] = sentRequestsResultTemp.map((item) => ({
      status: item.status,
      Profile: {
        id: item.ReceiverProfile.id,
        name: item.ReceiverProfile.name,
        avatar: item.ReceiverProfile.avatar,
        designation: item.ReceiverProfile?.designationAlternativeId
          ? {
              id: item.ReceiverProfile.designationAlternativeId,
              name: item.ReceiverProfile.designationText,
              dataType: 'master',
            }
          : item.ReceiverProfile?.designationRawDataId
            ? {
                id: item.ReceiverProfile.designationAlternativeId,
                name: item.ReceiverProfile.designationText,
                dataType: 'raw',
              }
            : null,
        entity: item.ReceiverProfile?.entityId
          ? {
              id: item.ReceiverProfile.entityId,
              name: item.ReceiverProfile.entityText,
              dataType: 'master',
            }
          : item.ReceiverProfile?.entityRawDataId
            ? {
                id: item.ReceiverProfile.entityRawDataId,
                name: item.ReceiverProfile.entityText,
                dataType: 'raw',
              }
            : null,
      } as ProfileExternalI,
    }));

    return { data: sentRequestsResult, total: sentRequestsCountResult };
  },
  fetchManyReceived: async (
    state: FastifyStateI,
    { page, pageSize }: RequestFetchManyI,
  ): Promise<RequestFetchManyResultI> => {
    const filters: Prisma.RequestWhereInput = {
      receiverProfileId: state.profileId,
      status: 'PENDING',
      SenderProfile: {
        status: 'ACTIVE',
      },
      NOT: {
        SenderProfile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: state.profileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: state.profileId,
                },
              },
            },
          ],
        },
      },
    };
    const [receivedRequestsCountResult, receivedRequestsResultTemp] = await Promise.all([
      prismaPG.request.count({
        where: filters,
      }),
      prismaPG.request.findMany({
        select: {
          status: true,
          SenderProfile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
        },
        where: filters,
        orderBy: {
          updatedAt: 'desc',
        },
        skip: page,
        take: pageSize,
      }),
    ]);
    const receivedRequestsResult: RequestExternalI[] = receivedRequestsResultTemp.map((item) => ({
      status: item.status,
      Profile: {
        id: item.SenderProfile.id,
        name: item.SenderProfile.name,
        avatar: item.SenderProfile.avatar,
        designation: item.SenderProfile?.designationAlternativeId
          ? {
              id: item.SenderProfile.designationAlternativeId,
              name: item.SenderProfile.designationText,
              dataType: 'master',
            }
          : item.SenderProfile?.designationRawDataId
            ? {
                id: item.SenderProfile.designationAlternativeId,
                name: item.SenderProfile.designationText,
                dataType: 'raw',
              }
            : null,
        entity: item.SenderProfile?.entityId
          ? {
              id: item.SenderProfile.entityId,
              name: item.SenderProfile.entityText,
              dataType: 'master',
            }
          : item.SenderProfile?.entityRawDataId
            ? {
                id: item.SenderProfile.entityRawDataId,
                name: item.SenderProfile.entityText,
                dataType: 'raw',
              }
            : null,
      } as ProfileExternalI,
    }));
    return { data: receivedRequestsResult, total: receivedRequestsCountResult };
  },
};

export default RequestModule;
