import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { BlockingBlockOneResultI } from '@interfaces/network/blocking';
import type { ProfileExternalI } from '@interfaces/user/profile';
import { Prisma, ProfileMeta } from '@prisma/postgres';
import type { BlockingBlockOneI, BlockingFetchManyI, BlockingUnblockOneI } from '@schemas/network/blocking';
export const BlockingModule = {
  blockOne: async (state: FastifyStateI, { toBlockId }: BlockingBlockOneI): Promise<BlockingBlockOneResultI> => {
    try {
      const selfProfileId = state.profileId;

      const [selfProfileResult, toBlockProfileResult] = await Promise.all([
        prismaPG.profile.findUnique({
          select: { connectionsCount: true, followersCount: true, followingsCount: true },
          where: { id: selfProfileId },
        }),
        prismaPG.profile.findUnique({
          select: { connectionsCount: true, followersCount: true, followingsCount: true },
          where: { id: toBlockId },
        }),
      ]);

      if (!(selfProfileResult && toBlockProfileResult)) {
        throw new AppError('BLOCK001');
      }

      const existingBlockResult = await prismaPG.blockedProfile.findFirst({
        select: { blockedId: true },
        where: {
          OR: [
            { blockerId: selfProfileId, blockedId: toBlockId },
            { blockerId: toBlockId, blockedId: selfProfileId },
          ],
        },
      });

      if (existingBlockResult) {
        throw new AppError('BLOCK001');
      }

      const [
        selfRequestResult,
        toBlockRequestResult,
        selfFolloweeResult,
        toBlockFolloweeResult,
        selfConnectionResult,
        toBlockConnectionResult,
      ] = await Promise.all([
        prismaPG.request.findUnique({
          select: { status: true },
          where: {
            senderProfileId_receiverProfileId: {
              senderProfileId: selfProfileId,
              receiverProfileId: toBlockId,
            },
          },
        }),
        prismaPG.request.findUnique({
          select: { status: true },
          where: {
            senderProfileId_receiverProfileId: {
              senderProfileId: toBlockId,
              receiverProfileId: selfProfileId,
            },
          },
        }),
        prismaPG.follow.findFirst({
          select: { cursorId: true },
          where: {
            followerProfileId: toBlockId,
            followeeProfileId: selfProfileId,
            followeeEntityProfileId: null,
            followerEntityProfileId: null,
          },
        }),
        prismaPG.follow.findFirst({
          select: { cursorId: true },
          where: {
            followerProfileId: selfProfileId,
            followeeProfileId: toBlockId,
            followeeEntityProfileId: null,
            followerEntityProfileId: null,
          },
        }),
        prismaPG.connection.findUnique({
          select: { cursorId: true },
          where: {
            profileId_connectedId: {
              profileId: selfProfileId,
              connectedId: toBlockId,
            },
          },
        }),
        prismaPG.connection.findUnique({
          select: { cursorId: true },
          where: {
            profileId_connectedId: {
              profileId: toBlockId,
              connectedId: selfProfileId,
            },
          },
        }),
      ]);

      let selfProfileMetaResult: Pick<ProfileMeta, 'sentRequestCount' | 'receivedRequestCount'> | null = null;
      let toBlockProfileMetaResult: Pick<ProfileMeta, 'sentRequestCount' | 'receivedRequestCount'> | null = null;

      if (selfRequestResult?.status === 'PENDING' || toBlockRequestResult?.status === 'PENDING') {
        [selfProfileMetaResult, toBlockProfileMetaResult] = await Promise.all([
          prismaPG.profileMeta.findUnique({
            select: { sentRequestCount: true, receivedRequestCount: true },
            where: { profileId: selfProfileId },
          }),
          prismaPG.profileMeta.findUnique({
            select: { sentRequestCount: true, receivedRequestCount: true },
            where: { profileId: toBlockId },
          }),
        ]);
      }

      const txCalls: Prisma.PrismaPromise<unknown>[] = [];

      if (selfRequestResult) {
        const update: Prisma.RequestUpdateArgs = {
          data: { status: 'BLOCKED' },
          where: {
            senderProfileId_receiverProfileId: {
              senderProfileId: selfProfileId,
              receiverProfileId: toBlockId,
            },
          },
        };
        txCalls.push(prismaPG.request.update(update));

        if (selfRequestResult.status === 'PENDING') {
          if (selfProfileMetaResult?.sentRequestCount > 0) {
            txCalls.push(
              prismaPG.profileMeta.update({
                data: { sentRequestCount: { decrement: 1 } },
                where: { profileId: selfProfileId },
              }),
            );
          }
          if (toBlockProfileMetaResult?.receivedRequestCount > 0) {
            txCalls.push(
              prismaPG.profileMeta.update({
                data: { receivedRequestCount: { decrement: 1 } },
                where: { profileId: toBlockId },
              }),
            );
          }
        }
      }

      if (toBlockRequestResult) {
        const update: Prisma.RequestUpdateArgs = {
          data: { status: 'BLOCKED' },
          where: {
            senderProfileId_receiverProfileId: {
              senderProfileId: toBlockId,
              receiverProfileId: selfProfileId,
            },
          },
        };
        txCalls.push(prismaPG.request.update(update));

        if (toBlockRequestResult.status === 'PENDING') {
          if (toBlockProfileMetaResult?.sentRequestCount > 0) {
            txCalls.push(
              prismaPG.profileMeta.update({
                data: { sentRequestCount: { decrement: 1 } },
                where: { profileId: toBlockId },
              }),
            );
          }
          if (selfProfileMetaResult?.receivedRequestCount > 0) {
            txCalls.push(
              prismaPG.profileMeta.update({
                data: { receivedRequestCount: { decrement: 1 } },
                where: { profileId: selfProfileId },
              }),
            );
          }
        }
      }

      if (selfFolloweeResult) {
        txCalls.push(
          prismaPG.follow.delete({
            where: {
              followerProfileId: toBlockId,
              followeeProfileId: selfProfileId,
              followeeEntityProfileId: null,
              followerEntityProfileId: null,
            } as Prisma.FollowWhereUniqueInput,
          }),
        );
        if (selfProfileResult.followingsCount > 0) {
          txCalls.push(
            prismaPG.profile.update({
              data: { followingsCount: { decrement: 1 } },
              where: { id: selfProfileId },
            }),
          );
        }
        if (toBlockProfileResult.followersCount > 0) {
          txCalls.push(
            prismaPG.profile.update({
              data: { followersCount: { decrement: 1 } },
              where: { id: toBlockId },
            }),
          );
        }
      }

      if (toBlockFolloweeResult) {
        txCalls.push(
          prismaPG.follow.delete({
            where: {
              followerProfileId: selfProfileId,
              followeeProfileId: toBlockId,
              followeeEntityProfileId: null,
              followerEntityProfileId: null,
            } as Prisma.FollowWhereUniqueInput,
          }),
        );
        if (toBlockProfileResult.followingsCount > 0) {
          txCalls.push(
            prismaPG.profile.update({
              data: { followingsCount: { decrement: 1 } },
              where: { id: toBlockId },
            }),
          );
        }
        if (selfProfileResult.followersCount > 0) {
          txCalls.push(
            prismaPG.profile.update({
              data: { followersCount: { decrement: 1 } },
              where: { id: selfProfileId },
            }),
          );
        }
      }

      if (selfConnectionResult) {
        txCalls.push(
          prismaPG.connection.delete({
            where: {
              profileId_connectedId: {
                profileId: selfProfileId,
                connectedId: toBlockId,
              },
            },
          }),
        );
      }

      if (toBlockConnectionResult) {
        txCalls.push(
          prismaPG.connection.delete({
            where: {
              profileId_connectedId: {
                profileId: toBlockId,
                connectedId: selfProfileId,
              },
            },
          }),
        );
      }

      txCalls.push(
        prismaPG.blockedProfile.create({
          data: {
            blockerId: selfProfileId,
            blockedId: toBlockId,
          },
        }),
      );

      await prismaPG.$transaction(txCalls);

      const finalProfile = await prismaPG.profile.findUnique({
        select: {
          connectionsCount: true,
          followersCount: true,
          followingsCount: true,
        },
        where: { id: selfProfileId },
      });

      return {
        connectionsCount: finalProfile?.connectionsCount || 0,
        followersCount: finalProfile?.followersCount || 0,
        followingsCount: finalProfile?.followingsCount || 0,
      };
    } catch (_error) {
      throw new AppError('BLOCK002');
    }
  },
  unblockOne: async (state: FastifyStateI, { toUnblockId }: BlockingUnblockOneI): Promise<void> => {
    try {
      const selfProfileId = state.profileId;
      const existingBlockResult = await prismaPG.blockedProfile.findUnique({
        select: { blockedId: true },
        where: { blockerId_blockedId: { blockerId: selfProfileId, blockedId: toUnblockId } },
      });

      if (!existingBlockResult) {
        throw new AppError('BLOCK001');
      }

      const deletedBlockResult = await prismaPG.blockedProfile.delete({
        select: { blockedId: true },
        where: { blockerId_blockedId: { blockerId: selfProfileId, blockedId: toUnblockId } },
      });

      if (!deletedBlockResult) {
        throw new AppError('BLOCK003');
      }
    } catch (_error) {
      throw new AppError('BLOCK003');
    }
  },
  fetchMany: async (
    state: FastifyStateI,
    { page, pageSize }: BlockingFetchManyI,
  ): Promise<{ data: ProfileExternalI[]; total: number }> => {
    try {
      const selfProfileId = state.profileId;
      const blockedProfilesResult: ProfileExternalI[] = [];
      const [blockedProfilesResultTemp, total] = await Promise.all([
        prismaPG.blockedProfile.findMany({
          select: {
            Blocked: {
              select: {
                id: true,
                avatar: true,
                name: true,
                designationText: true,
                designationAlternativeId: true,
                designationRawDataId: true,
                entityText: true,
                entityId: true,
                entityRawDataId: true,
              },
            },
          },
          where: {
            blockerId: selfProfileId,
            Blocked: {
              status: 'ACTIVE',
            },
          },
          orderBy: { createdAt: 'desc' },
          take: pageSize,
          skip: page * pageSize,
        }),
        prismaPG.blockedProfile.count({
          where: {
            blockerId: selfProfileId,
            Blocked: {
              status: 'ACTIVE',
            },
          },
        }),
      ]);
      if (blockedProfilesResultTemp.length) {
        blockedProfilesResult.push(
          ...blockedProfilesResultTemp.map(
            (item) =>
              ({
                id: item.Blocked.id,
                name: item.Blocked.name,
                avatar: item.Blocked.avatar,
                designation: item.Blocked?.designationAlternativeId
                  ? {
                      id: item.Blocked.designationAlternativeId,
                      name: item.Blocked.designationText,
                      dataType: 'master',
                    }
                  : item.Blocked?.designationRawDataId
                    ? {
                        id: item.Blocked.designationAlternativeId,
                        name: item.Blocked.designationText,
                        dataType: 'raw',
                      }
                    : null,
                entity: item.Blocked?.entityId
                  ? {
                      id: item.Blocked.entityId,
                      name: item.Blocked.entityText,
                      dataType: 'master',
                    }
                  : item.Blocked?.entityRawDataId
                    ? {
                        id: item.Blocked.entityRawDataId,
                        name: item.Blocked.entityText,
                        dataType: 'raw',
                      }
                    : null,
              }) as ProfileExternalI,
          ),
        );
      }

      return { data: blockedProfilesResult, total };
    } catch (_error) {
      throw new AppError('BLOCK004');
    }
  },
};

export default BlockingModule;
