import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';

export const NewsTopicModule = {
  fetchAll: async () => {
    try {
      const topics = await prismaPG.newsTopic.findMany({
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
        },
        where: {
          isActive: true,
        },
        orderBy: {
          name: 'asc',
        },
      });
      return topics;
    } catch (_error) {
      throw new AppError('NEWS001', 'Failed to fetch news topics');
    }
  },

  fetchById: async (
    id: string,
    select: {
      id: true;
      name: true;
      slug: true;
      description: true;
    },
  ) => {
    const topic = await prismaPG.newsTopic.findUnique({
      select,
      where: { id },
    });

    if (!topic) {
      throw new AppError('NEWS002', 'News topic not found');
    }

    return topic;
  },
};
