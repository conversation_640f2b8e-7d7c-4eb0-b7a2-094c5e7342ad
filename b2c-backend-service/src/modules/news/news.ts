import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { TotalDataI } from '@interfaces/common/data';
import type { NewsItemI, NewsFetchManySQLI } from '@interfaces/news/news';
import type { NewsFetchManyI } from '@schemas/news/news';
import AppError from '@classes/AppError';

type SQLParameter = string | number | bigint | boolean | Date | string[] | number[] | null | undefined;

export const NewsModule = {
  fetchMany: async (
    state: FastifyStateI,
    { cursorId, pageSize, topicId, sortBy, sortOrder }: NewsFetchManyI,
  ): Promise<TotalDataI<NewsItemI> & { nextCursorId: string | null }> => {
    try {
      const limit = Math.min(parseInt(pageSize || '10'), 100);
      const orderDirection = sortOrder === 'asc' ? 'ASC' : 'DESC';
      const sortColumn = sortBy === 'scrapedAt' ? 'n."scrapedAt"' : 'n."publishedDate"';

      let whereClause = 'WHERE 1=1';
      const queryParams: SQLParameter[] = [];
      let paramIndex = 1;

      if (cursorId) {
        const cursorOperator = sortOrder === 'asc' ? '>' : '<';
        whereClause += ` AND n."cursorId" ${cursorOperator} $${paramIndex}`;
        queryParams.push(BigInt(cursorId));
        paramIndex++;
      }

      if (topicId) {
        whereClause += ` AND EXISTS (
            SELECT 1 FROM "news"."NewsTopicMapping" ntm
            WHERE ntm."newsId" = n.id
            AND ntm."topicId" = $${paramIndex}::uuid
          )`;
        queryParams.push(topicId);
        paramIndex++;
      }

      const query = `
          SELECT
            n.id,
            n.title,
            n.link,
            n."publishedDate"::text,
            n."scrapedAt"::text,
            n."cursorId"::text,
            n."providerId",
            np.name as "providerName",
            np."baseUrl" as "providerBaseUrl",
            COALESCE(
              json_agg(
                json_build_object(
                  'id', nt.id,
                  'name', nt.name,
                  'slug', nt.slug,
                  'description', nt.description
                ) ORDER BY nt.name
              ) FILTER (WHERE nt.id IS NOT NULL),
              '[]'::json
            ) as topics
          FROM "news"."News" n
          INNER JOIN "master"."NewsProvider" np ON n."providerId" = np.id
          LEFT JOIN "news"."NewsTopicMapping" ntm ON n.id = ntm."newsId"
          LEFT JOIN "master"."NewsTopic" nt ON ntm."topicId" = nt.id AND nt."isActive" = true
          ${whereClause}
          GROUP BY n.id, n.title, n.link, n."publishedDate", n."scrapedAt", n."cursorId", n."providerId", np.name, np."baseUrl"
          ORDER BY ${sortColumn} ${orderDirection}, n."cursorId"
          LIMIT $${paramIndex}
        `;
      queryParams.push(limit + 1);

      const rawResults = await prismaPG.$queryRawUnsafe<NewsFetchManySQLI[]>(query, ...queryParams);

      const hasMore = rawResults.length > limit;
      const results = hasMore ? rawResults.slice(0, limit) : rawResults;

      const transformedResults: NewsItemI[] = results.map((row) => ({
        id: row.id,
        title: row.title,
        link: row.link,
        publishedDate: row.publishedDate,
        provider: {
          id: row.providerId,
          name: row.providerName,
          baseUrl: row.providerBaseUrl,
        },
        scrapedAt: row.scrapedAt,
        cursorId: row.cursorId,
        topics: typeof row.topics === 'string' ? JSON.parse(row.topics) : row.topics,
      }));

      let total = 0;
      if (!cursorId) {
        let countWhereClause = 'WHERE 1=1';
        const countParams: SQLParameter[] = [];
        const countParamIndex = 1;

        if (topicId) {
          countWhereClause += ` AND EXISTS (
              SELECT 1 FROM "news"."NewsTopicMapping" ntm
              WHERE ntm."newsId" = n.id
              AND ntm."topicId" = $${countParamIndex}::uuid
            )`;
          countParams.push(topicId);
        }

        const countQuery = `
            SELECT COUNT(*)::int as count
            FROM "news"."News" n
            ${countWhereClause}
          `;

        const countResult = await prismaPG.$queryRawUnsafe<[{ count: number }]>(countQuery, ...countParams);
        total = countResult[0]?.count || 0;
      }

      let nextCursorId: string | null = null;
      if (hasMore && results.length > 0) {
        const lastItem = results[results.length - 1];
        nextCursorId = lastItem.cursorId; // Use the numeric cursorId, not the date
      }

      return {
        data: transformedResults,
        total,
        nextCursorId,
      };
    } catch (error) {
      throw new AppError(
        'NEWS003',
        `Failed to fetch news articles: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  },
};
