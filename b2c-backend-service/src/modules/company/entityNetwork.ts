import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { EntityProfileExternalI } from '@interfaces/company/entityProfile';
import { FollowDataI, FollowExternalI } from '@interfaces/network/follow';
import { ProfileNetworkExternalI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';
import { Prisma } from '@prisma/postgres';
import { EntityProfileFollowOneParamsI, EntityProfileUnFollowOneParamsI } from '@schemas/company/entityNetwork';
import { EntityProfileIdCursorPaginationI } from '@schemas/company/entityProfile';

const EntityNetworkModule = {
  followOne: async (
    state: FastifyStateI,
    { followeeEntityProfileId }: EntityProfileFollowOneParamsI,
  ): Promise<void> => {
    const { profileType, profileId: selfProfileId, entityProfileId: selfEntityProfileId } = state;

    if (profileType === 'ENTITY' && selfEntityProfileId === followeeEntityProfileId) {
      throw new AppError('ENTWRK002');
    }
    const followCheckAndCreate =
      profileType === 'ENTITY'
        ? { followerEntityProfileId: selfEntityProfileId, followeeEntityProfileId }
        : { followerProfileId: selfProfileId, followeeEntityProfileId };

    await prismaPG.$transaction(async (txn) => {
      const existingFollow = await txn.follow.findFirst({
        where: followCheckAndCreate as Prisma.FollowWhereUniqueInput,
        select: { id: true },
      });
      if (existingFollow) {
        throw new AppError('ENTWRK001');
      }

      const [followerData, followeeMember] = await Promise.all([
        profileType === 'ENTITY'
          ? txn.entityProfile.findUnique({
              where: { id: selfEntityProfileId },
              select: { name: true },
            })
          : txn.profile.findUnique({
              where: { id: selfProfileId },
              select: { name: true },
            }),
        txn.entityMember.findFirst({
          where: { entityProfileId: followeeEntityProfileId },
          select: { profileId: true },
        }),
      ]);

      await Promise.all([
        txn.follow.create({
          data: followCheckAndCreate,
        }),
        profileType === 'ENTITY'
          ? txn.entityProfile.update({
              where: { id: selfEntityProfileId },
              data: { followingsCount: { increment: 1 } },
            })
          : txn.profile.update({
              where: { id: selfProfileId },
              data: { followingsCount: { increment: 1 } },
            }),
        txn.entityProfile.update({
          where: { id: followeeEntityProfileId },
          data: { followersCount: { increment: 1 } },
        }),
      ]);

      await CommunicationModule.NotificationModule.createOne({
        actorProfileId: selfProfileId,
        actorProfileName: followerData?.name || 'Unknown',
        receiverProfileId: followeeMember.profileId,
        receiverProfileType: 'USER',
        topic: 'communication_topic',
        type: 'FOLLOWER',
      });
    });
  },
  unfollowOne: async (
    state: FastifyStateI,
    { unfolloweeEntityProfileId }: EntityProfileUnFollowOneParamsI,
  ): Promise<void> => {
    const { profileType, profileId: selfProfileId, entityProfileId: selfEntityProfileId } = state;

    const followCheckAndDelete =
      profileType === 'ENTITY'
        ? { followerEntityProfileId: selfEntityProfileId, followeeEntityProfileId: unfolloweeEntityProfileId }
        : { followerProfileId: selfProfileId, followeeEntityProfileId: unfolloweeEntityProfileId };

    const followResult = await prismaPG.follow.findFirst({
      where: followCheckAndDelete,
      select: { id: true },
    });

    if (!followResult) {
      throw new AppError('ENTWRK003');
    }

    const [unfollowResult, _followerResult, _followeeResult] = await prismaPG.$transaction([
      prismaPG.follow.deleteMany({
        where: followCheckAndDelete as Prisma.FollowWhereUniqueInput,
      }),
      profileType === 'ENTITY'
        ? prismaPG.entityProfile.update({
            data: {
              followingsCount: {
                decrement: 1,
              },
            },
            where: {
              id: selfEntityProfileId,
            },
          })
        : prismaPG.profile.update({
            data: {
              followingsCount: {
                decrement: 1,
              },
            },
            where: {
              id: selfProfileId,
            },
          }),
      prismaPG.entityProfile.update({
        data: {
          followersCount: {
            decrement: 1,
          },
        },
        where: {
          id: unfolloweeEntityProfileId,
        },
      }),
    ]);
    if (!unfollowResult) {
      throw new AppError('ENTWRK004');
    }
  },
  // followStatus: async (
  //   state: FastifyStateI,
  //   { followeeProfileId }: FollowOneParamsI,
  // ): Promise<{ isFollowing: boolean }> => {
  //   const selfProfileId = state.profileId;
  //   const existingFollowResult = await prismaPG.follow.count({
  //     where: {
  //       followerProfileId: selfProfileId,
  //       followeeProfileId,
  //     },
  //   });
  //   return { isFollowing: Boolean(existingFollowResult) };
  // }
  fetchManyFollowers: async (
    state: FastifyStateI,
    { cursorId, pageSize, entityProfileId }: EntityProfileIdCursorPaginationI,
  ): Promise<{ data: FollowExternalI[]; total: number }> => {
    try {
      const { profileId: selfProfileId, profileType, entityProfileId: selfEntityProfileId } = state;
      const isSelfEntityProfile = profileType === 'ENTITY';

      const followers: FollowExternalI[] = [];

      const [followersResultTemp, totalFollowers] = await Promise.all([
        prismaPG.$queryRaw<FollowDataI[]>`
            SELECT
              f."cursorId" AS "cursorId",

              CASE WHEN u."id" IS NOT NULL THEN
                json_build_object(
                  'id', u."id",
                  'name', u."name",
                  'avatar', u."avatar",
                  'designationText', u."designationText",
                  'designationAlternativeId', u."designationAlternativeId",
                  'designationRawDataId', u."designationRawDataId",
                  'entityText', u."entityText",
                  'entityId', u."entityId",
                  'entityRawDataId', u."entityRawDataId"
                  ${
                    !isSelfEntityProfile
                      ? Prisma.sql`,
                    'isConnected', EXISTS (
                      SELECT 1 FROM "network"."Connection" c
                      WHERE c."profileId" = ${selfProfileId}::uuid
                        AND c."connectedId" = u."id"
                    ),
                    'isFollowing', EXISTS (
                      SELECT 1 FROM "network"."Follow" f2
                      WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                        AND f2."followeeProfileId" = u."id"
                    )`
                      : Prisma.empty
                  }
                )
              END AS "Profile",
              CASE WHEN ep."id" IS NOT NULL THEN
                json_build_object(
                  'id', ep."id",
                  'name', ep."name",
                  'avatar', ep."avatar"
                  ${
                    isSelfEntityProfile && selfEntityProfileId !== entityProfileId
                      ? Prisma.sql`,
                    'isFollowing', EXISTS (
                      SELECT 1 FROM "network"."Follow" f2
                      WHERE f2."followerEntityProfileId" = ${selfEntityProfileId}::uuid
                        AND f2."followeeEntityProfileId" = ${entityProfileId}::uuid
                    )`
                      : Prisma.empty
                  }
                )
              END AS "EntityProfile"

            FROM "network"."Follow" f
            LEFT JOIN "user"."Profile" u
              ON f."followerProfileId" = u."id"
              AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
            LEFT JOIN "company"."EntityProfile" ep
              ON f."followerEntityProfileId" = ep."id"
              AND ep."status" = 'ACTIVE'::"user"."ProfileStatusE"

            WHERE f."followeeEntityProfileId" = ${entityProfileId}::uuid
              AND NOT EXISTS (
                SELECT 1 FROM "network"."BlockedProfile" b
                WHERE (
                  (b."blockerId" = ${selfProfileId}::uuid
                  AND b."blockedId" = COALESCE(f."followerProfileId", f."followerEntityProfileId"))
                  OR
                  (b."blockerId" = COALESCE(f."followerProfileId", f."followerEntityProfileId")
                  AND b."blockedId" = ${selfProfileId}::uuid)
                )
              )
              ${typeof cursorId === 'number' ? Prisma.sql`AND f."cursorId" < ${cursorId}` : Prisma.empty}

            ORDER BY f."createdAt" DESC
            LIMIT ${pageSize}
          `,
        prismaPG.follow.count({
          where: {
            followeeEntityProfileId: entityProfileId,
            FolloweeEntityProfile: {
              status: 'ACTIVE',
            },
            NOT: {
              OR: [
                {
                  FollowerProfile: {
                    BlockedByProfile: { some: { blockerId: selfProfileId } },
                  },
                },
                {
                  FollowerProfile: {
                    BlockedProfile: { some: { blockedId: selfProfileId } },
                  },
                },
              ],
            },
          },
        }),
      ]);

      if (followersResultTemp?.length) {
        followers.push(
          ...followersResultTemp.map((item) =>
            item.Profile
              ? ({
                  cursorId: Number(item.cursorId),
                  Profile: {
                    id: item.Profile.id,
                    name: item.Profile.name,
                    avatar: item.Profile.avatar,
                    designation: item.Profile?.designationAlternativeId
                      ? {
                          id: item.Profile.designationAlternativeId,
                          name: item.Profile.designationText,
                          dataType: 'master',
                        }
                      : item.Profile?.designationRawDataId
                        ? {
                            id: item.Profile.designationRawDataId,
                            name: item.Profile.designationText,
                            dataType: 'raw',
                          }
                        : null,
                    entity: item.Profile?.entityId
                      ? {
                          id: item.Profile.entityId,
                          name: item.Profile.entityText,
                          dataType: 'master',
                        }
                      : item.Profile?.entityRawDataId
                        ? {
                            id: item.Profile.entityRawDataId,
                            name: item.Profile.entityText,
                            dataType: 'raw',
                          }
                        : null,
                    isConnected: item.isConnected,
                    isFollowing: item.isFollowing,
                  } as ProfileNetworkExternalI,
                } as FollowExternalI)
              : ({
                  cursorId: Number(item.cursorId),
                  EntityProfile: {
                    id: item.EntityProfile.id,
                    name: item.EntityProfile.name,
                    avatar: item.EntityProfile.avatar,
                    isFollowing: item.isFollowing,
                  } as EntityProfileExternalI,
                } as FollowExternalI),
          ),
        );
      }
      return {
        data: followers,
        total: totalFollowers,
      };
    } catch (error) {
      throw new AppError('FLW001', error);
    }
  },
  fetchManyFollowings: async (
    state: FastifyStateI,
    { cursorId, pageSize, entityProfileId }: EntityProfileIdCursorPaginationI,
  ): Promise<{ data: FollowExternalI[]; total: number }> => {
    try {
      const { profileId: selfProfileId, profileType, entityProfileId: selfEntityProfileId } = state;
      const isSelfEntityProfile = profileType === 'ENTITY';

      const followings: FollowExternalI[] = [];

      const [followingsResultTemp, followingsCountResult] = await Promise.all([
        prismaPG.$queryRaw<FollowDataI[]>`
            SELECT
            f."cursorId" AS "cursorId",
            CASE WHEN f."followeeProfileId" IS NOT NULL THEN
              json_build_object(
                'id', u."id",
                'name', u."name",
                'avatar', u."avatar",
                'designationText', u."designationText",
                'designationAlternativeId', u."designationAlternativeId",
                'designationRawDataId', u."designationRawDataId",
                'entityText', u."entityText",
                'entityId', u."entityId",
                'entityRawDataId', u."entityRawDataId"
                ${
                  !isSelfEntityProfile
                    ? Prisma.sql`,
                    'isConnected', EXISTS (
                      SELECT 1 FROM "network"."Connection" c
                      WHERE c."profileId" = ${selfProfileId}::uuid
                      AND c."connectedId" = u."id"
                    ),
                    'isFollowing', EXISTS (
                      SELECT 1 FROM "network"."Follow" f2
                      WHERE f2."followerProfileId" = ${selfProfileId}::uuid
                      AND f2."followeeProfileId" = u."id"
                    )`
                    : Prisma.empty
                }
              )
            END AS "Profile",
            CASE WHEN f."followeeEntityProfileId" IS NOT NULL THEN
              json_build_object(
                'id', ep."id",
                'name', ep."name",
                'avatar', ep."avatar"
                ${
                  isSelfEntityProfile && selfEntityProfileId !== entityProfileId
                    ? Prisma.sql`,
                    'isFollowing', EXISTS (
                      SELECT 1 FROM "network"."Follow" f2
                      WHERE f2."followerEntityProfileId" = ${selfEntityProfileId}::uuid
                      AND f2."followeeEntityProfileId" = ep."id"
                    )`
                    : Prisma.empty
                }
              )
            END AS "EntityProfile"
          FROM "network"."Follow" f
          LEFT JOIN "user"."Profile" u
            ON f."followeeProfileId" = u."id"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          LEFT JOIN "company"."EntityProfile" ep
            ON f."followeeEntityProfileId" = ep."id"
            AND ep."status" = 'ACTIVE'::"user"."ProfileStatusE"
          WHERE f."followerEntityProfileId" = ${entityProfileId}::uuid
          AND NOT EXISTS (
            SELECT 1 FROM "network"."BlockedProfile" b
            WHERE (
              (b."blockerId" = ${selfProfileId}::uuid
              AND b."blockedId" = COALESCE(f."followeeProfileId", f."followeeEntityProfileId"))
              OR
              (b."blockerId" = COALESCE(f."followeeProfileId", f."followeeEntityProfileId")
              AND b."blockedId" = ${selfProfileId}::uuid)
            )
          )
          ${typeof cursorId === 'number' ? Prisma.sql`AND f."cursorId" < ${cursorId}` : Prisma.empty}
          ORDER BY f."createdAt" DESC
          LIMIT ${pageSize}
          `,
        prismaPG.follow.count({
          where: {
            followerEntityProfileId: entityProfileId,
            FollowerEntityProfile: {
              status: 'ACTIVE',
            },
            NOT: {
              OR: [
                { FolloweeProfile: { BlockedByProfile: { some: { blockerId: selfProfileId } } } },
                { FolloweeProfile: { BlockedProfile: { some: { blockedId: selfProfileId } } } },
              ],
            },
          },
        }),
      ]);

      if (followingsResultTemp?.length) {
        followings.push(
          ...followingsResultTemp.map((item) =>
            item.Profile
              ? ({
                  cursorId: Number(item.cursorId),
                  Profile: {
                    id: item.Profile.id,
                    name: item.Profile.name,
                    avatar: item.Profile.avatar,
                    designation: item.Profile?.designationAlternativeId
                      ? {
                          id: item.Profile.designationAlternativeId,
                          name: item.Profile.designationText,
                          dataType: 'master',
                        }
                      : item.Profile?.designationRawDataId
                        ? {
                            id: item.Profile.designationRawDataId,
                            name: item.Profile.designationText,
                            dataType: 'raw',
                          }
                        : null,
                    entity: item.Profile?.entityId
                      ? {
                          id: item.Profile.entityId,
                          name: item.Profile.entityText,
                          dataType: 'master',
                        }
                      : item.Profile?.entityRawDataId
                        ? {
                            id: item.Profile.entityRawDataId,
                            name: item.Profile.entityText,
                            dataType: 'raw',
                          }
                        : null,
                    isConnected: item.isConnected,
                    isFollowing: item.isFollowing,
                  } as ProfileNetworkExternalI,
                } as FollowExternalI)
              : ({
                  cursorId: Number(item.cursorId),
                  EntityProfile: {
                    id: item.EntityProfile.id,
                    name: item.EntityProfile.name,
                    avatar: item.EntityProfile.avatar,
                    isFollowing: item.isFollowing,
                  } as EntityProfileExternalI,
                } as FollowExternalI),
          ),
        );
      }

      return { data: followings, total: followingsCountResult };
    } catch (error) {
      throw new AppError('FLW002', error);
    }
  },
};

export default EntityNetworkModule;
