import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  EntityIsAnyMemberI,
  EntityMemberDeleteParamsI,
  EntityMemberFetchParamsI,
  EntityMemberUpsertParamsI,
} from '@schemas/company/entityMember';
import { EntityMemberClientI } from '@interfaces/company/entityMember';
import { UUIDI } from '@schemas/common/common';
import { Prisma } from '@prisma/postgres';
import { UndefinedNullableI } from '@interfaces/common/data';

export const EntityMemberModule = {
  fetchAllProfileIds: async (entityProfileId: UUIDI, selfProfileId?: UndefinedNullableI<UUIDI>): Promise<UUIDI[]> => {
    const where: Prisma.EntityMemberWhereInput = {
      entityProfileId,
    };
    if (selfProfileId) {
      where.NOT = {
        id: selfProfileId,
      };
    }
    const entityMembers = await prismaPG.entityMember.findMany({ where, select: { profileId: true } });
    return entityMembers?.map((item) => item.profileId);
  },
  fetchMembers: async (
    filters: EntityMemberFetchParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EntityMemberClientI[]> => {
    return await txn.entityMember.findMany({
      where: {
        OR: [{ entityId: filters.entityId }, { entityRawDataId: filters.entityId }],
      },
      select: {
        id: true,
        entityId: true,
        entityRawDataId: true,
        profileId: true,
        role: true,
      },
    });
  },
  upsertMember: async (
    state: FastifyStateI,
    params: EntityMemberUpsertParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EntityMemberClientI> => {
    const selfProfileId = state.profileId;

    if (!selfProfileId) throw new AppError('AUTH020');
    const actingMember = await txn.entityMember.findFirst({
      where: {
        OR: [
          {
            entityId: params.entityId,
            profileId: selfProfileId,
          },
          {
            entityRawDataId: params.entityRawDataId,
            profileId: selfProfileId,
          },
        ],
      },
      select: { role: true },
    });

    if (!actingMember || actingMember.role !== 'ADMIN') {
      throw new AppError('ENT001');
    }

    const isSelfUpdate = selfProfileId === params.profileId;

    if (isSelfUpdate && params.role !== 'ADMIN') {
      const adminCount = await txn.entityMember.count({
        where: {
          OR: [{ entityId: params.entityId }, { entityRawDataId: params.entityRawDataId }],
          role: 'ADMIN',
        },
      });

      if (adminCount === 1) {
        throw new AppError('ENT002');
      }
    }

    const existingMember = await txn.entityMember.findFirst({
      where: {
        profileId: params.profileId,
        OR: [{ entityId: params.entityId }, { entityRawDataId: params.entityRawDataId }],
      },
      select: { id: true },
    });

    const whereClause = (
      params.entityId
        ? { entityId: params.entityId, profileId: params.profileId }
        : { entityRawDataId: params.entityRawDataId, profileId: params.profileId }
    ) as Prisma.EntityMemberWhereUniqueInput;

    const result = await txn.entityMember.upsert({
      where: whereClause,
      update: {
        role: params.role,
      },
      create: {
        entityProfileId: params.entityProfileId,
        entityId: params.entityId,
        entityRawDataId: params.entityRawDataId,
        profileId: params.profileId,
        role: params.role,
      },
      select: {
        id: true,
        entityId: true,
        entityRawDataId: true,
        profileId: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!existingMember) {
      if (params.entityId) {
        await txn.entity.update({
          where: { id: params.entityId },
          data: { memberCount: { increment: 1 } },
        });
      } else if (params.entityRawDataId) {
        await txn.entityRawData.update({
          where: { id: params.entityRawDataId },
          data: { memberCount: { increment: 1 } },
        });
      }
    }

    return result;
  },
  deleteMember: async (
    state: FastifyStateI,
    { entityId, entityRawDataId, profileId }: EntityMemberDeleteParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<void> => {
    const selfProfileId = state.profileId;
    if (!selfProfileId) throw new AppError('AUTH020');
    const actingMember = await txn.entityMember.findFirst({
      where: {
        OR: [
          { entityId, profileId: selfProfileId },
          { entityRawDataId, profileId: selfProfileId },
        ],
      },
      select: { role: true },
    });

    if (!actingMember) throw new AppError('ENT003');

    const targetMember = await txn.entityMember.findFirst({
      where: {
        profileId,
        OR: [{ entityId }, { entityRawDataId }],
      },
      select: { role: true },
    });

    if (!targetMember) throw new AppError('ENT003');

    const isSelfDelete = selfProfileId === profileId;

    if (!isSelfDelete && actingMember.role !== 'ADMIN') {
      throw new AppError('ENT001');
    }

    if (targetMember.role === 'ADMIN') {
      const adminCount = await txn.entityMember.count({
        where: {
          OR: [{ entityId }, { entityRawDataId }],
          role: 'ADMIN',
        },
      });

      if (adminCount <= 1) {
        throw new AppError('ENT004');
      }
    }

    const whereClause = (
      entityId ? { entityId, profileId } : { entityRawDataId, profileId }
    ) as Prisma.EntityMemberWhereUniqueInput;

    await txn.entityMember.delete({
      where: whereClause,
    });

    if (entityId) {
      await txn.entity.update({
        where: { id: entityId },
        data: { memberCount: { decrement: 1 } },
      });
    } else if (entityRawDataId) {
      await txn.entityRawData.update({
        where: { id: entityRawDataId },
        data: { memberCount: { decrement: 1 } },
      });
    }
  },
  isAnyMember: async ({ entity, profileId }: EntityIsAnyMemberI): Promise<void> => {
    const entityMember = await prismaPG.entityMember.findUnique({
      where: (entity?.dataType === 'master'
        ? {
            entityId: entity.id,
            profileId,
          }
        : {
            entityRawDataId: entity.id,
            profileId,
          }) as Prisma.EntityMemberWhereUniqueInput,
    });
    if (!entityMember) {
      throw new AppError('ENT014');
    }
    return;
  },
};
