import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { DBDataTypeI } from '@consts/common/data';
import type { IdTitleI } from '@consts/master/common';
import type { CursorDataI, NumberNullI } from '@interfaces/common/data';
import type {
  EntityBenefitModuleFetchsertParamsI,
  EntityBenefitNestedClientI,
  EntityBenefitPaginatedClientI,
} from '@interfaces/company/entityBenefit';
import { Prisma } from '@prisma/postgres';
import type { CursorPaginationSearchI, IdTypeI } from '@schemas/common/common';

export const EntityBenefitModule = {
  fetchById: async (
    filters: IdTypeI,
    select: IdTitleI = { id: true, title: true },
    _isThrowingError: boolean = true,
  ): Promise<EntityBenefitNestedClientI> => {
    if (filters.dataType === 'raw') {
      const entityBenefitRawDataResult = await prismaPG.entityBenefitRawData.findFirst({
        where: { id: filters.id },
        select,
      });

      if (!entityBenefitRawDataResult && _isThrowingError) {
        throw new AppError('ORG001');
      }
      return {
        ...entityBenefitRawDataResult,
        dataType: 'raw',
      } as EntityBenefitNestedClientI;
    }
  },
  fetchForClient: async ({
    cursorId,
    pageSize,
    search,
  }: CursorPaginationSearchI): Promise<CursorDataI<EntityBenefitPaginatedClientI>> => {
    search = search?.trim()?.toLowerCase();
    const entityBenefitsTemp = await prismaPG.$queryRaw<EntityBenefitPaginatedClientI[]>`
        SELECT
          jrw."id",
          jrw."title",
          jrw."cursorId",
          'raw' AS "dataType"
        FROM
          "rawData"."EntityBenefitRawData" jrw
        WHERE
          jrw."title" ILIKE ${`${search}%`}
          ${cursorId ? Prisma.sql` AND jrw."cursorId" < ${cursorId}` : Prisma.empty}
        ORDER BY "title" ASC
        LIMIT ${pageSize + 1}
      `;
    const hasNextPage = entityBenefitsTemp.length > pageSize;
    const results = hasNextPage ? entityBenefitsTemp.slice(0, pageSize) : entityBenefitsTemp;

    const nextCursorId: NumberNullI = hasNextPage ? Number(results[results.length - 1].cursorId) : null;
    return {
      data: results,
      nextCursorId,
    };
  },

  fetchsert: async ({ title }: EntityBenefitModuleFetchsertParamsI): Promise<EntityBenefitNestedClientI> => {
    const existingEntityBenefitResult = await prismaPG.$queryRaw<EntityBenefitNestedClientI[]>`
    SELECT
        drw."id",
        drw."title",
        'raw' AS "dataType"
      FROM "rawData"."EntityBenefitRawData" drw
      WHERE drw."title" ILIKE ${title.trim().toLowerCase()}
      LIMIT 1
  `;

    if (existingEntityBenefitResult.length) {
      return existingEntityBenefitResult[0] as EntityBenefitNestedClientI;
    }

    const entityBenefitResult = await prismaPG.entityBenefitRawData.create({
      data: {
        title,
      },
      select: {
        id: true,
      },
    });

    if (!entityBenefitResult) {
      throw new AppError('DGR002');
    }

    return {
      ...entityBenefitResult,
      title,
      dataType: 'raw' as DBDataTypeI,
    } as EntityBenefitNestedClientI;
  },
  count: async (rawDataEntityBenefits: string[]) => {
    rawDataEntityBenefits = Array.from(new Set<string>(rawDataEntityBenefits));
    const countRawDataEntityBenefits = (await rawDataEntityBenefits?.length)
      ? prismaPG.entityBenefitRawData.count({
          where: {
            id: {
              in: rawDataEntityBenefits,
            },
          },
        })
      : 0;
    return { countRawDataEntityBenefits };
  },
};
