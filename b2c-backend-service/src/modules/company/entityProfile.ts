import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  EntityAboutUpdateParamsI,
  EntityCreateOneParamsI,
  EntityProfileIdI,
  EntityTabsFetchParamsI,
  EntityTabsUpdateParamsI,
  VerifyOTPForEntityProfileVerificationI,
} from '@schemas/company/entityProfile';
import {
  EntityProfileAboutForExternalClient,
  EntityProfileBasicDetails,
  EntityProfileForClient,
  EntityTabsClientI,
} from '@interfaces/company/entityProfile';
import CommunicationModule from '@modules/communication';
import { CommunicationConfigI } from '@interfaces/appConfig/appConfig';
import { addMsToDate, getCurrentDate } from '@utils/data/date';
import { EntityProfile, Prisma } from '@prisma/postgres';
import AppConfig from '@modules/appConfig';
import { EntityMemberRoleE } from '@prisma/postgres';
import { SessionUpdateProfileTypeParamsI } from '@schemas/auth/session';
import SessionModule from '@modules/auth/session';
import { IdTypeI } from '@schemas/common/common';

export const EntityProfileModule = {
  fetchByEntity: async (
    filters: IdTypeI,
    select: Prisma.EntityProfileSelect = {
      id: true,
    },
  ): Promise<EntityProfile> => {
    const where: Prisma.EntityProfileWhereInput = {};
    if (filters.dataType === 'master') {
      where.entityId = filters.id;
    } else {
      where.entityRawDataId = filters.id;
    }
    const result = await prismaPG.entityProfile.findFirst({
      where,
      select,
    });
    if (!result) {
      throw new AppError('ENPR001');
    }
    return result;
  },
  updateBasicDetails: async (state: FastifyStateI, params: EntityAboutUpdateParamsI): Promise<void> => {
    const selfProfileId = state.profileId;
    const entityProfileId = state.entityProfileId;
    if (!selfProfileId) throw new AppError('AUTH020');

    const memberResult = await prismaPG.entityMember.findFirst({
      where: {
        profileId: selfProfileId,
        entityProfileId,
      },
      select: {
        role: true,
      },
    });

    if (!memberResult || memberResult.role === 'MEMBER') {
      throw new AppError('ENT007');
    }

    const entityProfileResult = await prismaPG.entityProfile.update({
      data: {
        description: params.description,
        overview: params.overview,
        foundedAt: params.foundedAt,
        website: params.website,
      },
      where: {
        id: entityProfileId,
      },
    });

    if (!entityProfileResult) throw new AppError('ENT019');
  },
  fetchTabs: async (params: EntityTabsFetchParamsI, txn: PostgresTxnI = prismaPG): Promise<EntityTabsClientI> => {
    const tab = await txn.entityTab.findFirst({
      where: {
        entityId: params.entityId,
        entityRawDataId: params.entityRawDataId,
      },
      select: {
        peopleTab: true,
        alumniTab: true,
        jobPostingTab: true,
      },
    });

    return (
      tab ?? {
        peopleTab: false,
        alumniTab: false,
        jobPostingTab: false,
      }
    );
  },
  updateTabs: async (
    state: FastifyStateI,
    params: EntityTabsUpdateParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<void> => {
    const selfProfileId = state.profileId;
    if (!selfProfileId) throw new AppError('AUTH020');

    const member = await txn.entityMember.findFirst({
      where: {
        profileId: selfProfileId,
        OR: [{ entityId: params.entityId }, { entityRawDataId: params.entityRawDataId }],
      },
      select: { role: true },
    });

    if (!member || !['ADMIN', 'MAINTAINER'].includes(member.role)) {
      throw new AppError('ENT007');
    }

    const existing = await txn.entityTab.findFirst({
      where: {
        entityId: params.entityId,
        entityRawDataId: params.entityRawDataId,
      },
      select: { id: true },
    });

    const data = {
      entityId: params.entityId,
      entityRawDataId: params.entityRawDataId,
      peopleTab: params.peopleTab,
      alumniTab: params.alumniTab,
      jobPostingTab: params.jobPostingTab,
    };

    if (existing) {
      await txn.entityTab.update({
        where: { id: existing.id },
        data,
      });
    } else {
      await txn.entityTab.create({
        data,
      });
    }
  },

  createOne: async (params: EntityCreateOneParamsI): Promise<{ id: string }> => {
    //max 5 entities for one admin
    await Promise.all(
      params.admins.map(async (adminId) => {
        const adminCount = await prismaPG.entityMember.count({
          where: {
            profileId: adminId,
            role: EntityMemberRoleE.ADMIN,
          },
        });
        if (adminCount > 5) {
          throw new AppError('ENT014');
        }
      }),
    );

    const entityProfileData = {
      type: params.type,
      profileId: params.profileId,
      name: params.name,
      website: params.website,
      email: params.email,
      ...(params.entityId && { entityId: params.entityId }),
      ...(params.entityRawDataId && { entityRawDataId: params.entityRawDataId }),
    };

    const requestStatus = params.emailType === 'PERSONAL' ? 'PENDING' : 'APPROVED';

    return await prismaPG.$transaction(async (txn) => {
      const entityProfile = await txn.entityProfile.create({
        data: entityProfileData,
        select: { id: true },
      });

      await Promise.all(
        params.admins.map((adminId) =>
          txn.entityRequest.create({
            data: {
              entityProfileId: entityProfile.id,
              ...(params.entityId && { entityId: params.entityId }),
              ...(params.entityRawDataId && { entityRawDataId: params.entityRawDataId }),
              profileId: adminId,
              status: requestStatus,
              ...(params.purpose &&
                requestStatus === 'PENDING' && {
                  purpose: params.purpose,
                }),
            },
          }),
        ),
      );

      if (requestStatus === 'APPROVED') {
        await Promise.all(
          params.admins.map((adminId) =>
            txn.entityMember.create({
              data: {
                entityProfileId: entityProfile.id,
                ...(params.entityId && { entityId: params.entityId }),
                ...(params.entityRawDataId && { entityRawDataId: params.entityRawDataId }),
                profileId: adminId,
                role: EntityMemberRoleE.ADMIN,
              },
            }),
          ),
        );
      }

      return entityProfile;
    });
  },

  sendOTPForEntityProfileEmailVerification: async ({ entityProfileId }: EntityProfileIdI): Promise<void> => {
    const entityProfileResult = await prismaPG.entityProfile.findUnique({
      select: {
        id: true,
        isVerified: true,
        emailVerificationCount: true,
        emailVerificationFirstAttemptAt: true,
        email: true,
        name: true,
        profileId: true,
      },
      where: {
        id: entityProfileId,
      },
    });
    if (!entityProfileResult) {
      throw new AppError('ENT015');
    }
    if (entityProfileResult.isVerified) throw new AppError('ENT016');

    const appConfigCommunicationResult: CommunicationConfigI = (await AppConfig.AppConfigModule.fetchByIdWithFallback(
      {
        module: 'COMMUNICATION',
        subModule: 'COMMUNICATION',
      },
      undefined,
      'web_app',
    )) as CommunicationConfigI;
    const limitConfig = appConfigCommunicationResult.verification.email.limit;
    const currentDate = getCurrentDate();

    const toUpdateEntityProfile: Prisma.EntityProfileUpdateInput = {};

    if (entityProfileResult.emailVerificationCount < limitConfig.count) {
      if (entityProfileResult.emailVerificationCount === 0) {
        toUpdateEntityProfile.emailVerificationFirstAttemptAt = currentDate;
      }
      toUpdateEntityProfile.emailVerificationCount = {
        increment: 1,
      };
    } else {
      const attemptExpiryDate = addMsToDate({
        date: new Date(toUpdateEntityProfile.emailVerificationFirstAttemptAt as string | Date),
        ms: appConfigCommunicationResult.verification.email.limit.duration,
      });
      if (currentDate < attemptExpiryDate) {
        throw new AppError('ENT017');
      } else {
        toUpdateEntityProfile.emailVerificationFirstAttemptAt = currentDate;
        toUpdateEntityProfile.emailVerificationCount = 0;
      }
    }

    try {
      await CommunicationModule.EmailModule.sendOne({
        email: entityProfileResult.email,
        name: entityProfileResult.name,
        profileId: entityProfileResult.profileId,
        type: 'EMAIL_ID_VERIFICATION',
      });
    } catch (_error) {
      throw new AppError('ENT018');
    }
    await prismaPG.entityProfile.update({
      data: toUpdateEntityProfile,
      select: { id: true },
      where: {
        id: entityProfileId,
      },
    });
    return;
  },

  verifyOTPForEntityProfileEmailVerification: async ({
    entityProfileId,
    otp,
  }: VerifyOTPForEntityProfileVerificationI): Promise<void> => {
    const entityProfileResult = await prismaPG.entityProfile.findUnique({
      select: {
        id: true,
        isVerified: true,
        profileId: true,
      },
      where: {
        id: entityProfileId,
      },
    });
    if (!entityProfileResult) {
      throw new AppError('ENT015');
    }

    if (entityProfileResult.isVerified) throw new AppError('ENT016');
    const _verifyResult = await CommunicationModule.EmailModule.verify({
      otp,
      profileId: entityProfileResult.profileId,
      type: 'EMAIL_ID_VERIFICATION',
    });
    await prismaPG.entityProfile.update({ data: { isVerified: true }, where: { id: entityProfileId } });
    return;
  },

  fetchEntityProfileAbout: async (
    state: FastifyStateI,
    { entityProfileId }: EntityProfileIdI,
  ): Promise<EntityProfileAboutForExternalClient> => {
    const selfProfileId = state.profileId;
    const profileType = state.profileType;
    const selfEntityProfileId = state.entityProfileId;

    if (!selfProfileId) {
      throw new AppError('PFL001');
    }

    if (profileType === 'ENTITY' && !selfEntityProfileId) throw new AppError('ENT015');

    const entityProfileResult = await prismaPG.entityProfile.findUnique({
      select: {
        id: true,
        name: true,
        avatar: true,
        description: true,
        overview: true,
        foundedAt: true,
        website: true,
        followersCount: true,
        followingsCount: true,
        type: true,
      },
      where: {
        id: entityProfileId,
      },
    });

    let isFollowing = false;

    if (!(profileType === 'ENTITY' && selfEntityProfileId === entityProfileId)) {
      if (profileType === 'ENTITY') {
        const followResult = await prismaPG.follow.findFirst({
          where: {
            followerEntityProfileId: selfEntityProfileId,
            followeeEntityProfileId: entityProfileId,
            followerProfileId: null,
            followeeProfileId: null,
          },
          select: {
            createdAt: true,
          },
        });
        if (followResult) {
          isFollowing = true;
        }
      } else {
        const followResult = await prismaPG.follow.findFirst({
          where: {
            followerProfileId: selfProfileId,
            followeeEntityProfileId: entityProfileId,
          },
          select: {
            createdAt: true,
          },
        });
        if (followResult) {
          isFollowing = true;
        }
      }
    }

    return entityProfileResult
      ? (() => {
          const { id, type, ...rest } = entityProfileResult;
          return {
            ...rest,
            entityProfileId: id,
            entityType: type,
            isFollowing,
          };
        })()
      : null;
  },

  updateProfileType: async (state: FastifyStateI, data: SessionUpdateProfileTypeParamsI): Promise<void> => {
    const sessionId = state.sessionId;
    await SessionModule.isActive(sessionId);
    const _sessionResult = await SessionModule.updateOne({ profileType: data.type }, { id: sessionId });
    return;
  },

  fetchEntityProfileBasicDetails: async ({ entityProfileId }: EntityProfileIdI): Promise<EntityProfileBasicDetails> => {
    const entityProfileResult = await prismaPG.entityProfile.findUnique({
      select: {
        id: true,
        description: true,
        overview: true,
        website: true,
        foundedAt: true,
      },
      where: {
        id: entityProfileId,
      },
    });

    return entityProfileResult;
  },

  fetchEntityProfilesForClient: async (state: FastifyStateI): Promise<EntityProfileForClient> => {
    const selfProfileId = state.profileId;
    const entityProfilesTemp = await prismaPG.entityProfile.findMany({
      select: {
        id: true,
        name: true,
        avatar: true,
        isVerified: true,
        entityId: true,
        entityRawDataId: true,
        EntityMember: {
          where: {
            profileId: selfProfileId,
          },
          select: {
            role: true,
          },
        },
      },
      where: {
        EntityMember: {
          some: {
            profileId: selfProfileId,
          },
        },
      },
    });

    const entityProfiles = entityProfilesTemp.map((entityProfile) => {
      return {
        id: entityProfile.id,
        name: entityProfile.name,
        avatar: entityProfile.avatar,
        isVerified: entityProfile.isVerified,
        entity: {
          id: entityProfile.entityId ? entityProfile.entityId : entityProfile.entityRawDataId,
          dataType: entityProfile.entityId ? 'master' : 'raw',
        },
        role: entityProfile.EntityMember[0].role,
      };
    });

    return entityProfiles;
  },
};
