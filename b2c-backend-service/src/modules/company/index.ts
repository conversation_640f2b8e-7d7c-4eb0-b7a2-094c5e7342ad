import { EntityModule } from './entity';
import { DegreeModule } from './degree';
import { DepartmentModule } from './department';
import { DesignationModule } from './designation';
import { SkillModule } from './skill';
import { CertificateCourseModule } from './certificateCourse';
import { EntityMemberModule } from './entityMember';
import { EntityProfileModule } from './entityProfile';
import EntityRequestModule from './entityRequest';
import { PeopleModule } from './people';
import { JobModule } from './job/job';
import { EntityBenefitModule } from './entityBenefit';
import { ApplicationModule } from './job/application';
import EntityNetworkModule from './entityNetwork';

const Company = {
  ApplicationModule,
  CertificateCourseModule,
  DegreeModule,
  DepartmentModule,
  DesignationModule,
  EntityBenefitModule,
  EntityModule,
  EntityMemberModule,
  EntityProfileModule,
  JobModule,
  EntityRequestModule,
  PeopleModule,
  SkillModule,
  EntityNetworkModule,
};

export default Company;
