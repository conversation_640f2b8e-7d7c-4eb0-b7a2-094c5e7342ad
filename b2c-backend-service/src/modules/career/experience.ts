import type {
  ExperienceCargoI,
  ExperienceDesignationI,
  ExperienceEquipmentCategoryI,
  ExperienceFetchForClientParamsI,
  ExperienceFuelTypeI,
  ExperienceItemI,
  ExperienceModuleCreateOneParamsI,
  ExperienceOneForExternalClientParamsI,
  ExperienceShipI,
} from '@schemas/career/experience';
import { prismaPG } from '@config/db';
import type {
  ExperienceDesignationClientI,
  ExperienceFetchForClientDataResultI,
  ExperienceFetchForClientRawResultI,
  ExperienceFetchForClientResultI,
  ExperienceFetchOneForExternalClientResultI,
  ExperienceShipClientI,
} from '@interfaces/career/experience';
import { isFilled } from '@utils/data/object';
import { ShipNestedClientI } from '@interfaces/ship/ship';
import type { FastifyStateI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import Company from '@modules/company';
import type { UUIDI } from '@schemas/common/common';
import { Prisma } from '@prisma/postgres';
import type {
  Experience,
  ExperienceCargo,
  ExperienceDesignation,
  ExperienceEquipmentCategory,
  ExperienceFuelType,
  ExperienceShip,
} from '@prisma/postgres';
import Ship from '@modules/ship';

import { PortVisitModule } from './portVisit';
import { SubVesselTypeNestedClientI } from '@interfaces/ship/subVesselType';
import { PostgresTxnI } from '@interfaces/common/db';
import { SkillModule } from './skill';
import { getDocument, updateDocument } from '@utils/search/elasticsearch';
import { getMonthYearDifference } from '@utils/data/date';

export const ExperienceModule = {
  fetchForClient: async ({
    profileId,
    page,
    pageSize,
  }: ExperienceFetchForClientParamsI): Promise<ExperienceFetchForClientResultI> => {
    const [experienceResult, totalResult, portVisitResult] = await Promise.all([
      prismaPG.$queryRaw<ExperienceFetchForClientRawResultI[]>`
    SELECT *
    FROM (
      SELECT
        x."id" AS "id",
        x."entityId" AS "entityId",
        e."name" AS "entityName",
        x."entityRawDataId" AS "entityRawDataId",
        er."name" AS "entityRawDataName",
        x."years",
        x."months",
        (
          SELECT
          json_agg(
            json_build_object(
              'experienceDesignationId', xd."id",
              'fromDate', xd."fromDate",
              'toDate', xd."toDate",
              'designationAlternativeId', xd."designationAlternativeId",
              'designationRawDataId', xd."designationRawDataId",
              'designationName', da."name",
              'designationRawDataName', drw."name",
              'ships',
              (
                SELECT json_agg(
                  json_build_object(
                    'id', xs."id",
                    'fromDate', xs."fromDate",
                    'toDate', xs."toDate",
                    'shipImo', s."imo",
                    'shipRawDataImo', srw."imo",
                    'name', xs."name",
                    'subVesselTypeId', vs."id",
                    'subVesselTypeRawDataId', vsrw."id",
                    'subVesselTypeName', vs."name",
                    'subVesselTypeRawDataName', vsrw."name"
                  )
                  ORDER BY xs."toDate" DESC NULLS FIRST, xs."fromDate" DESC
                )
                FROM
                "career"."ExperienceShip" xs
                LEFT JOIN
                  "ship"."Ship" s
                  ON s."imo" = xs."shipImo"
                LEFT JOIN
                  "rawData"."ShipRawData" srw
                  ON srw."imo" = xs."shipRawDataImo"
                LEFT JOIN
                  "ship"."SubVesselType" vs
                  ON vs."id" = xs."subVesselTypeId"
                LEFT JOIN
                  "rawData"."SubVesselTypeRawData" vsrw
                  ON vsrw."id" = xs."subVesselTypeRawDataId"
                WHERE
                xs."experienceDesignationId" = xd."id"
              )
            )
            ORDER BY xd."toDate" DESC NULLS FIRST, xd."fromDate" DESC
          )
          FROM
            "career"."ExperienceDesignation" xd
          LEFT JOIN
            "company"."DesignationAlternative" da
            ON da."id" = xd."designationAlternativeId"
          LEFT JOIN
            "rawData"."DesignationRawData" drw
            ON drw."id" = xd."designationRawDataId"
          WHERE
            xd."experienceId" = x."id"
        ) AS "designations"
        FROM
          "career"."Experience" x
        INNER JOIN "user"."Profile" u
          ON x."profileId" = u."id"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN
          "company"."Entity" e
          ON e."id" = x."entityId"
        LEFT JOIN
          "rawData"."EntityRawData" er
          ON er."id" = x."entityRawDataId"
        WHERE
          x."profileId" = ${profileId}::uuid
        ) AS subQuery
        ORDER BY
        CASE
          WHEN json_array_length(subQuery."designations") = 0 THEN 2
          WHEN (subQuery."designations"->0->>'toDate') IS NULL THEN 0
          ELSE 1
        END,
        CASE
          WHEN json_array_length(subQuery."designations") = 0 THEN '1900-01-01'::timestamp
          WHEN (subQuery."designations"->0->>'toDate') IS NULL THEN (subQuery."designations"->0->>'fromDate')::timestamp
          ELSE (subQuery."designations"->0->>'toDate')::timestamp
        END DESC,
        CASE
          WHEN json_array_length(subQuery."designations") = 0 THEN '1900-01-01'::timestamp
          ELSE (subQuery."designations"->0->>'fromDate')::timestamp
        END DESC
        OFFSET ${page * pageSize}
        LIMIT ${pageSize}
    `,
      prismaPG.experience.count({
        where: {
          profileId,
          Profile: {
            status: {
              equals: 'ACTIVE',
            },
          },
        },
      }),
      page === 0 ? PortVisitModule.fetch({ profileId, page: 0, pageSize: 5 }) : null,
    ]);

    const experienceClientResult: ExperienceFetchForClientDataResultI[] = experienceResult.map(
      (experienceItem) =>
        ({
          id: experienceItem.id,
          entity: isFilled(experienceItem.entityId)
            ? {
                id: experienceItem.entityId,
                name: experienceItem.entityName,
                dataType: 'master',
              }
            : { id: experienceItem.entityRawDataId, name: experienceItem.entityRawDataName, dataType: 'raw' },
          months: experienceItem.months,
          years: experienceItem.years,
          designations: experienceItem?.designations?.map(
            (xDesignationItem) =>
              ({
                experienceDesignationId: xDesignationItem.experienceDesignationId,
                fromDate: xDesignationItem?.fromDate,
                toDate: xDesignationItem?.toDate,
                designation: xDesignationItem?.designationAlternativeId
                  ? {
                      id: xDesignationItem.designationAlternativeId,
                      name: xDesignationItem.designationName,
                      dataType: 'master',
                    }
                  : {
                      id: xDesignationItem.designationRawDataId,
                      name: xDesignationItem.designationRawDataName,
                      dataType: 'raw',
                    },
                ships:
                  xDesignationItem?.ships?.map((shipItem) => {
                    const subVesselType: SubVesselTypeNestedClientI = shipItem?.subVesselTypeId
                      ? { id: shipItem?.subVesselTypeId, name: shipItem?.subVesselTypeName, dataType: 'master' }
                      : {
                          id: shipItem?.subVesselTypeRawDataId,
                          name: shipItem?.subVesselTypeRawDataName,
                          dataType: 'raw',
                        };

                    const ship: ShipNestedClientI = shipItem?.shipImo
                      ? ({
                          imo: shipItem.shipImo,
                          name: shipItem.name,
                          dataType: 'master',
                        } as ShipNestedClientI)
                      : ({
                          imo: shipItem.shipRawDataImo,
                          name: shipItem.name,
                          dataType: 'raw',
                        } as ShipNestedClientI);

                    return {
                      id: shipItem.id,
                      name: shipItem.name,
                      ship,
                      subVesselType,
                      fromDate: shipItem.fromDate,
                      toDate: shipItem.toDate,
                    } as ExperienceShipClientI;
                  }) ?? [],
              }) as ExperienceDesignationClientI,
          ),
        }) as ExperienceFetchForClientDataResultI,
    );

    return { data: experienceClientResult, total: totalResult, portVisits: portVisitResult };
  },
  fetchOneForExternalClient: async (
    params: ExperienceOneForExternalClientParamsI,
  ): Promise<ExperienceFetchOneForExternalClientResultI> => {
    const experienceDesignationResult = await prismaPG.experienceDesignation.findMany({
      select: {
        id: true,
        fromDate: true,
        toDate: true,
        createdAt: true,
        Experience: {
          select: {
            Entity: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            EntityRawData: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
        DesignationAlternative: {
          select: {
            id: true,
            name: true,
          },
        },
        DesignationRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        ExperienceShip: {
          select: {
            id: true,
            Ship: {
              select: {
                imo: true,
                name: true,
              },
            },
            ShipRawData: {
              select: {
                imo: true,
                name: true,
              },
            },
            SubVesselType: {
              select: {
                id: true,
                name: true,
              },
            },
            SubVesselTypeRawData: {
              select: {
                id: true,
                name: true,
              },
            },
            fromDate: true,
            toDate: true,
            name: true,
          },
          orderBy: [
            {
              toDate: {
                sort: 'desc',
                nulls: 'first',
              },
            },
            {
              fromDate: 'desc',
            },
            {
              createdAt: 'desc',
            },
          ],
        },
      },
      where: {
        experienceId: params.id,
      },
      orderBy: [
        {
          toDate: {
            nulls: 'first',
            sort: 'desc',
          },
        },
        {
          fromDate: 'desc',
        },
        {
          createdAt: 'desc',
        },
      ],
    });
    const experienceFetchOneForExternalClientResultResult: ExperienceFetchOneForExternalClientResultI = {
      entity: isFilled(experienceDesignationResult?.[0]?.Experience?.Entity)
        ? { ...experienceDesignationResult?.[0]?.Experience?.Entity, dataType: 'master' }
        : { ...experienceDesignationResult?.[0]?.Experience?.EntityRawData, dataType: 'raw' },
      designations: experienceDesignationResult?.map(
        (xDesignationItem) =>
          ({
            id: xDesignationItem.id,
            fromDate: xDesignationItem?.fromDate,
            toDate: xDesignationItem?.toDate,
            designation: isFilled(xDesignationItem?.DesignationAlternative)
              ? { ...xDesignationItem?.DesignationAlternative, dataType: 'master' }
              : { ...xDesignationItem?.DesignationRawData, dataType: 'raw' },
            ships:
              xDesignationItem?.ExperienceShip?.map((shipItem) => {
                const subVesselType: SubVesselTypeNestedClientI = isFilled(shipItem?.SubVesselType)
                  ? { ...shipItem?.SubVesselType, dataType: 'master' }
                  : { ...shipItem?.SubVesselTypeRawData, dataType: 'raw' };
                const ship: ShipNestedClientI = (
                  isFilled(shipItem?.Ship)
                    ? { ...shipItem?.Ship, dataType: 'master' }
                    : { ...shipItem?.ShipRawData, dataType: 'raw' }
                ) as ShipNestedClientI;
                return {
                  id: shipItem.id,
                  name: shipItem.name,
                  ship,
                  subVesselType,
                  fromDate: shipItem.fromDate,
                  toDate: shipItem.toDate,
                } as ExperienceShipClientI;
              }) ?? [],
          }) as ExperienceDesignationClientI,
      ),
    };
    return experienceFetchOneForExternalClientResultResult;
  },
  crudFuelTypeOne: async (
    experienceEquipmentCategoryId: UUIDI,
    experienceFuelTypeP: ExperienceFuelTypeI,
    txn: PostgresTxnI,
  ): Promise<Pick<ExperienceFuelType, 'id'>> => {
    let experienceFuelTypeResult: Pick<ExperienceFuelType, 'id'>;

    if (experienceFuelTypeP.opr === 'CREATE') {
      const dataInput: Prisma.ExperienceFuelTypeUncheckedCreateInput = {
        experienceEquipmentCategoryId,
      };

      if (experienceFuelTypeP?.fuelType?.dataType === 'master') {
        dataInput.fuelTypeId = experienceFuelTypeP.fuelType.id;
      } else {
        dataInput.fuelTypeRawDataId = experienceFuelTypeP.fuelType.id;
      }

      experienceFuelTypeResult = await txn.experienceFuelType.create({
        data: dataInput,
        select: { id: true },
      });
    } else {
      const experienceFuelType = await txn.experienceFuelType.findFirst({
        select: { id: true },
        where: {
          OR: [{ fuelTypeId: experienceFuelTypeP.id }, { fuelTypeRawDataId: experienceFuelTypeP.id }],
          experienceEquipmentCategoryId,
        },
      });

      if (!experienceFuelType) {
        throw new AppError('EXP001');
      }

      if (experienceFuelTypeP.opr === 'DELETE') {
        experienceFuelTypeResult = await txn.experienceFuelType.delete({
          select: { id: true },
          where: { id: experienceFuelType.id },
        });
      } else if (experienceFuelTypeP.opr === 'UPDATE') {
        const updateInput: Prisma.ExperienceFuelTypeUncheckedUpdateInput = {
          fuelTypeId: null,
          fuelTypeRawDataId: null,
        };

        if (experienceFuelTypeP?.fuelType?.dataType === 'master') {
          updateInput.fuelTypeId = experienceFuelTypeP.fuelType.id;
        } else {
          updateInput.fuelTypeRawDataId = experienceFuelTypeP.fuelType.id;
        }

        experienceFuelTypeResult = await txn.experienceFuelType.update({
          select: { id: true },
          where: { id: experienceFuelType.id },
          data: updateInput,
        });
      } else {
        experienceFuelTypeResult = experienceFuelType;
      }
    }

    return experienceFuelTypeResult;
  },
  crudEquipmentCategoryOne: async (
    state: FastifyStateI,
    experienceShipId: UUIDI,
    experienceEquipmentCategoryP: ExperienceEquipmentCategoryI,
    txn: PostgresTxnI,
  ): Promise<Pick<ExperienceEquipmentCategory, 'id'>> => {
    const selfProfileId = state.profileId;
    let experienceEquipmentCategoryResult: Pick<ExperienceEquipmentCategory, 'id'>;
    if (experienceEquipmentCategoryP.opr === 'CREATE') {
      const dataInput: Prisma.ExperienceEquipmentCategoryUncheckedCreateInput = {
        experienceShipId,
        manufacturerName: experienceEquipmentCategoryP.manufacturerName,
        model: experienceEquipmentCategoryP.model,
        powerCapacity: experienceEquipmentCategoryP.powerCapacity,
        profileId: selfProfileId,
      };
      if (experienceEquipmentCategoryP?.equipmentCategory?.dataType === 'master') {
        dataInput.equipmentCategoryId = experienceEquipmentCategoryP.equipmentCategory.id;
      } else {
        dataInput.equipmentCategoryRawDataId = experienceEquipmentCategoryP.equipmentCategory.id;
      }
      if (experienceEquipmentCategoryP?.details) {
        dataInput.details = experienceEquipmentCategoryP.details;
      }
      experienceEquipmentCategoryResult = await txn.experienceEquipmentCategory.create({
        data: dataInput,
        select: { id: true },
      });
    } else {
      const experienceEquipmentCategoryFoundResult = await txn.experienceEquipmentCategory.findUnique({
        select: { id: true },
        where: {
          id: experienceEquipmentCategoryP.id,
        },
      });
      if (!experienceEquipmentCategoryFoundResult) {
        throw new AppError('EXP001');
      }
      experienceEquipmentCategoryResult = experienceEquipmentCategoryFoundResult;
      if (experienceEquipmentCategoryP.opr === 'DELETE') {
        experienceEquipmentCategoryResult = await txn.experienceEquipmentCategory.delete({
          select: { id: true },
          where: {
            id: experienceEquipmentCategoryP.id,
          },
        });
        return experienceEquipmentCategoryResult;
      } else if (experienceEquipmentCategoryP.opr === 'UPDATE') {
        const dataInput: Prisma.ExperienceEquipmentCategoryUncheckedUpdateInput = {};

        if (experienceEquipmentCategoryP?.manufacturerName) {
          dataInput.manufacturerName = experienceEquipmentCategoryP.manufacturerName;
        }
        if (experienceEquipmentCategoryP?.model) {
          dataInput.model = experienceEquipmentCategoryP.model;
        }
        if (experienceEquipmentCategoryP?.powerCapacity) {
          dataInput.powerCapacity = experienceEquipmentCategoryP.powerCapacity;
        }
        if (experienceEquipmentCategoryP?.details) {
          dataInput.details = experienceEquipmentCategoryP.details;
        }
        if (isFilled(experienceEquipmentCategoryP?.equipmentCategory)) {
          await Ship.EquipmentCategoryModule.fetchById(experienceEquipmentCategoryP.equipmentCategory, txn);
          if (experienceEquipmentCategoryP.equipmentCategory.dataType === 'master') {
            dataInput.equipmentCategoryId = experienceEquipmentCategoryP.equipmentCategory.id;
          } else {
            dataInput.equipmentCategoryRawDataId = experienceEquipmentCategoryP.equipmentCategory.id;
          }
        }
        experienceEquipmentCategoryResult = await txn.experienceEquipmentCategory.update({
          data: dataInput,
          select: { id: true },
          where: {
            id: experienceEquipmentCategoryP.id,
          },
        });
      }
    }
    if (experienceEquipmentCategoryP?.fuelTypes?.length) {
      await Promise.all(
        experienceEquipmentCategoryP.fuelTypes.map((fuelTypeItem) =>
          ExperienceModule.crudFuelTypeOne(experienceEquipmentCategoryResult.id, fuelTypeItem, txn),
        ),
      );
    }
    return experienceEquipmentCategoryResult;
  },
  crudCargoOne: async (
    experienceShipId: UUIDI,
    experienceCargoP: ExperienceCargoI,
    txn: PostgresTxnI,
  ): Promise<Pick<ExperienceCargo, 'id'>> => {
    let experienceCargoResult: Pick<ExperienceEquipmentCategory, 'id'>;
    if (experienceCargoP.opr === 'CREATE') {
      const dataInput: Prisma.ExperienceCargoUncheckedCreateInput = {
        experienceShipId,
        name: experienceCargoP.name,
        description: experienceCargoP.description,
        fromDate: experienceCargoP.fromDate,
      };
      if (experienceCargoP?.toDate !== undefined) {
        dataInput.toDate = experienceCargoP.toDate;
      }
      experienceCargoResult = await txn.experienceCargo.create({
        data: dataInput,
        select: { id: true },
      });
    } else {
      const experienceCargoFoundResult = await txn.experienceCargo.findUnique({
        select: { id: true, fromDate: true, toDate: true },
        where: {
          id: experienceCargoP.id,
        },
      });
      if (!experienceCargoFoundResult) {
        throw new AppError('EXP001');
      }
      experienceCargoResult = experienceCargoFoundResult;
      if (experienceCargoP.opr === 'DELETE') {
        experienceCargoResult = await txn.experienceCargo.delete({
          select: { id: true },
          where: {
            id: experienceCargoP.id,
          },
        });
        return experienceCargoResult;
      } else if (experienceCargoP.opr === 'UPDATE') {
        const dataInput: Prisma.ExperienceCargoUncheckedUpdateInput = {};
        if (
          experienceCargoP?.fromDate &&
          experienceCargoP?.toDate &&
          experienceCargoP?.fromDate > experienceCargoP?.toDate
        ) {
          throw new AppError('EXP007');
        } else if (
          experienceCargoP?.fromDate &&
          experienceCargoFoundResult?.toDate &&
          experienceCargoP?.fromDate > experienceCargoFoundResult?.toDate
        ) {
          throw new AppError('EXP007');
        } else if (experienceCargoP?.toDate && experienceCargoFoundResult?.fromDate > experienceCargoP?.toDate) {
          throw new AppError('EXP007');
        }
        if (experienceCargoP?.name) {
          dataInput.name = experienceCargoP.name;
        }
        if (experienceCargoP?.description) {
          dataInput.description = experienceCargoP.description;
        }
        if (experienceCargoP?.fromDate) {
          dataInput.fromDate = experienceCargoP.fromDate;
        }
        if (experienceCargoP?.toDate !== undefined) {
          dataInput.toDate = experienceCargoP.toDate;
        }
        experienceCargoResult = await txn.experienceCargo.update({
          data: dataInput,
          select: { id: true },
          where: {
            id: experienceCargoP.id,
          },
        });
      }
    }
    return experienceCargoResult;
  },
  crudShipOne: async (
    state: FastifyStateI,
    experienceDesignationId: UUIDI,
    experienceShipP: ExperienceShipI,
    txn: PostgresTxnI,
  ): Promise<Pick<ExperienceShip, 'id'>> => {
    let experienceShipResult: Pick<ExperienceShip, 'id'>;
    if (experienceShipP.opr === 'CREATE') {
      await Promise.all([
        Ship.CoreShipModule.fetchByImo(experienceShipP.ship, txn),
        Ship.SubVesselTypeModule.fetchById(experienceShipP.subVesselType, txn),
      ]);
      const dataInput: Prisma.ExperienceShipUncheckedCreateInput = {
        experienceDesignationId,
        profileId: state.profileId,
        fromDate: experienceShipP.fromDate,
        name: experienceShipP.name,
        sizeGt: experienceShipP.sizeGt,
        details: experienceShipP?.details,
      };

      if (experienceShipP?.powerKw !== undefined && experienceShipP?.powerKw !== null) {
        dataInput.powerKw = experienceShipP.powerKw;
      }

      if (experienceShipP?.dwt !== undefined && experienceShipP?.dwt !== null) {
        dataInput.dwt = experienceShipP.dwt;
      }

      if (experienceShipP?.ship?.dataType === 'master') {
        dataInput.shipImo = experienceShipP.ship.imo;
      } else {
        dataInput.shipRawDataImo = experienceShipP.ship.imo;
      }
      if (experienceShipP?.subVesselType?.dataType === 'master') {
        dataInput.subVesselTypeId = experienceShipP.subVesselType.id;
      } else {
        dataInput.subVesselTypeRawDataId = experienceShipP.subVesselType.id;
      }
      if (experienceShipP?.toDate !== undefined) {
        dataInput.toDate = experienceShipP.toDate;
      }
      if (experienceShipP.department?.dataType === 'master') {
        dataInput.departmentAlternativeId = experienceShipP.department.id;
      } else {
        dataInput.departmentRawDataId = experienceShipP.department.id;
      }
      experienceShipResult = await txn.experienceShip.create({
        data: dataInput,
        select: { id: true },
      });
    } else {
      const experienceShipFoundResult = await txn.experienceShip.findUnique({
        select: { id: true, fromDate: true, toDate: true, subVesselTypeId: true, subVesselTypeRawDataId: true },
        where: {
          id: experienceShipP.id,
        },
      });
      if (!experienceShipFoundResult) {
        throw new AppError('EXP001');
      }
      experienceShipResult = experienceShipFoundResult;

      if (experienceShipP.opr === 'DELETE') {
        await txn.profileSkillExperienceShip.deleteMany({
          where: {
            experienceShipId: experienceShipP.id,
          },
        });
        experienceShipResult = await txn.experienceShip.delete({
          select: { id: true },
          where: {
            id: experienceShipP.id,
          },
        });
        return experienceShipResult;
      } else if (experienceShipP.opr === 'UPDATE') {
        const dataInput: Prisma.ExperienceShipUncheckedUpdateInput = {};
        if (experienceShipP?.fromDate && experienceShipP?.toDate) {
          if (experienceShipP?.fromDate > experienceShipP?.toDate) {
            throw new AppError('EXP007');
          }
        } else if (experienceShipP?.fromDate && experienceShipFoundResult?.toDate) {
          if (experienceShipP?.fromDate > experienceShipFoundResult?.toDate) {
            throw new AppError('EXP007');
          }
        } else if (experienceShipP?.toDate) {
          if (experienceShipFoundResult?.fromDate > experienceShipP?.toDate) {
            throw new AppError('EXP007');
          }
        }
        if (experienceShipP?.name) {
          dataInput.name = experienceShipP.name;
        }
        if (experienceShipP?.details !== undefined) {
          dataInput.details = experienceShipP.details;
        }
        if (experienceShipP?.sizeGt) {
          dataInput.sizeGt = experienceShipP.sizeGt;
        }
        if (experienceShipP?.powerKw) {
          dataInput.powerKw = experienceShipP.powerKw;
        }
        if (experienceShipP?.fromDate) {
          dataInput.fromDate = experienceShipP.fromDate;
        }
        if (experienceShipP?.toDate !== undefined) {
          dataInput.toDate = experienceShipP.toDate;
        }
        if (experienceShipP?.dwt) {
          dataInput.dwt = experienceShipP.dwt;
        }
        if (isFilled(experienceShipP?.department)) {
          await Company.DesignationModule.fetchById(experienceShipP.department, txn);
          if (experienceShipP.department.dataType === 'master') {
            dataInput.departmentAlternativeId = experienceShipP.department.id;
          } else {
            dataInput.departmentRawDataId = experienceShipP.department.id;
          }
        }

        if (isFilled(experienceShipP?.subVesselType)) {
          await Ship.MainVesselTypeModule.fetchOne(experienceShipP.subVesselType, txn);
          if (experienceShipP.subVesselType.dataType === 'master') {
            dataInput.subVesselTypeId = experienceShipP.subVesselType.id;
          } else {
            dataInput.subVesselTypeRawDataId = experienceShipP.subVesselType.id;
          }
        }
        experienceShipResult = await txn.experienceShip.update({
          data: dataInput,
          select: { id: true },
          where: {
            id: experienceShipP.id,
          },
        });
      }
    }
    const crudOperations = [];

    if (experienceShipP?.cargos?.length) {
      crudOperations.push(
        ...experienceShipP.cargos.map((experienceCargoItem) =>
          ExperienceModule.crudCargoOne(experienceShipResult.id, experienceCargoItem, txn),
        ),
      );
    }

    if (experienceShipP?.equipmentCategories?.length) {
      crudOperations.push(
        ...experienceShipP.equipmentCategories.map((experienceEquipmentCategoryItem) =>
          ExperienceModule.crudEquipmentCategoryOne(
            state,
            experienceShipResult.id,
            experienceEquipmentCategoryItem,
            txn,
          ),
        ),
      );
    }

    if (experienceShipP?.skills?.length) {
      crudOperations.push(
        SkillModule.crudExperienceShipSkills(state, txn, {
          experienceShipId: experienceShipResult.id,
          skills: experienceShipP.skills,
        }),
      );
    }

    if (crudOperations.length > 0) {
      await Promise.all(crudOperations);
    }

    return experienceShipResult;
  },
  crudDesignationOne: async (
    state: FastifyStateI,
    experienceId: UUIDI,
    experienceDesignationP: ExperienceDesignationI,
    txn: PostgresTxnI,
  ): Promise<Pick<ExperienceDesignation, 'id'>> => {
    let experienceDesignationResult: Pick<ExperienceDesignation, 'id'>;
    if (experienceDesignationP.opr === 'CREATE') {
      await Company.DesignationModule.fetchById(experienceDesignationP.designation, txn);
      const dataInput: Prisma.ExperienceDesignationUncheckedCreateInput = {
        experienceId,
        profileId: state.profileId,
        fromDate: experienceDesignationP.fromDate,
      };
      if (experienceDesignationP?.toDate !== undefined) {
        dataInput.toDate = experienceDesignationP.toDate;
      }
      if (experienceDesignationP.designation?.dataType === 'master') {
        dataInput.designationAlternativeId = experienceDesignationP.designation.id;
      } else {
        dataInput.designationRawDataId = experienceDesignationP.designation.id;
      }
      experienceDesignationResult = await txn.experienceDesignation.create({
        data: dataInput,
        select: { id: true },
      });
    } else {
      const experienceDesignationFoundResult = await txn.experienceDesignation.findUnique({
        select: { id: true, fromDate: true, toDate: true },
        where: {
          id: experienceDesignationP.id,
        },
      });
      if (!experienceDesignationFoundResult) {
        throw new AppError('EXP001');
      }
      experienceDesignationResult = experienceDesignationFoundResult;
      if (experienceDesignationP.opr === 'DELETE') {
        const experienceShipFoundResult = await txn.experienceShip.findMany({
          select: { id: true },
          where: {
            experienceDesignationId: experienceDesignationFoundResult.id,
          },
        });

        if (experienceShipFoundResult.length > 0) {
          const shipIds = experienceShipFoundResult.map((ship) => ship.id);

          await txn.profileSkillExperienceShip.deleteMany({
            where: {
              experienceShipId: { in: shipIds },
            },
          });

          await txn.experienceShip.deleteMany({
            where: {
              id: { in: shipIds },
            },
          });
        }

        experienceDesignationResult = await txn.experienceDesignation.delete({
          select: { id: true },
          where: {
            id: experienceDesignationP.id,
          },
        });
        return experienceDesignationResult;
      } else if (experienceDesignationP.opr === 'UPDATE') {
        const dataInput: Prisma.ExperienceDesignationUncheckedUpdateInput = {};
        if (experienceDesignationP?.fromDate && experienceDesignationP?.toDate) {
          if (experienceDesignationP?.fromDate > experienceDesignationP?.toDate) {
            throw new AppError('EXP007');
          }
        } else if (experienceDesignationP?.fromDate && experienceDesignationFoundResult?.toDate) {
          if (experienceDesignationP?.fromDate > experienceDesignationFoundResult?.toDate) {
            throw new AppError('EXP007');
          }
        } else if (experienceDesignationP?.toDate) {
          if (experienceDesignationFoundResult?.fromDate > experienceDesignationP?.toDate) {
            throw new AppError('EXP007');
          }
        }
        if (experienceDesignationP?.fromDate) {
          dataInput.fromDate = experienceDesignationP.fromDate;
        }
        if (experienceDesignationP?.toDate !== undefined) {
          dataInput.toDate = experienceDesignationP.toDate;
        }
        if (isFilled(experienceDesignationP?.designation)) {
          await Company.DesignationModule.fetchById(experienceDesignationP.designation, txn);
          if (experienceDesignationP.designation.dataType === 'master') {
            dataInput.designationAlternativeId = experienceDesignationP.designation.id;
          } else {
            dataInput.designationRawDataId = experienceDesignationP.designation.id;
          }
        }
        experienceDesignationResult = await txn.experienceDesignation.update({
          data: dataInput,
          select: { id: true },
          where: {
            id: experienceDesignationP.id,
          },
        });
      }
    }
    if (experienceDesignationP?.ships?.length) {
      await Promise.all(
        experienceDesignationP.ships.map((experienceShipItem) =>
          ExperienceModule.crudShipOne(state, experienceDesignationResult.id, experienceShipItem, txn),
        ),
      );

      const allShips = await txn.experienceShip.findMany({
        where: { experienceDesignationId: experienceDesignationResult.id },
        select: { fromDate: true, toDate: true },
        orderBy: { fromDate: 'asc' },
      });

      const mergedPeriods: { fromDate: Date; toDate: Date | null }[] = [];

      for (const ship of allShips) {
        const currentFrom = new Date(ship.fromDate);
        const currentTo = ship.toDate ? new Date(ship.toDate) : new Date();

        if (mergedPeriods.length === 0) {
          mergedPeriods.push({ fromDate: currentFrom, toDate: currentTo });
          continue;
        }

        const lastPeriod = mergedPeriods[mergedPeriods.length - 1];
        const lastTo = lastPeriod.toDate ? new Date(lastPeriod.toDate) : new Date();

        if (currentFrom <= lastTo) {
          if (currentTo > lastTo) {
            lastPeriod.toDate = currentTo;
          }
        } else {
          mergedPeriods.push({ fromDate: currentFrom, toDate: currentTo });
        }
      }

      let totalMonths = 0;

      for (const period of mergedPeriods) {
        const { years, months } = getMonthYearDifference(period.fromDate, period.toDate);
        totalMonths += years * 12 + months;
      }

      const finalYears = Math.floor(totalMonths / 12);
      const finalMonths = totalMonths % 12;

      await txn.experience.update({
        data: {
          sailingYears: finalYears,
          sailingMonths: finalMonths,
        },
        where: {
          id: experienceId,
        },
      });
    }
    return experienceDesignationResult;
  },
  crudOne: async (state: FastifyStateI, experienceP: ExperienceItemI, txn: PostgresTxnI): Promise<string> => {
    let experienceResult: Pick<Experience, 'id'>;
    if (experienceP.opr === 'CREATE') {
      await Company.EntityModule.fetchById(experienceP.entity);
      const dataInput: Prisma.ExperienceUncheckedCreateInput = {
        profileId: state.profileId,
      };
      if (experienceP.entity.dataType === 'master') {
        dataInput.entityId = experienceP.entity.id;
      } else {
        dataInput.entityRawDataId = experienceP.entity.id;
      }
      experienceResult = await txn.experience.create({ data: dataInput, select: { id: true } });
    } else {
      experienceResult = await txn.experience.findUnique({
        select: { id: true },
        where: {
          profileId: state.profileId,
          id: experienceP.id,
        },
      });

      if (!experienceResult) {
        throw new AppError('EXP001');
      }
      if (experienceP.opr === 'DELETE') {
        const experienceShips = await txn.experienceShip.findMany({
          select: { id: true },
          where: {
            ExperienceDesignation: {
              experienceId: experienceP.id,
            },
          },
        });

        if (experienceShips.length > 0) {
          const shipIds = experienceShips.map((ship) => ship.id);
          await txn.profileSkillExperienceShip.deleteMany({
            where: {
              experienceShipId: { in: shipIds },
            },
          });
        }

        experienceResult = await txn.experience.delete({
          select: { id: true },
          where: {
            id: experienceP.id,
          },
        });

        try {
          const existingProfile = await getDocument('profiles', state.profileId);
          const existingExperiences = Array.isArray(existingProfile.experiences) ? existingProfile.experiences : [];
          const filteredExperiences = existingExperiences.filter((exp: { id: string }) => exp.id !== experienceP.id);

          await updateDocument('profiles', state.profileId, {
            experiences: filteredExperiences,
          });
        } catch (esError) {
          console.warn('Elasticsearch sync failed:', esError);
        }

        return experienceResult?.id;
      } else if (experienceP.opr === 'UPDATE') {
        await Company.EntityModule.fetchById(experienceP.entity, txn);
        experienceResult = await txn.experience.update({
          data:
            experienceP.entity?.dataType === 'master'
              ? { entityId: experienceP.entity.id }
              : { entityRawDataId: experienceP.entity.id },
          select: { id: true },
          where: {
            id: experienceP.id,
          },
        });
      }
    }
    if (experienceP?.designations?.length) {
      await Promise.all(
        experienceP.designations.map((experienceDesignationItem) =>
          ExperienceModule.crudDesignationOne(state, experienceResult.id, experienceDesignationItem, txn),
        ),
      );
      const allDesignations = await txn.experienceDesignation.findMany({
        where: { experienceId: experienceResult.id },
        select: { fromDate: true, toDate: true },
      });

      let totalMonths = 0;

      for (const designation of allDesignations) {
        const { years, months } = getMonthYearDifference(designation.fromDate, designation.toDate);
        totalMonths += years * 12 + months;
      }

      const finalYears = Math.floor(totalMonths / 12);
      const finalMonths = totalMonths % 12;

      await txn.experience.update({
        data: {
          years: finalYears,
          months: finalMonths,
        },
        where: {
          id: experienceResult.id,
        },
      });
    }

    if (experienceP.opr === 'CREATE' || experienceP.opr === 'UPDATE') {
      try {
        const allExperiences = await txn.experience.findMany({
          where: { profileId: state.profileId },
          include: {
            Entity: true,
            EntityRawData: true,
            ExperienceDesignation: {
              include: {
                DesignationAlternative: true,
                DesignationRawData: true,
                ExperienceShip: {
                  include: {
                    ExperienceEquipmentCategory: {
                      include: {
                        ExperienceFuelType: true,
                      },
                    },
                    ExperienceCargo: true,
                    ProfileSkillExperienceShip: {
                      include: {
                        Skill: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        const experiencesData = allExperiences.map((experience) => ({
          id: experience.id,
          entityId: experience.entityId || experience.entityRawDataId,
          entityName: experience.Entity?.name || experience.EntityRawData?.name || null,
          designations:
            experience.ExperienceDesignation?.map((designation) => ({
              id: designation.id,
              designationId: designation.designationAlternativeId || designation.designationRawDataId,
              designationName: designation.DesignationAlternative?.name || designation.DesignationRawData?.name || null,
              fromDate: designation.fromDate,
              toDate: designation.toDate,
              ships:
                designation.ExperienceShip?.map((ship) => ({
                  id: ship.id,
                  name: ship.name,
                  imo: ship.shipImo || ship.shipRawDataImo,
                  fromDate: ship.fromDate,
                  toDate: ship.toDate,
                  sizeGt: ship.sizeGt,
                  powerKw: ship.powerKw,
                  dwt: ship.dwt,
                  details: ship.details,
                  subVesselTypeId: ship.subVesselTypeId || ship.subVesselTypeRawDataId,
                  departmentId: ship.departmentAlternativeId || ship.departmentRawDataId,
                  equipmentCategories:
                    ship.ExperienceEquipmentCategory?.map((equipment) => ({
                      id: equipment.id,
                      equipmentCategoryId: equipment.equipmentCategoryId || equipment.equipmentCategoryRawDataId,
                      manufacturerName: equipment.manufacturerName,
                      model: equipment.model,
                      powerCapacity: equipment.powerCapacity,
                      details: equipment.details,
                      fuelTypes:
                        equipment.ExperienceFuelType?.map((fuel) => ({
                          id: fuel.id,
                          fuelTypeId: fuel.fuelTypeId || fuel.fuelTypeRawDataId,
                        })) || [],
                    })) || [],
                  cargos:
                    ship.ExperienceCargo?.map((cargo) => ({
                      id: cargo.id,
                      name: cargo.name,
                      description: cargo.description,
                      fromDate: cargo.fromDate,
                      toDate: cargo.toDate,
                    })) || [],
                  skills:
                    ship.ProfileSkillExperienceShip?.map((skill) => ({
                      skillId: skill.skillId,
                      skillName: skill.Skill?.name || null,
                    })) || [],
                })) || [],
            })) || [],
        }));

        await updateDocument('profiles', state.profileId, {
          experiences: experiencesData,
        });
      } catch (esError) {
        console.warn('Elasticsearch sync failed:', esError);
      }
    }

    return experienceResult?.id || experienceP?.id;
  },
  crudMultiple: async (state: FastifyStateI, params: ExperienceModuleCreateOneParamsI): Promise<string[]> => {
    const experienceResult = await prismaPG.$transaction(
      async (txn: PostgresTxnI) => await Promise.all(params.map((item) => ExperienceModule.crudOne(state, item, txn))),
      {
        timeout: 60000,
        maxWait: 10000,
      },
    );
    const experienceIdsResult: string[] = [];
    const errors = [];
    experienceResult?.forEach((resultItem) => {
      experienceIdsResult.push(resultItem);
    });
    if (errors?.length) {
      if (errors.length === experienceResult.length) {
        throw new AppError('EXP010', { errors });
      } else {
        throw new AppError('EXP003', { errors });
      }
    }
    return experienceIdsResult;
  },
};
