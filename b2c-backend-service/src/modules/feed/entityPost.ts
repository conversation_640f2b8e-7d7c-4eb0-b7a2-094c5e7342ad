import { prismaPG } from '@config/db';
import { Prisma } from '@prisma/postgres';
import { EntityProfilePostFetchManyI } from '@schemas/feed/entityPost';
import { errorHandler } from '@utils/errors/handler';
import { EntityProfileExternalI } from '@interfaces/company/entityProfile';
import { EntityProfilePostDataClientI, EntityProfilePostFetchManyResultI } from '@interfaces/feed/entityPost';

const EntityPostModule = {
  fetchEntityProfilePosts: async ({ entityProfileId, pagination }: EntityProfilePostFetchManyI) => {
    try {
      const entityProfilePostFetchManyResult: EntityProfilePostFetchManyResultI = {
        posts: [],
      };
      let cursorId: number | null = null;
      if (typeof pagination.cursorId === 'number' && pagination.cursorId > 0) {
        cursorId = Number(pagination.cursorId);
      }
      const entityProfilePostResult: EntityProfilePostDataClientI[] = await prismaPG.$queryRaw`
          SELECT
            p."id" AS "id",
            p."cursorId" AS "cursorId",
            p."caption" AS "caption",
            CASE
              WHEN length(p."caption") > 100 THEN LEFT(p."caption", 100) || '...'
              ELSE p."caption"
            END AS "captionTruncated",
            length(p."caption") > 100 AS "isCaptionTruncated",
            p."reactionsCount" AS "reactionsCount",
            p."totalCommentsCount" AS "totalCommentsCount",
            EXISTS (
              SELECT 1 FROM "feed"."PostReaction" pr
              WHERE pr."postId" = p."id"
                AND pr."entityProfileId" = ${entityProfileId}::uuid
            ) AS "isLiked",
            p."createdAt" AS "createdAt",
            COALESCE(
              (
                SELECT json_agg(
                  json_build_object(
                    'caption', m."caption",
                    'fileUrl', m."fileUrl"
                  )
                )
                FROM "feed"."PostMedia" m
                WHERE m."postId" = p."id"
              ), '[]'::json
            ) AS "Media",
            json_build_object(
              'id', epr."id",
              'name', epr."name",
              'avatar', epr."avatar"
            ) AS "EntityProfile"
          FROM "feed"."Post" p
          INNER JOIN "company"."EntityProfile" epr
            ON p."entityProfileId" = epr."id"
            AND epr."status" = 'ACTIVE'
          WHERE p."status" = 'ACTIVE'
            AND p."entityProfileId" = ${entityProfileId}::uuid
            ${cursorId ? Prisma.sql`AND p."cursorId" < ${cursorId}` : Prisma.empty}
          ORDER BY p."createdAt" DESC
          LIMIT ${pagination.pageSize}
        `;

      if (entityProfilePostResult?.length) {
        entityProfilePostFetchManyResult.posts.push(
          ...entityProfilePostResult.map((item) => ({
            ...item,
            cursorId: Number(item.cursorId),
            EntityProfile: {
              id: item.EntityProfile.id,
              name: item.EntityProfile.name,
              avatar: item.EntityProfile.avatar,
            } as EntityProfileExternalI,
          })),
        );
        entityProfilePostFetchManyResult.cursorId = Number(
          entityProfilePostResult?.[entityProfilePostResult.length - 1].cursorId,
        );
      }
      return entityProfilePostFetchManyResult;
    } catch (error) {
      errorHandler(error);
    }
  },
};

export default EntityPostModule;
