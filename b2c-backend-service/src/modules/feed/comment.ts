import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { NotificationProfileTypeI } from '@consts/communication/notification';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { CommentCreateOneResultI, CommentFetchManyResultI } from '@interfaces/feed/comment';
import CommunicationModule from '@modules/communication';
import { Prisma } from '@prisma/postgres';
import type { CommentCreateOneI, CommentFetchManyI, CommentFetchRepliesI } from '@schemas/feed/comment';
import { omit } from '@utils/data/object';
import { errorHandler } from '@utils/errors/handler';

const CommentModule = {
  fetchMany: async (
    state: FastifyStateI,
    { postId, cursorId, pageSize }: CommentFetchManyI,
  ): Promise<CommentFetchManyResultI> => {
    const { profileId: selfProfileId } = state;
    const filters: Prisma.PostCommentWhereInput = {
      postId,
      parentCommentId: null,
      Profile: {
        status: 'ACTIVE',
      },
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };
    if (typeof cursorId === 'number' && cursorId > 0) {
      filters.cursorId = {
        lt: BigInt(cursorId),
      };
    }

    const [commentsCountResult, commentTempResult] = await Promise.all([
      prismaPG.postComment.count({
        where: omit(filters, ['cursorId']),
      }),
      prismaPG.postComment.findMany({
        select: {
          id: true,
          text: true,
          cursorId: true,
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
              status: true,
            },
          },
          EntityProfile: {
            select: {
              id: true,
              name: true,
              avatar: true,
              status: true,
            },
          },
          repliesCount: true,
          createdAt: true,
          updatedAt: true,
          Replies: {
            select: {
              id: true,
              text: true,
              cursorId: true,
              Profile: {
                select: {
                  id: true,
                  avatar: true,
                  name: true,
                  designationText: true,
                  designationAlternativeId: true,
                  designationRawDataId: true,
                  entityText: true,
                  entityId: true,
                  entityRawDataId: true,
                },
              },
              EntityProfile: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
              repliesCount: true,
              createdAt: true,
              updatedAt: true,
            },
            where: {
              NOT: {
                Profile: {
                  OR: [
                    {
                      BlockedByProfile: {
                        some: {
                          blockerId: selfProfileId,
                        },
                      },
                    },
                    {
                      BlockedProfile: {
                        some: {
                          blockedId: selfProfileId,
                        },
                      },
                    },
                  ],
                },
              },
            },
            take: 2,
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
        where: filters,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    const transformProfile = (profile) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const transformComment = (item) => ({
      id: item.id,
      text: item.text,
      cursorId: Number(item.cursorId.toString()),
      repliesCount: item.repliesCount,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      Profile: transformProfile(item.Profile),
      EntityProfile: {
        id: item.EntityProfile?.id,
        name: item.EntityProfile?.name,
        avatar: item.EntityProfile?.avatar,
      },
    });

    const commentResult = commentTempResult.map((item) => ({
      ...transformComment(item),
      replies: (item.Replies || []).map((reply) => ({
        ...transformComment(reply),
        replies: [],
      })),
    }));

    const lastComment = commentResult[commentResult.length - 1];
    const resultCursorId =
      commentResult.length > 0 && lastComment ? lastComment.cursorId : typeof cursorId === 'number' ? cursorId : null;

    return {
      comments: commentResult,
      total: commentsCountResult,
      cursorId: resultCursorId,
    };
  },
  fetchReplies: async (
    state: FastifyStateI,
    { postId, parentCommentId, cursorId, pageSize }: CommentFetchRepliesI,
  ): Promise<CommentFetchManyResultI> => {
    try {
      const { profileId: selfProfileId } = state;
      const filters: Prisma.PostCommentWhereInput = {
        postId,
        parentCommentId,
        OR: [
          {
            entityProfileId: null,
            Profile: {
              status: 'ACTIVE',
            },
          },
          {
            entityProfileId: { not: null },
            EntityProfile: {
              status: 'ACTIVE',
            },
          },
        ],
        NOT: {
          Profile: {
            OR: [
              {
                BlockedByProfile: {
                  some: {
                    blockerId: selfProfileId,
                  },
                },
              },
              {
                BlockedProfile: {
                  some: {
                    blockedId: selfProfileId,
                  },
                },
              },
            ],
          },
        },
      };

      if (typeof cursorId === 'number' && cursorId > 0) {
        filters.cursorId = {
          lt: BigInt(cursorId),
        };
      }

      const [commentsCountResult, commentTempResult] = await Promise.all([
        prismaPG.postComment.count({
          where: omit(filters, ['cursorId']),
        }),
        prismaPG.postComment.findMany({
          select: {
            id: true,
            text: true,
            cursorId: true,
            Profile: {
              select: {
                id: true,
                avatar: true,
                name: true,
                designationText: true,
                designationAlternativeId: true,
                designationRawDataId: true,
                entityText: true,
                entityId: true,
                entityRawDataId: true,
              },
            },
            EntityProfile: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
            repliesCount: true,
            createdAt: true,
            updatedAt: true,
          },
          where: filters,
          take: pageSize,
          orderBy: {
            createdAt: 'desc',
          },
        }),
      ]);

      const transformProfile = (profile) => ({
        id: profile.id,
        name: profile.name,
        avatar: profile.avatar,
        designation: profile.designationAlternativeId
          ? {
              id: profile.designationAlternativeId,
              name: profile.designationText || '',
              dataType: 'master' as const,
            }
          : profile.designationRawDataId
            ? {
                id: profile.designationRawDataId,
                name: profile.designationText || '',
                dataType: 'raw' as const,
              }
            : null,
        entity: profile.entityId
          ? {
              id: profile.entityId,
              name: profile.entityText || '',
              dataType: 'master' as const,
            }
          : profile.entityRawDataId
            ? {
                id: profile.entityRawDataId,
                name: profile.entityText || '',
                dataType: 'raw' as const,
              }
            : null,
      });

      const transformComment = (item) => ({
        id: item.id,
        text: item.text,
        cursorId: Number(item.cursorId.toString()),
        repliesCount: item.repliesCount,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        Profile: transformProfile(item.Profile),
        EntityProfile: {
          id: item.EntityProfile?.id,
          name: item.EntityProfile?.name,
          avatar: item.EntityProfile?.avatar,
        },
      });

      const commentResult = commentTempResult.map((item) => ({
        ...transformComment(item),
        replies: [],
      }));

      const lastComment = commentResult[commentResult.length - 1];
      const resultCursorId =
        commentResult.length > 0 && lastComment ? lastComment.cursorId : typeof cursorId === 'number' ? cursorId : null;

      return {
        comments: commentResult,
        total: commentsCountResult,
        cursorId: resultCursorId,
      };
    } catch (error) {
      errorHandler(error);
    }
  },
  createOne: async (
    state: FastifyStateI,
    { parentCommentId, postId, text }: CommentCreateOneI,
  ): Promise<CommentCreateOneResultI> => {
    const { profileId: selfProfileId, profileType, entityProfileId: selfEntityProfileId } = state;

    const [postResult, parentCommentResult] = await Promise.all([
      prismaPG.post.findUnique({
        where: {
          id: postId,
          status: 'ACTIVE',
          OR: [
            {
              entityProfileId: null,
              Profile: {
                status: 'ACTIVE',
              },
            },
            {
              entityProfileId: { not: null },
              EntityProfile: {
                status: 'ACTIVE',
              },
            },
          ],
        },
        select: { id: true, profileId: true, entityProfileId: true, caption: true },
      }),
      parentCommentId
        ? prismaPG.postComment.findUnique({
            where: {
              id: parentCommentId,
              postId,
              OR: [
                {
                  entityProfileId: null,
                  Profile: {
                    status: 'ACTIVE',
                  },
                },
                {
                  entityProfileId: { not: null },
                  EntityProfile: {
                    status: 'ACTIVE',
                  },
                },
              ],
            },
            select: { id: true, parentCommentId: true, repliesCount: true },
          })
        : null,
    ]);

    if (!postResult) {
      throw new AppError('POST001');
    }
    if (parentCommentId && !parentCommentResult) {
      throw new AppError('PCMT001');
    }
    if (parentCommentId && parentCommentResult?.parentCommentId) {
      throw new AppError('PCMT011');
    }

    const toUpdatePost: Prisma.PostUncheckedUpdateInput = {
      totalCommentsCount: { increment: 1 },
    };

    const commentInput: Prisma.PostCommentUncheckedCreateInput = {
      text,
      postId,
    };
    if (profileType === 'ENTITY') {
      commentInput.entityProfileId = selfEntityProfileId;
    } else {
      commentInput.profileId = selfProfileId;
    }
    if (parentCommentResult) {
      commentInput.parentCommentId = parentCommentResult.id;
    } else {
      commentInput.repliesCount = 0;
      toUpdatePost.parentCommentsCount = { increment: 1 };
    }

    const [commentResult, _postResult, _commentResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.postComment.create({
            select: { id: true, cursorId: true },
            data: commentInput,
          }),
          txn.post.update({
            data: toUpdatePost,
            where: { id: postId },
            select: { id: true },
          }),
          parentCommentResult
            ? txn.postComment.update({
                data: { repliesCount: { increment: 1 } },
                where: { id: parentCommentResult.id },
                select: { id: true },
              })
            : null,
        ]),
    );
    const receiverProfileType: NotificationProfileTypeI = postResult.entityProfileId ? 'ENTITY' : 'USER';
    if (
      (profileType === 'ENTITY' && postResult.entityProfileId && selfEntityProfileId !== postResult.entityProfileId) ||
      (profileType === 'USER' && postResult.profileId && selfProfileId !== postResult.profileId)
    ) {
      await CommunicationModule.NotificationModule.createOne({
        actorProfileId: profileType === 'ENTITY' ? selfEntityProfileId : selfProfileId,
        actorProfileType: profileType,
        postId,
        commentId: commentResult.id,
        postText: postResult.caption.length > 20 ? `${postResult.caption.slice(0, 20)}...` : postResult.caption,
        ...(parentCommentId ? { parentCommentId } : {}),
        ...(receiverProfileType === 'ENTITY'
          ? {
              receiverEntityProfileId: postResult?.entityProfileId,
            }
          : {
              receiverProfileId: postResult.profileId,
            }),
        receiverProfileType,
        topic: 'communication_topic',
        type: parentCommentId ? 'REPLY' : 'COMMENT',
      });
    }

    return {
      id: commentResult.id,
      cursorId: Number(commentResult.cursorId.toString()),
    };
  },
  deleteOne: async (state: FastifyStateI, filters: Pick<Prisma.PostCommentWhereUniqueInput, 'id'>): Promise<void> => {
    try {
      const { profileType, entityProfileId: selfEntityProfileId } = state;
      const isSelfEntityProfile = profileType === 'ENTITY';
      let commentResult;
      if (isSelfEntityProfile) {
        commentResult = await prismaPG.postComment.findFirst({
          select: {
            id: true,
            parentCommentId: true,
            repliesCount: true,
            postId: true,
          },
          where: {
            entityProfileId: selfEntityProfileId,
            id: filters.id,
          },
        });
      } else {
        commentResult = await prismaPG.postComment.findFirst({
          select: {
            id: true,
            parentCommentId: true,
            repliesCount: true,
            postId: true,
          },
          where: {
            entityProfileId: selfEntityProfileId,
            id: filters.id,
          },
        });
      }
      const [_commentResult, _parentCommentResult, _postResult] = await prismaPG.$transaction(async (txn) => {
        const commentTemp = await txn.postComment.delete({
          where: {
            id: commentResult.id,
          },
          select: {
            id: true,
          },
        });
        const parentCommentTemp = await (commentResult?.parentCommentId
          ? txn.postComment.update({
              data: {
                repliesCount: {
                  decrement: 1,
                },
              },
              where: {
                id: commentResult.parentCommentId,
              },
              select: {
                id: true,
              },
            })
          : prismaPG.$queryRaw`SELECT NULL`);

        const toUpdatePost: Prisma.PostUncheckedUpdateInput = {
          totalCommentsCount: {
            decrement: parentCommentTemp?.[0]?.['?column?'] === null ? commentResult.repliesCount + 1 : 1,
          },
        };
        const postTemp = await txn.post.update({
          data: toUpdatePost,
          where: {
            id: commentResult.postId,
          },
          select: {
            id: true,
          },
        });
        return [commentTemp, parentCommentTemp, postTemp];
      });
      return;
    } catch (error) {
      errorHandler(error, { RECORD_NOT_FOUND: 'PCMT001', GENERIC: 'PCMT007' });
    }
  },
};

export default CommentModule;
