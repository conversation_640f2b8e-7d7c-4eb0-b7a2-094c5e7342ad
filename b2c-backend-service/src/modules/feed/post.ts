import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { PostDataClientI, PostExternalClientI, PostFetchManyResultI } from '@interfaces/feed/post';
import { ProfileExternalI } from '@interfaces/user/profile';
import { PostStatusE, Prisma } from '@prisma/postgres';
import { RouteParamsI } from '@schemas/common/common';
import type { PostCreateOneI, PostFetchManyI, PostSearchSchemaI, PostUpdateOneI } from '@schemas/feed/post';
import { omit } from '@utils/data/object';
import { errorHandler } from '@utils/errors/handler';
import ServiceModule from '@modules/storage';
import RewardModule from '@modules/reward';
import { REWARD_TYPE_IDS } from '@consts/reward/reward';
import { insertDocument, searchByText, updateDocument, deleteDocument, createIndex } from '@utils/search/elasticsearch';

const PostModule = {
  createOne: async (state: FastifyStateI, { files, caption }: PostCreateOneI) => {
    try {
      const profileId = state.profileId;
      const profileType = state.profileType;
      const entityProfileId = state.entityProfileId;

      if (profileType === 'ENTITY') {
        const entityMemberResult = await prismaPG.entityMember.findFirst({
          where: {
            profileId,
            entityProfileId,
          },
          select: {
            role: true,
          },
        });

        if (!entityMemberResult || entityMemberResult.role === 'MEMBER') {
          throw new AppError('POST005');
        }
      }

      const postResult = await prismaPG.post.create({
        data: {
          caption,
          profileId,
          ...(profileType === 'ENTITY' && { entityProfileId }),
          Media: files?.length
            ? {
                createMany: {
                  data: files.map((fileItem) => ({
                    caption: fileItem?.caption,
                    profileId,
                    ...(profileType === 'ENTITY' && { entityProfileId }),
                    fileUrl: fileItem.fileUrl,
                    fileExtension: fileItem.extension,
                  })),
                },
              }
            : undefined,
        },
        include: { Media: true },
      });

      if (!postResult) {
        throw new AppError('POST002');
      }

      try {
        await RewardModule.RewardActionModule.assignReward({
          profileId,
          rewardId: REWARD_TYPE_IDS.POST_CONTRIBUTION,
        });
      } catch (rewardError) {
        console.error('Failed to assign reward for post creation:', rewardError);
      }

      try {
        await createIndex('posts');
        await insertDocument('posts', {
          id: postResult.id,
          caption: postResult.caption,
          profileId: postResult.profileId,
          reactionsCount: postResult.reactionsCount,
          totalCommentsCount: postResult.totalCommentsCount,
          createdAt: postResult.createdAt,
          status: postResult.status,
          media:
            postResult.Media?.map((m) => ({
              caption: m.caption,
              fileUrl: m.fileUrl,
              fileExtension: m.fileExtension,
            })) || [],
        });
      } catch (esError) {
        console.error('Failed to index post in Elasticsearch:', esError);
      }

      const postForClientResult = { ...omit(postResult, ['cursorId']), cursorId: Number(postResult['cursorId']) };
      return postForClientResult;
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchManySearchGlobal: async (state: FastifyStateI, body: PostSearchSchemaI) => {
    try {
      const selfProfileId = state.profileId;
      const { search, page, pageSize } = body;

      try {
        const esResults = await searchByText('posts', search, ['caption', 'media.caption'], pageSize, page * pageSize);

        if (Array.isArray(esResults) && esResults.length > 0) {
          const postIds = esResults.map((r) => r.id);

          const searchPostsResult: PostDataClientI[] = await prismaPG.$queryRaw`
            SELECT
            p."id" AS "id",
            p."cursorId" AS "cursorId",
            p."caption" AS "caption",
            CASE
              WHEN LENGTH(p."caption") > 250 THEN SUBSTRING(p."caption", 1, 250)
              ELSE p."caption"
            END AS "shortCaption",
            (LENGTH(p."caption") > 250) AS "isCaptionTruncated",
            p."reactionsCount" AS "reactionsCount",
            p."totalCommentsCount" AS "totalCommentsCount",
            EXISTS (
              SELECT 1 FROM "feed"."PostReaction" pr
              WHERE pr."postId" = p."id"
              AND pr."profileId" = ${selfProfileId}::uuid
            ) AS "isLiked",
            EXISTS (
              SELECT 1 FROM "feed"."SavedPost" sp
              WHERE sp."postId" = p."id"
              AND sp."profileId" = ${selfProfileId}::uuid
            ) AS "isSaved",
            p."createdAt" AS "createdAt",
            (
              SELECT json_agg(
                json_build_object(
                  'caption', m."caption",
                  'fileUrl', m."fileUrl",
                  'extension', m."fileExtension"
                )
              )
              FROM "feed"."PostMedia" m
              WHERE m."postId" = p."id"
            ) AS "Media",
            json_build_object(
              'id', u."id",
              'name', u."name",
              'avatar', u."avatar",
              'designationText', u."designationText",
              'designationAlternativeId', u."designationAlternativeId",
              'designationRawDataId', u."designationRawDataId",
              'entityText', u."entityText",
              'entityId', u."entityId",
              'entityRawDataId', u."entityRawDataId"
            ) AS "Profile"
            FROM "feed"."Post" p
            INNER JOIN "user"."Profile" u
              ON p."profileId" = u."id"
              AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
            LEFT JOIN "network"."BlockedProfile" b1
              ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
            LEFT JOIN "network"."BlockedProfile" b2
              ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
            WHERE p."id" = ANY(${postIds}::uuid[])
            AND p."status" = 'ACTIVE'
            AND b1."blockerId" IS NULL
            AND b2."blockerId" IS NULL
            ORDER BY array_position(${postIds}::uuid[], p."id")
          `;

          const data = searchPostsResult?.length
            ? searchPostsResult.map((item) => ({
                ...item,
                caption: item.shortCaption,
                isCaptionTruncated: item.isCaptionTruncated,
                cursorId: Number(item.cursorId),
                Profile: {
                  id: item.Profile.id,
                  name: item.Profile.name,
                  avatar: item.Profile.avatar,
                  designation: item.Profile?.designationAlternativeId
                    ? {
                        id: item.Profile.designationRawDataId,
                        name: item.Profile.designationText,
                        dataType: 'raw',
                      }
                    : null,
                  entity: item.Profile?.entityId
                    ? {
                        id: item.Profile.entityId,
                        name: item.Profile.entityText,
                        dataType: 'master',
                      }
                    : item.Profile?.entityRawDataId
                      ? {
                          id: item.Profile.entityRawDataId,
                          name: item.Profile.entityText,
                          dataType: 'raw',
                        }
                      : null,
                } as ProfileExternalI,
              }))
            : [];

          return {
            data,
            total: esResults.length,
          };
        }
      } catch (esError) {
        console.error('Elasticsearch search failed, falling back to SQL:', esError);
      }

      const offset = page * pageSize;
      const searchTerm = `%${search.toLowerCase()}%`;

      const countResult = await prismaPG.$queryRaw<[{ count: bigint }]>`
        SELECT COUNT(DISTINCT p."id") as count
        FROM "feed"."Post" p
        INNER JOIN "user"."Profile" u
        ON p."profileId" = u."id"
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN "feed"."PostMedia" pm ON p."id" = pm."postId"
        LEFT JOIN "network"."BlockedProfile" b1
          ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
          ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
        WHERE p."status" = 'ACTIVE'
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
        AND (
          LOWER(p."caption") LIKE ${searchTerm}
          OR LOWER(pm."caption") LIKE ${searchTerm}
          OR LOWER(u."name") LIKE ${searchTerm}
          OR LOWER(u."designationText") LIKE ${searchTerm}
          OR LOWER(u."entityText") LIKE ${searchTerm}
        )
      `;

      const total = Number(countResult[0]?.count || 0);

      const searchPostsResult: PostDataClientI[] = await prismaPG.$queryRaw`
        WITH ranked_posts AS (
          SELECT DISTINCT p."id",
          CASE
            WHEN LOWER(p."caption") LIKE ${searchTerm} THEN 1
            WHEN LOWER(u."name") LIKE ${searchTerm} THEN 2
            WHEN LOWER(u."designationText") LIKE ${searchTerm} THEN 3
            WHEN LOWER(u."entityText") LIKE ${searchTerm} THEN 4
            WHEN LOWER(pm."caption") LIKE ${searchTerm} THEN 5
            ELSE 6
          END AS relevance_score,
          p."createdAt"
          FROM "feed"."Post" p
          INNER JOIN "user"."Profile" u
            ON p."profileId" = u."id"
            AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
          LEFT JOIN "feed"."PostMedia" pm ON p."id" = pm."postId"
          LEFT JOIN "network"."BlockedProfile" b1
            ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
          LEFT JOIN "network"."BlockedProfile" b2
            ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
          WHERE p."status" = 'ACTIVE'
          AND b1."blockerId" IS NULL
          AND b2."blockerId" IS NULL
          AND (
            LOWER(p."caption") LIKE ${searchTerm}
            OR LOWER(pm."caption") LIKE ${searchTerm}
            OR LOWER(u."name") LIKE ${searchTerm}
            OR LOWER(u."designationText") LIKE ${searchTerm}
            OR LOWER(u."entityText") LIKE ${searchTerm}
          )
          ORDER BY relevance_score, p."createdAt" DESC
          LIMIT ${pageSize}
          OFFSET ${offset}
        )
        SELECT
        p."id" AS "id",
        p."cursorId" AS "cursorId",
        p."caption" AS "caption",
        CASE
          WHEN LENGTH(p."caption") > 250 THEN SUBSTRING(p."caption", 1, 250)
          ELSE p."caption"
        END AS "shortCaption",
        (LENGTH(p."caption") > 250) AS "isCaptionTruncated",
        p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" pr
          WHERE pr."postId" = p."id"
          AND pr."profileId" = ${selfProfileId}::uuid
        ) AS "isLiked",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl",
              'extension', m."fileExtension"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
        FROM ranked_posts rp
        INNER JOIN "feed"."Post" p ON rp."id" = p."id"
        INNER JOIN "user"."Profile" u
          ON p."profileId" = u."id"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        ORDER BY rp.relevance_score, rp."createdAt" DESC
      `;

      const data = searchPostsResult?.length
        ? searchPostsResult.map((item) => ({
            ...item,
            caption: item.shortCaption,
            isCaptionTruncated: item.isCaptionTruncated,
            cursorId: Number(item.cursorId),
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile?.designationAlternativeId
                ? {
                    id: item.Profile.designationRawDataId,
                    name: item.Profile.designationText,
                    dataType: 'raw',
                  }
                : null,
              entity: item.Profile?.entityId
                ? {
                    id: item.Profile.entityId,
                    name: item.Profile.entityText,
                    dataType: 'master',
                  }
                : item.Profile?.entityRawDataId
                  ? {
                      id: item.Profile.entityRawDataId,
                      name: item.Profile.entityText,
                      dataType: 'raw',
                    }
                  : null,
            } as ProfileExternalI,
          }))
        : [];

      return {
        data,
        total,
      };
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchPopularPosts: async (state: FastifyStateI) => {
    try {
      const selfProfileId = state.profileId;

      const popularPosts: PostDataClientI[] = await prismaPG.$queryRaw`
      SELECT
        p."id" AS "id",
        p."cursorId" AS "cursorId",
        p."caption" AS "caption",
        CASE
          WHEN LENGTH(p."caption") > 250 THEN SUBSTRING(p."caption", 1, 250)
          ELSE p."caption"
        END AS "shortCaption",
        (LENGTH(p."caption") > 250) AS "isCaptionTruncated",
        p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" pr
          WHERE pr."postId" = p."id"
          AND pr."profileId" = ${selfProfileId}::uuid
        ) AS "isLiked",
        EXISTS (
          SELECT 1 FROM "feed"."SavedPost" sp
          WHERE sp."postId" = p."id"
          AND sp."profileId" = ${selfProfileId}::uuid
        ) AS "isSaved",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl",
              'extension', m."fileExtension"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
      FROM "feed"."Post" p
      INNER JOIN "user"."Profile" u ON p."profileId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b1 ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2 ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE p."status" = 'ACTIVE'
      AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      AND b1."blockerId" IS NULL
      AND b2."blockerId" IS NULL
      ORDER BY p."reactionsCount" DESC, p."totalCommentsCount" DESC, p."createdAt" DESC
      LIMIT 5
    `;

      const data = popularPosts.map((item) => ({
        ...item,
        caption: item.shortCaption,
        isCaptionTruncated: item.isCaptionTruncated,
        cursorId: Number(item.cursorId),
        Profile: {
          id: item.Profile.id,
          name: item.Profile.name,
          avatar: item.Profile.avatar,
          designation: item.Profile?.designationAlternativeId
            ? {
                id: item.Profile.designationAlternativeId,
                name: item.Profile.designationText,
                dataType: 'master',
              }
            : item.Profile?.designationRawDataId
              ? {
                  id: item.Profile.designationRawDataId,
                  name: item.Profile.designationText,
                  dataType: 'raw',
                }
              : null,
          entity: item.Profile?.entityId
            ? {
                id: item.Profile.entityId,
                name: item.Profile.entityText,
                dataType: 'master',
              }
            : item.Profile?.entityRawDataId
              ? {
                  id: item.Profile.entityRawDataId,
                  name: item.Profile.entityText,
                  dataType: 'raw',
                }
              : null,
        } as ProfileExternalI,
      }));

      return {
        data,
        total: data.length,
      };
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchMany: async (state: FastifyStateI, { pagination }: PostFetchManyI): Promise<PostFetchManyResultI> => {
    const { entityProfileId: selfEntityProfileId, profileType, profileId: selfProfileId } = state;
    const isSelfEntityProfile = profileType === 'ENTITY';
    try {
      const postFetchManyResult: PostFetchManyResultI = {
        posts: [],
      };
      let cursorId: number | null = null;

      if (typeof pagination.cursorId === 'number' && pagination.cursorId > 0) {
        cursorId = Number(pagination.cursorId);
      }

      const postsResult: PostDataClientI[] = await prismaPG.$queryRaw`
        SELECT p."id" AS "id",
        p."caption" AS "caption",
        CASE
          WHEN length(p."caption") > 100 THEN LEFT(p."caption", 100) || '...'
          ELSE p."caption"
        END AS "caption",
        length(p."caption") > 100 AS "isCaptionTruncated",
        p."cursorId" AS "cursorId",
        p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" r
          WHERE r."postId" = p."id"
          AND (
            (${isSelfEntityProfile} AND r."entityProfileId" = ${selfEntityProfileId}::uuid AND r."profileId" = ${selfProfileId}::uuid)
            OR
            (NOT ${isSelfEntityProfile} AND r."profileId" = ${selfProfileId}::uuid AND r."entityProfileId" IS NULL)
          )
        ) AS "isLiked",
        EXISTS (
          SELECT 1 FROM "feed"."SavedPost" sp
          WHERE sp."postId" = p."id"
          AND sp."profileId" = ${selfProfileId}::uuid
        ) AS "isSaved",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile",
        json_build_object(
          'id', ep."id",
          'name', ep."name",
          'avatar', ep."avatar"
        ) AS "EntityProfile"
        FROM "feed"."Post" p
        INNER JOIN "user"."Profile" u
        ON p."profileId" = u."id"
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN "company"."EntityProfile" ep
        ON p."entityProfileId" = ep."id"
        AND ep."status" = 'ACTIVE'::"user"."ProfileStatusE"
        LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
        WHERE p."status" = 'ACTIVE'
        ${cursorId ? Prisma.sql`AND p."cursorId" < ${cursorId}` : Prisma.empty}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
        ORDER BY p."createdAt" DESC
        LIMIT ${pagination.pageSize}
      `;

      if (postsResult?.length) {
        postFetchManyResult.cursorId = Number(postsResult[postsResult.length - 1].cursorId);
        postFetchManyResult.posts.push(
          ...postsResult.map((item) => ({
            ...item,
            cursorId: Number(item.cursorId),
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile?.designationAlternativeId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText,
                    dataType: 'master',
                  }
                : item.Profile?.designationRawDataId
                  ? {
                      id: item.Profile.designationRawDataId,
                      name: item.Profile.designationText,
                      dataType: 'raw',
                    }
                  : null,
              entity: item.Profile?.entityId
                ? {
                    id: item.Profile.entityId,
                    name: item.Profile.entityText,
                    dataType: 'master',
                  }
                : item.Profile?.entityRawDataId
                  ? {
                      id: item.Profile.entityRawDataId,
                      name: item.Profile.entityText,
                      dataType: 'raw',
                    }
                  : null,
            } as ProfileExternalI,
            EntityProfile: {
              id: item.EntityProfile.id,
              name: item.EntityProfile.name,
              avatar: item.EntityProfile.avatar,
            },
          })),
        );
      }
      return postFetchManyResult;
    } catch (error) {
      errorHandler(error);
    }
  },

  deleteOne: async (state: FastifyStateI, filtersP: RouteParamsI): Promise<void> => {
    try {
      const existingPostResult = await prismaPG.$queryRaw<
        Array<{
          id: string;
          profileId: string;
          mediaFiles: Array<{ id: string; fileUrl: string }> | null;
        }>
      >`
        SELECT
          p."id",
          p."profileId",
          COALESCE(
            json_agg(
              json_build_object(
                'id', m."id",
                'fileUrl', m."fileUrl"
              )
            ) FILTER (WHERE m."id" IS NOT NULL),
            '[]'::json
          ) AS "mediaFiles"
        FROM "feed"."Post" p
        LEFT JOIN "feed"."PostMedia" m ON p."id" = m."postId"
        WHERE p."id" = ${filtersP.id}::uuid
        AND p."profileId" = ${state.profileId}::uuid
        AND p."status" = 'ACTIVE'
        GROUP BY p."id", p."profileId"
      `;

      if (!existingPostResult || existingPostResult.length === 0) {
        throw new AppError('POST007');
      }

      const existingPost = existingPostResult[0];
      if (existingPost.mediaFiles && existingPost.mediaFiles.length > 0) {
        await Promise.allSettled(
          existingPost.mediaFiles.map((media) => {
            ServiceModule.CoreStorageModule.deleteFile({ fileUrl: media.fileUrl });
          }),
        );
      }

      try {
        await deleteDocument('posts', existingPost.id);
      } catch (esError) {
        console.error('Failed to delete from Elasticsearch:', esError);
      }

      await prismaPG.post.delete({
        where: {
          id: existingPost.id,
        },
        include: {
          SavedPost: true,
        },
      });
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchOne: async (state: FastifyStateI, filtersP: RouteParamsI): Promise<Omit<PostExternalClientI, 'cursorId'>> => {
    try {
      const selfProfileId = state.profileId;
      const postResultArrTemp: PostDataClientI[] = await prismaPG.$queryRaw`
      SELECT p."id" AS "id",
      p."caption" AS "caption",
      p."reactionsCount" AS "reactionsCount",
      p."totalCommentsCount" AS "totalCommentsCount",
      EXISTS (
        SELECT 1 FROM "feed"."PostReaction" pr
        WHERE pr."postId" = p."id"
        AND pr."profileId" = ${selfProfileId}::uuid
      ) AS "isLiked",
      EXISTS (
        SELECT 1 FROM "feed"."SavedPost" sp
        WHERE sp."postId" = p."id"
        AND sp."profileId" = ${selfProfileId}::uuid
      ) AS "isSaved",
      p."createdAt" AS "createdAt",
      (
        SELECT json_agg(
          json_build_object(
            'caption', m."caption",
            'fileUrl', m."fileUrl"
          )
        )
        FROM "feed"."PostMedia" m
        WHERE m."postId" = p."id"
      ) AS "Media",
      json_build_object(
        'id', u."id",
        'name', u."name",
        'avatar', u."avatar",
        'designationText', u."designationText",
        'designationAlternativeId', u."designationAlternativeId",
        'designationRawDataId', u."designationRawDataId",
        'entityText', u."entityText",
        'entityId', u."entityId",
        'entityRawDataId', u."entityRawDataId"
      ) AS "Profile"
      FROM "feed"."Post" p
      INNER JOIN "user"."Profile" u
      ON p."profileId" = u."id"
      AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      WHERE p."status" = 'ACTIVE'
      AND p."id" = ${filtersP.id}::uuid
    `;
      const postResultTemp = postResultArrTemp?.[0];
      const postResult: PostExternalClientI = {
        ...postResultTemp,
        Profile: {
          id: postResultTemp.Profile.id,
          name: postResultTemp.Profile.name,
          avatar: postResultTemp.Profile.avatar,
          designation: postResultTemp.Profile?.designationAlternativeId
            ? {
                id: postResultTemp.Profile.designationAlternativeId,
                name: postResultTemp.Profile.designationText,
                dataType: 'master',
              }
            : postResultTemp.Profile?.designationRawDataId
              ? {
                  id: postResultTemp.Profile.designationRawDataId,
                  name: postResultTemp.Profile.designationText,
                  dataType: 'raw',
                }
              : null,
          entity: postResultTemp.Profile?.entityId
            ? {
                id: postResultTemp.Profile.entityId,
                name: postResultTemp.Profile.entityText,
                dataType: 'master',
              }
            : postResultTemp.Profile?.entityRawDataId
              ? {
                  id: postResultTemp.Profile.entityRawDataId,
                  name: postResultTemp.Profile.entityText,
                  dataType: 'raw',
                }
              : null,
        },
      };
      if (!postResult) {
        throw new AppError('POST001');
      }
      return postResult;
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchProfilePosts: async (state: FastifyStateI, { pagination }: PostFetchManyI) => {
    const { profileId: selfProfileId } = state;
    try {
      const postFetchManyResult: PostFetchManyResultI = {
        posts: [],
      };
      let cursorId: number | null = null;
      if (typeof pagination.cursorId === 'number' && pagination.cursorId > 0) {
        cursorId = Number(pagination.cursorId);
      }
      const userPostResult: PostDataClientI[] = await prismaPG.$queryRaw`
  SELECT
    p."id" AS "id",
    p."cursorId" AS "cursorId",
    p."caption" AS "caption",
    CASE
      WHEN length(p."caption") > 100 THEN LEFT(p."caption", 100) || '...'
      ELSE p."caption"
    END AS "captionTruncated",
    length(p."caption") > 100 AS "isCaptionTruncated",
    p."reactionsCount" AS "reactionsCount",
    p."totalCommentsCount" AS "totalCommentsCount",
    EXISTS (
      SELECT 1 FROM "feed"."PostReaction" pr
      WHERE pr."postId" = p."id"
        AND pr."profileId" = ${state.profileId}::uuid
    ) AS "isLiked",
    EXISTS (
      SELECT 1 FROM "feed"."SavedPost" sp
      WHERE sp."postId" = p."id"
      AND sp."profileId" = ${selfProfileId}::uuid
    ) AS "isSaved",
    p."createdAt" AS "createdAt",
    COALESCE(
      (
        SELECT json_agg(
          json_build_object(
            'caption', m."caption",
            'fileUrl', m."fileUrl"
          )
        )
        FROM "feed"."PostMedia" m
        WHERE m."postId" = p."id"
      ), '[]'::json
    ) AS "Media",
    json_build_object(
      'id', u."id",
      'name', u."name",
      'avatar', u."avatar",
      'designationText', u."designationText",
      'designationAlternativeId', u."designationAlternativeId",
      'designationRawDataId', u."designationRawDataId",
      'entityText', u."entityText",
      'entityId', u."entityId",
      'entityRawDataId', u."entityRawDataId"
    ) AS "Profile"
  FROM "feed"."Post" p
  INNER JOIN "user"."Profile" u
    ON p."profileId" = u."id"
    AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
  WHERE p."status" = 'ACTIVE'
    AND p."profileId" = ${selfProfileId}::uuid
    AND p."entityProfileId" IS NULL
    ${cursorId ? Prisma.sql`AND p."cursorId" < ${cursorId}` : Prisma.empty}
  ORDER BY p."createdAt" DESC
  LIMIT ${pagination.pageSize}
`;

      if (userPostResult?.length) {
        postFetchManyResult.posts.push(
          ...userPostResult.map((item) => ({
            ...item,
            cursorId: Number(item.cursorId),
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile?.designationAlternativeId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText,
                    dataType: 'master',
                  }
                : item.Profile?.designationRawDataId
                  ? {
                      id: item.Profile.designationRawDataId,
                      name: item.Profile.designationText,
                      dataType: 'raw',
                    }
                  : null,
              entity: item.Profile?.entityId
                ? {
                    id: item.Profile.entityId,
                    name: item.Profile.entityText,
                    dataType: 'master',
                  }
                : item.Profile?.entityRawDataId
                  ? {
                      id: item.Profile.entityRawDataId,
                      name: item.Profile.entityText,
                      dataType: 'raw',
                    }
                  : null,
            } as ProfileExternalI,
          })),
        );
        postFetchManyResult.cursorId = Number(userPostResult[userPostResult.length - 1].cursorId);
      }
      return postFetchManyResult;
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchFullPost: async (postId: string, profileId: string): Promise<PostDataClientI> => {
    try {
      const post = await prismaPG.$queryRaw<PostDataClientI[]>`
      SELECT
        p."id" AS "id",
        p."caption" AS "caption",
        p."reactionsCount" AS "reactionsCount",
        p."totalCommentsCount" AS "totalCommentsCount",
        EXISTS (
          SELECT 1 FROM "feed"."PostReaction" pr
          WHERE pr."postId" = p."id"
          AND pr."profileId" = ${profileId}::uuid
        ) AS "isLiked",
        EXISTS (
          SELECT 1 FROM "feed"."SavedPost" sp
          WHERE sp."postId" = p."id"
          AND sp."profileId" = ${profileId}::uuid
        ) AS "isSaved",
        p."createdAt" AS "createdAt",
        (
          SELECT json_agg(
            json_build_object(
              'caption', m."caption",
              'fileUrl', m."fileUrl"
            )
          )
          FROM "feed"."PostMedia" m
          WHERE m."postId" = p."id"
        ) AS "Media",
        json_build_object(
          'id', u."id",
          'name', u."name",
          'avatar', u."avatar",
          'designationText', u."designationText",
          'designationAlternativeId', u."designationAlternativeId",
          'designationRawDataId', u."designationRawDataId",
          'entityText', u."entityText",
          'entityId', u."entityId",
          'entityRawDataId', u."entityRawDataId"
        ) AS "Profile"
      FROM "feed"."Post" p
      INNER JOIN "user"."Profile" u
        ON p."profileId" = u."id"
        AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      WHERE p."id" = ${postId}::uuid
      AND p."status" = 'ACTIVE'
    `;

      return post[0];
    } catch (error) {
      errorHandler(error);
    }
  },

  updateOne: async (state: FastifyStateI, { postId, caption, files }: PostUpdateOneI) => {
    try {
      const profileId = state.profileId;
      const existingPost = await prismaPG.post.findFirst({
        where: {
          id: postId,
          profileId,
          status: 'ACTIVE',
        },
        include: { Media: true },
      });
      if (!existingPost) {
        throw new AppError('POST003');
      }
      const updatedPost = await prismaPG.post.update({
        where: { id: postId },
        data: {
          caption,
          Media:
            files !== undefined
              ? {
                  deleteMany: {},
                  ...(files.length > 0 && {
                    createMany: {
                      data: files.map((fileItem) => ({
                        caption: fileItem?.caption,
                        profileId,
                        fileUrl: fileItem.fileUrl,
                        fileExtension: fileItem.extension,
                      })),
                    },
                  }),
                }
              : undefined,
        },
        include: { Media: true },
      });
      if (!updatedPost) {
        throw new AppError('POST004');
      }

      try {
        await updateDocument('posts', postId, {
          caption: updatedPost.caption,
          reactionsCount: updatedPost.reactionsCount,
          totalCommentsCount: updatedPost.totalCommentsCount,
          media:
            updatedPost.Media?.map((m) => ({
              caption: m.caption,
              fileUrl: m.fileUrl,
              fileExtension: m.fileExtension,
            })) || [],
        });
      } catch (esError) {
        console.error('Failed to update post in Elasticsearch:', esError);
      }

      const postForClientResult = { ...omit(updatedPost, ['cursorId']), cursorId: Number(updatedPost['cursorId']) };
      return postForClientResult;
    } catch (error) {
      errorHandler(error);
    }
  },

  fetchFullCaption: async (postId: string): Promise<{ caption: string }> => {
    try {
      const post = await prismaPG.post.findUnique({
        where: { id: postId, status: PostStatusE.ACTIVE },
        select: { caption: true },
      });

      if (!post) {
        throw new AppError('POST001');
      }

      return { caption: post.caption };
    } catch (error) {
      errorHandler(error);
    }
  },
};

export default PostModule;
