import AppError from '@classes/AppError';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { SavedPostCreateI, SavedPostDeleteI, SavedPostFetchManyI } from '@schemas/feed/savedPost';
import { prismaPG } from '@config/db';
import { errorHandler } from '@utils/errors/handler';
import type { SavedPost } from '@interfaces/feed/savedPost';
import { Prisma } from '@prisma/postgres';

const MAX_CAPTION_LENGTH = 250;

const SavedPostModule = {
  createOne: async (state: FastifyStateI, { postId }: SavedPostCreateI): Promise<SavedPost> => {
    try {
      const profileId = state.profileId;
      const existing = await prismaPG.savedPost.findFirst({ where: { profileId, postId } });
      if (existing) throw new AppError('SAVEDPOST001');
      const postExists = await prismaPG.post.findFirst({ where: { id: postId, status: 'ACTIVE' } });
      if (!postExists) throw new AppError('SAVEDPOST002');

      const saved = await prismaPG.savedPost.create({
        data: { profileId, postId },
        include: {
          Post: {
            include: {
              Profile: true,
              EntityProfile: true,
              Media: true,
            },
          },
        },
      });

      const post = saved.Post;

      const Profile = post.Profile
        ? {
            id: post.Profile.id,
            name: post.Profile.name,
            avatar: post.Profile.avatar,
            designation: post.Profile.designationAlternativeId
              ? {
                  id: post.Profile.designationAlternativeId,
                  name: post.Profile.designationText,
                  dataType: 'master' as const,
                }
              : post.Profile.designationRawDataId
                ? {
                    id: post.Profile.designationRawDataId,
                    name: post.Profile.designationText,
                    dataType: 'raw' as const,
                  }
                : null,
            entity: post.Profile.entityId
              ? { id: post.Profile.entityId, name: post.Profile.entityText, dataType: 'master' as const }
              : post.Profile.entityRawDataId
                ? { id: post.Profile.entityRawDataId, name: post.Profile.entityText, dataType: 'raw' as const }
                : null,
          }
        : null;

      return {
        id: post.id,
        caption: post.caption,
        isCaptionTruncated: post.caption ? post.caption.length > MAX_CAPTION_LENGTH : false,
        reactionsCount: post.reactionsCount || 0,
        totalCommentsCount: post.totalCommentsCount || 0,
        isSaved: true,
        isLiked: false,
        postCreatedAt: post.createdAt,
        postUpdatedAt: post.updatedAt,
        Media:
          post.Media?.map((m) => ({
            id: m.id,
            postId: m.postId,
            profileId: m.profileId,
            entityProfileId: m.entityProfileId,
            caption: m.caption,
            fileUrl: m.fileUrl,
            extension: m.fileExtension,
            createdAt: m.createdAt,
            updatedAt: m.updatedAt,
          })) || [],
        Profile,
        EntityProfile: post.EntityProfile
          ? { id: post.EntityProfile.id, name: post.EntityProfile.name, avatar: post.EntityProfile.avatar }
          : null,
      } as SavedPost;
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  },

  deleteOne: async (state: FastifyStateI, { postId }: SavedPostDeleteI): Promise<{ success: boolean }> => {
    try {
      const profileId = state.profileId;
      const existing = await prismaPG.savedPost.findFirst({ where: { profileId, postId } });
      if (!existing) throw new AppError('SAVEDPOST004');
      await prismaPG.savedPost.delete({ where: { id: existing.id } });
      return { success: true };
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  },
  fetchMany: async (
    state: FastifyStateI,
    { pagination }: SavedPostFetchManyI,
  ): Promise<{ cursorId?: number; posts: SavedPost[] }> => {
    const profileId = state.profileId;

    try {
      const result: { cursorId?: number; posts: SavedPost[] } = { posts: [] };
      const cursorId = pagination.cursorId && pagination.cursorId > 0 ? BigInt(pagination.cursorId) : null;

      const postsResult = await prismaPG.$queryRaw<
        {
          cursorId: bigint;
          postId: string;
          caption: string | null;
          createdAt: Date;
          updatedAt: Date;
          reactionsCount: number;
          totalCommentsCount: number;
          Profile: {
            id: string;
            name: string;
            avatar: string;
            designationText: string | null;
            designationAlternativeId: string | null;
            designationRawDataId: string | null;
            entityText: string | null;
            entityId: string | null;
            entityRawDataId: string | null;
          };
          EntityProfile: { id: string; name: string; avatar: string } | null;
          Media:
            | {
                id: string;
                postId: string;
                profileId: string;
                entityProfileId: string | null;
                caption: string | null;
                fileUrl: string;
                fileExtension: string;
                createdAt: Date;
                updatedAt: Date;
              }[]
            | null;
        }[]
      >`
      SELECT sp."cursorId",
             p."id" AS "postId",
             p."caption",
             p."createdAt",
             p."updatedAt",
             p."reactionsCount",
             p."totalCommentsCount",
             json_build_object(
               'id', u."id",
               'name', u."name",
               'avatar', u."avatar",
               'designationText', u."designationText",
               'designationAlternativeId', u."designationAlternativeId",
               'designationRawDataId', u."designationRawDataId",
               'entityText', u."entityText",
               'entityId', u."entityId",
               'entityRawDataId', u."entityRawDataId"
             ) AS "Profile",
             json_build_object(
               'id', ep."id",
               'name', ep."name",
               'avatar', ep."avatar"
             ) AS "EntityProfile",
             (SELECT json_agg(json_build_object(
                 'id', m."id",
                 'postId', m."postId",
                 'profileId', m."profileId",
                 'entityProfileId', m."entityProfileId",
                 'caption', m."caption",
                 'fileUrl', m."fileUrl",
                 'fileExtension', m."fileExtension",
                 'createdAt', m."createdAt",
                 'updatedAt', m."updatedAt"
               ))
              FROM "feed"."PostMedia" m WHERE m."postId" = p."id") AS "Media"
      FROM "feed"."SavedPost" sp
      INNER JOIN "feed"."Post" p ON sp."postId" = p."id"
      INNER JOIN "user"."Profile" u ON p."profileId" = u."id"
      LEFT JOIN "company"."EntityProfile" ep ON p."entityProfileId" = ep."id"
      WHERE sp."profileId" = ${profileId}::uuid
      ${cursorId ? Prisma.sql`AND sp."cursorId" < ${cursorId}` : Prisma.sql``}
      ORDER BY sp."cursorId" DESC
      LIMIT ${Number(pagination.pageSize)};
    `;

      if (postsResult.length > 0) {
        result.cursorId = Number(postsResult[postsResult.length - 1].cursorId);
        result.posts.push(
          ...postsResult.map((item) => ({
            id: item.postId,
            caption: item.caption,
            isCaptionTruncated: item.caption ? item.caption.length > MAX_CAPTION_LENGTH : false,
            reactionsCount: item.reactionsCount || 0,
            totalCommentsCount: item.totalCommentsCount || 0,
            isSaved: true,
            isLiked: false,
            postCreatedAt: item.createdAt ?? null,
            postUpdatedAt: item.updatedAt ?? null,
            Media: item.Media
              ? item.Media.map((m) => ({
                  id: m.id,
                  postId: m.postId,
                  profileId: m.profileId,
                  entityProfileId: m.entityProfileId,
                  caption: m.caption,
                  fileUrl: m.fileUrl,
                  extension: m.fileExtension,
                  createdAt: m.createdAt,
                  updatedAt: m.updatedAt,
                }))
              : [],
            Profile: {
              id: item.Profile.id,
              name: item.Profile.name,
              avatar: item.Profile.avatar,
              designation: item.Profile.designationAlternativeId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText!,
                    dataType: 'master' as const,
                  }
                : item.Profile.designationRawDataId
                  ? {
                      id: item.Profile.designationRawDataId,
                      name: item.Profile.designationText!,
                      dataType: 'raw' as const,
                    }
                  : null,
              entity: item.Profile.entityId
                ? { id: item.Profile.entityId, name: item.Profile.entityText!, dataType: 'master' as const }
                : item.Profile.entityRawDataId
                  ? { id: item.Profile.entityRawDataId, name: item.Profile.entityText!, dataType: 'raw' as const }
                  : null,
            },
            EntityProfile: item.EntityProfile
              ? { id: item.EntityProfile.id, name: item.EntityProfile.name, avatar: item.EntityProfile.avatar }
              : null,
          })),
        );
      }

      return result;
    } catch (error) {
      console.log(error);
      errorHandler(error);
      throw error;
    }
  },
};

export default SavedPostModule;
