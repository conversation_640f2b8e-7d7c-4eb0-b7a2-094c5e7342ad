import AppError from '@classes/AppError';
import { FastifyRequestI } from '@interfaces/common/declaration';

export const rateLimitConfig = {
  unsecure: {
    max: 50,
    ban: -1,
    timeWindow: '60000',
    keyGenerator: (request: FastifyRequestI) => {
      return request.ip;
    },
    errorResponseBuilder: () => {
      throw new AppError('AUTH030');
    },
  },
  secure: {
    max: 100,
    timeWindow: '60000',
    keyGenerator: (request: FastifyRequestI) => request.profileId || request.ip,
    errorResponseBuilder: () => {
      throw new AppError('AUTH030');
    },
  },
};
