import { CursorPaginationSchema } from '@schemas/common/common';
import { z } from 'zod';

export const SavedPostCreateSchema = z.object({
  postId: z.string().uuid(),
});
export type SavedPostCreateI = z.infer<typeof SavedPostCreateSchema>;

export const SavedPostDeleteSchema = z.object({
  postId: z.string().uuid(),
});
export type SavedPostDeleteI = z.infer<typeof SavedPostDeleteSchema>;

export const SavedPostFetchManyQuerySchema = CursorPaginationSchema;
export type SavedPostFetchManyQueryI = z.infer<typeof SavedPostFetchManyQuerySchema>;

export const SavedPostFetchManySchema = z.object({
  pagination: CursorPaginationSchema,
});
export type SavedPostFetchManyI = z.infer<typeof SavedPostFetchManySchema>;
