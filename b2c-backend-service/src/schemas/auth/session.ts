import { ProfileTypeE } from '@consts/auth/common';
import { LatitudeSchema, LongitudeSchema } from '@schemas/port/common';
import z from 'zod';

export const SessionUpdateCoordinatesParamsSchema = z.object({
  latitude: LatitudeSchema,
  longitude: LongitudeSchema,
});

export type SessionUpdateCoordinatesParamsI = z.infer<typeof SessionUpdateCoordinatesParamsSchema>;
export const SessionUpdateDeviceTokenParamsSchema = z.object({
  deviceToken: z.string().min(1, 'Device Token Is Required'),
});

export type SessionUpdateDeviceTokenParamsI = z.infer<typeof SessionUpdateDeviceTokenParamsSchema>;

export const SessionUpdateProfileTypeParamsSchema = z.object({
  type: ProfileTypeE,
});

export type SessionUpdateProfileTypeParamsI = z.infer<typeof SessionUpdateProfileTypeParamsSchema>;
