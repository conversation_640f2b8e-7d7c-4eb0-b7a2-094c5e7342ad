import { CommunityAccessE } from '@consts/forum/community';
import { MemberTypeE } from '@consts/forum/member';
import { CursorPaginationSchema, FileOperationSchema, RouteParamsSchema } from '@schemas/common/common';
import { z } from 'zod';

const CommunityMemberSchema = z.object({
  profileId: z.string().uuid(),
  type: MemberTypeE,
});

export const CommunityCreateOneSchema = z.object({
  name: z.string().min(1).max(100).nullable(),
  description: z.string().max(300).optional(),
  access: CommunityAccessE,
  isRestricted: z.boolean(),
  members: z.array(CommunityMemberSchema).optional().default([]),
  avatar: FileOperationSchema.optional(),
});
export type CommunityCreateOneI = z.infer<typeof CommunityCreateOneSchema>;
export const CommunityUpdateOneSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(300).optional().nullable(),
  access: CommunityAccessE.optional(),
  isRestricted: z.boolean().optional(),
  members: z.array(CommunityMemberSchema).optional(),
  avatar: FileOperationSchema.optional(),
});

export type CommunityUpdateOneI = z.infer<typeof CommunityUpdateOneSchema>;
export const CommunityFetchForClientSchema = CursorPaginationSchema.extend({
  name: z.string().min(1).max(100).nullable().optional(),
});

export type CommunityFetchForClientI = z.infer<typeof CommunityFetchForClientSchema>;

export const CommunityFetchOneForExternalClientParamsSchema = RouteParamsSchema;
export type CommunityOneForExternalClientParamsI = z.infer<typeof CommunityFetchOneForExternalClientParamsSchema>;

export const CommunityFetchUserCommunitiesSchema = CursorPaginationSchema.extend({
  type: z.enum(['ADMIN', 'MEMBER', 'ALL']).optional().default('ALL'),
});
export type CommunityFetchUserCommunitiesI = z.infer<typeof CommunityFetchUserCommunitiesSchema>;
