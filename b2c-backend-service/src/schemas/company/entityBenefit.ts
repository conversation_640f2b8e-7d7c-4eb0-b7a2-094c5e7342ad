import { DBDataTypeE } from '@consts/common/data';
import { EntityBenefitTitleR } from '@consts/common/regex/regex';
import { UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const EntityBenefitTitleSchema = z.object({
  title: z.string().min(2).max(100).regex(EntityBenefitTitleR),
});
export type EntityBenefitTitleI = z.infer<typeof EntityBenefitTitleSchema>;
export const EntityBenefitIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type EntityBenefitIdClientI = z.infer<typeof EntityBenefitIdClientSchema>;
