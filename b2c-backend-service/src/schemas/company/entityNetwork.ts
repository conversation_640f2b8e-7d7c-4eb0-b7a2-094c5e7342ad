import { UUIDSchema } from '@navicater/b2c-internal-communication';
import z from 'zod';

export const EntityProfileFollowOneParamsSchema = z.object({
  followeeEntityProfileId: UUIDSchema,
});
export type EntityProfileFollowOneParamsI = z.infer<typeof EntityProfileFollowOneParamsSchema>;

export const EntityProfileUnFollowOneParamsSchema = z.object({
  unfolloweeEntityProfileId: UUIDSchema,
});
export type EntityProfileUnFollowOneParamsI = z.infer<typeof EntityProfileUnFollowOneParamsSchema>;
