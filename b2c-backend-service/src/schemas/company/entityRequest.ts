import { z } from 'zod';
import { UUIDSchema } from '@schemas/common/common';

export const EntityRequestBodySchema = z.object({
  entityId: UUIDSchema.optional(),
  entityRawDataId: UUIDSchema.optional(),
  profileId: UUIDSchema,
});

export const EntityRequestApprovalQuerySchema = z.object({
  profileId: UUIDSchema,
  entityId: UUIDSchema.optional(),
  entityRawDataId: UUIDSchema.optional(),
});

export const EntityRequestRejectionQuerySchema = EntityRequestApprovalQuerySchema;
export const EntityRequestRevocationQuerySchema = EntityRequestApprovalQuerySchema;

export type EntityRequestBodyI = z.infer<typeof EntityRequestBodySchema>;
export type EntityRequestApprovalQueryI = z.infer<typeof EntityRequestApprovalQuerySchema>;
export type EntityRequestRejectionQueryI = z.infer<typeof EntityRequestRejectionQuerySchema>;
export type EntityRequestRevocationQueryI = z.infer<typeof EntityRequestRevocationQuerySchema>;
