import { EntityMemberRoleE } from '@consts/company/entityMember';
import { IdTypeSchema, UUIDSchema } from '../common/common';
import { z } from 'zod';

export const EntityMemberFetchSchema = z.object({
  entityId: UUIDSchema,
});
export type EntityMemberFetchParamsI = z.infer<typeof EntityMemberFetchSchema>;

export const EntityMemberUpsertSchema = z
  .object({
    entityProfileId: UUIDSchema,
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
    profileId: UUIDSchema,
    role: EntityMemberRoleE,
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });

export type EntityMemberUpsertParamsI = z.infer<typeof EntityMemberUpsertSchema>;

export const EntityMemberDeleteSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
    profileId: UUIDSchema,
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });
export type EntityMemberDeleteParamsI = z.infer<typeof EntityMemberDeleteSchema>;

export const EntityIsAnyMemberSchema = z.object({
  entity: IdTypeSchema,
  profileId: UUIDSchema,
});
export type EntityIsAnyMemberI = z.infer<typeof EntityIsAnyMemberSchema>;
