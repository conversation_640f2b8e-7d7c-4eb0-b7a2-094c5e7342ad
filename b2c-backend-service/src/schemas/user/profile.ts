import { GenderE } from '@consts/user/profile';
import { OTPSchema } from '@navicater/b2c-internal-communication';
import {
  CountryIso2Schema,
  EmailSchema,
  IdTypeSchema,
  PaginationSchema,
  PersonFullNameSchema,
  UsernameSchema,
  UUIDSchema,
} from '@schemas/common/common';
import { z } from 'zod';

export const UpdateBioParamsSchema = z
  .object({
    name: PersonFullNameSchema.optional(),
    description: z.string().max(255).optional(),
    designation: IdTypeSchema.optional(),
    entity: IdTypeSchema.optional(),
    avatar: z.string().optional().nullable(),
  })
  .refine((data) => Object.values(data).some((value) => value !== undefined && value !== null), {
    message: 'At least one attribute is required',
  });
export type UpdateBioParamsI = z.infer<typeof UpdateBioParamsSchema>;

export const UpdateUsernameParamsSchema = z.object({
  username: UsernameSchema,
});
export type UpdateUsernameParamsI = z.infer<typeof UpdateUsernameParamsSchema>;

export const UpdateAuthUsernameParamsSchema = z.object({
  profileId: UUIDSchema,
  username: UsernameSchema,
});
export type UpdateAuthUsernameParamsI = z.infer<typeof UpdateAuthUsernameParamsSchema>;

export const CreateOnBoardingPersonalParamsSchema = z.object({
  fullName: PersonFullNameSchema,
  gender: GenderE.nullable(),
  countryIso2: CountryIso2Schema,
});
export type CreateOnBoardingPersonalParamsI = z.infer<typeof CreateOnBoardingPersonalParamsSchema>;

export const CreateOnBoardingWorkParamsSchema = z.object({
  designation: IdTypeSchema,
  entity: IdTypeSchema.optional(),
});

// .refine((data) => NON_ENTITY_EMPLOYEE_DESIGNATION_IDS.has(data.designationId) || data.entityId, {
//   message: 'entityId is required unless the designation is non maritime employee',
//   path: ['designationId'],
// });
export type CreateOnBoardingWorkParamsI = z.infer<typeof CreateOnBoardingWorkParamsSchema>;

export const VerifyOTPForEmailVerificationSchema = z.object({
  otp: OTPSchema,
});
export type VerifyOTPForEmailVerificationI = z.infer<typeof VerifyOTPForEmailVerificationSchema>;

export const VerifyOTPForPasswordResetSchema = z.object({
  otp: OTPSchema,
  email: EmailSchema,
});
export type VerifyOTPForPasswordResetI = z.infer<typeof VerifyOTPForPasswordResetSchema>;

export const FetchUsernameQuerySchema = PaginationSchema.extend({
  search: z.string(),
});

export type FetchUsernameQueryI = z.infer<typeof FetchUsernameQuerySchema>;
