import { DBDataTypeE } from '@consts/common/data';
import { EquipmentModelR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { EquipmentCategoryIdClientSchema } from '@schemas/ship/equipmentCategory';
import { EquipmentManufacturerIdClientSchema } from '@schemas/ship/equipmentManufacturer';
import { z } from 'zod';

export const EquipmentModelFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z
      .string()
      .min(0)
      .max(100)
      .regex(EquipmentModelR)
      .transform((data) => data.trim().toLowerCase())
      .optional(),
    equipmentCategory: EquipmentCategoryIdClientSchema.optional(),
    equipmentManufacturer: EquipmentManufacturerIdClientSchema.optional(),
  }),
);
export type EquipmentModelFetchForClientI = z.infer<typeof EquipmentModelFetchForClientSchema>;

export const EquipmentModelFetchsertSchema = z.object({
  name: z.string().min(2).max(100).regex(EquipmentModelR),
});
export type EquipmentModelFetchsertI = z.infer<typeof EquipmentModelFetchsertSchema>;

export const EquipmentModelIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type EquipmentModelIdClientI = z.infer<typeof EquipmentModelIdClientSchema>;
