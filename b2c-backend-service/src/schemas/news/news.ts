import { z } from 'zod';

export const NewsFetchManySchema = z.object({
  cursorId: z.string().optional(),
  pageSize: z.string().optional().default('10'),
  topicId: z.string().optional(),
  sortBy: z.enum(['publishedDate', 'scrapedAt']).optional().default('publishedDate'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export type NewsFetchManyI = z.infer<typeof NewsFetchManySchema>;

export const NewsTopicFetchAllSchema = z.object({});

export type NewsTopicFetchAllI = z.infer<typeof NewsTopicFetchAllSchema>;
