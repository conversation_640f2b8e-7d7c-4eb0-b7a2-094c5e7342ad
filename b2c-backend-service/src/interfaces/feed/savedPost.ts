export interface SavedPostMedia {
  id: string;
  postId: string;
  profileId: string;
  entityProfileId: string | null;
  caption: string | null;
  fileUrl: string;
  extension: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SavedPostProfile {
  id: string;
  name: string;
  avatar: string;
  designation: {
    id: string;
    name: string;
    dataType: 'master' | 'raw';
  } | null;
  entity: {
    id: string;
    name: string;
    dataType: 'master' | 'raw';
  } | null;
}

export interface SavedPostEntityProfile {
  id: string;
  name: string;
  avatar: string;
}

export interface SavedPost {
  id: string;
  caption: string | null;
  isCaptionTruncated: boolean;
  reactionsCount: number;
  totalCommentsCount: number;
  isSaved: boolean;
  isLiked: boolean;
  postCreatedAt: Date | null;
  postUpdatedAt: Date | null;
  Media: SavedPostMedia[] | null;
  Profile: SavedPostProfile;
  EntityProfile: SavedPostEntityProfile | null;
}
