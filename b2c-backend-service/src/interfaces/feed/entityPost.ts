import type { PostMedia } from '@prisma/postgres';
import { ProfileExternalI } from '@interfaces/user/profile';
import { EntityProfileExternalI, EntityProfileForExternalClient } from '@interfaces/company/entityProfile';

export type EntityPostDataClientI = {
  id: string;
  caption: string;
  shortCaption?: string;
  isCaptionTruncated?: boolean;
  cursorId?: number | bigint;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  entity?: {
    id: string;
    name: string;
    avatar: string | null;
    type: 'master' | 'raw';
  };
  Profile: ProfileExternalI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
};

export type EntityPostExternalClientI = Omit<EntityPostDataClientI, 'shortCaption' | 'isCaptionTruncated'>;

export type EntityPostFetchManyResultI = {
  posts: EntityPostExternalClientI[];
  cursorId?: number;
};

export type EntityProfilePostDataClientI = {
  caption: string;
  id: string;
  shortCaption?: string;
  isCaptionTruncated?: boolean;
  cursorId?: number;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  EntityProfile: EntityProfileForExternalClient;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
};

export type EntityProfilePostExternalClientI = {
  caption: string;
  id: string;
  cursorId?: number;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  EntityProfile: EntityProfileExternalI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
};

export type EntityProfilePostFetchManyResultI = {
  posts: EntityProfilePostExternalClientI[];
  cursorId?: number;
  otherCursorId?: number;
};
