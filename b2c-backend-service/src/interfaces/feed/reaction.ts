import { ReactionTypeI } from '@consts/feed/reaction';
import { EntityProfileDataI } from '@interfaces/company/entityProfile';
import { ProfileDataI, ProfileExternalI } from '@interfaces/user/profile';

export type ReactionDataClientI = {
  postId: string;
  reactionType: ReactionTypeI;
  Profile: ProfileDataI;
  EntityProfile: EntityProfileDataI;
};
export type ReactionExternalClientI = {
  postId: string;
  reactionType: ReactionTypeI;
  Profile: ProfileExternalI;
  EntityProfile: EntityProfileDataI;
};
