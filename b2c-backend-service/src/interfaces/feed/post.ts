import type { UndefinedNullAttrsI } from '@interfaces/common/data';
import type { EntityProfileForExternalClient } from '@interfaces/company/entityProfile';
import type { ProfileDataI, ProfileExternalI } from '@interfaces/user/profile';
import type { Post, PostMedia } from '@prisma/postgres';

export type PostDataClientI = {
  caption: string;
  id: string;
  shortCaption?: string;
  isCaptionTruncated?: boolean;
  cursorId?: number;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  Profile: ProfileDataI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
  EntityProfile: EntityProfileForExternalClient;
};
export type PostExternalClientI = {
  caption: string;
  id: string;
  cursorId?: number;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  Profile: ProfileExternalI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
  EntityProfile: EntityProfileForExternalClient;
};
export type PostFetchManyResultI = { posts: PostExternalClientI[]; cursorId?: number; otherCursorId?: number };

export type PostForNotificationI = UndefinedNullAttrsI<
  Pick<Post, 'id' | 'caption' | 'profileId' | 'entityProfileId'>,
  'entityProfileId' | 'profileId'
> & { image?: string };
