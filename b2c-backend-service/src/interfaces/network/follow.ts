import { EntityProfileDataI, EntityProfileExternalI } from '@interfaces/company/entityProfile';
import { ProfileDataI, ProfileNetworkExternalI } from '@interfaces/user/profile';

export type FollowDataI = {
  cursorId: number;
  isConnected: boolean;
  isFollowing: boolean;
  Profile: ProfileDataI;
  EntityProfile: EntityProfileDataI;
};
export type FollowExternalI = {
  cursorId: number;
  Profile: ProfileNetworkExternalI | null;
  EntityProfile: EntityProfileExternalI | null;
};
