import { ProfileSkillDataI, SkillNestedExternalI } from '@interfaces/career/skill';
import type {
  BooleanUndefinedNullI,
  DateNullI,
  IdNameDataTypeI,
  NullableI,
  NumberNullI,
  StringNullI,
  StringUndefinedNullI,
  AllUndefinedNullableI,
} from '@interfaces/common/data';
import { DegreeNestedClientI } from '@interfaces/company/degree';
import type { DesignationNestedClientI } from '@interfaces/company/designation';
import type { EntityNestedClientI, EntityNestedExternalI } from '@interfaces/company/entity';
import { CityNestedClientI } from '@interfaces/master/city';
import { Prisma, ProfileStatus } from '@prisma/postgres';
import type { Profile } from '@prisma/postgres';

export type ProfileUpdateOneDataI = Prisma.XOR<Prisma.ProfileUpdateInput, Prisma.ProfileUncheckedUpdateInput>;

export type ProfileDataI = Pick<
  Profile,
  | 'id'
  | 'name'
  | 'avatar'
  | 'designationText'
  | 'designationAlternativeId'
  | 'designationRawDataId'
  | 'entityText'
  | 'entityId'
  | 'entityRawDataId'
>;
export type ProfileTransformParamsI = AllUndefinedNullableI<
  Pick<Profile, 'id' | 'name' | 'avatar' | 'designationText' | 'entityText'>
> & {
  designationAlternativeId?: StringUndefinedNullI;
  designationRawDataId?: StringUndefinedNullI;
  entityId?: StringUndefinedNullI;
  entityRawDataId?: StringUndefinedNullI;
};
export type ProfileTransformI = Pick<Profile, 'id' | 'name' | 'avatar'> & {
  designation: IdNameDataTypeI;
  entity: IdNameDataTypeI;
};
export type ProfileSearchI = Pick<
  Profile,
  | 'name'
  | 'avatar'
  | 'username'
  | 'designationText'
  | 'designationAlternativeId'
  | 'designationRawDataId'
  | 'entityText'
  | 'entityId'
  | 'entityRawDataId'
> & {
  profileId: string;
  priority: 1 | 2 | 3;
};
export type ProfileSearchExternalI = Pick<ProfileSearchI, 'profileId' | 'name' | 'avatar' | 'username' | 'priority'> & {
  designation: DesignationNestedClientI | null;
  entity: EntityNestedExternalI | null;
};
export type ProfileDataNetworkI = ProfileDataI & {
  isConnected?: BooleanUndefinedNullI;
};
export type ProfileExternalI = Pick<Profile, 'id' | 'name' | 'avatar'> & {
  cursorId?: number;
  designation: DesignationNestedClientI | null;
  entity: EntityNestedExternalI | null;
};
export type PeopleRawI = Pick<
  Profile,
  | 'id'
  | 'name'
  | 'avatar'
  | 'designationText'
  | 'designationAlternativeId'
  | 'designationRawDataId'
  | 'entityText'
  | 'entityId'
  | 'entityRawDataId'
> & {
  isConnected: boolean;
  requestStatus: string;
};
export type PeopleI = Pick<Profile, 'id' | 'name' | 'avatar'> & {
  designation: DesignationNestedClientI | null;
  entity: EntityNestedExternalI | null;
  isConnected: boolean;
};

export type ProfileNetworkExternalI = ProfileExternalI & {
  isConnected?: BooleanUndefinedNullI;
  isFollowing?: boolean;
};
export type ProfileFetchBioI = Pick<Profile, 'name' | 'description' | 'designationText' | 'entityText'> & {
  designation: DesignationNestedClientI | null;
  entity: EntityNestedClientI | null;
};

export type ProfileFetchDataI = Pick<Profile, 'email'> &
  Partial<Pick<Profile, 'name' | 'username' | 'designationText' | 'entityText' | 'avatar' | 'description'>> &
  Pick<
    ProfileStatus,
    'isEmailVerified' | 'isPersonalDetailsSaved' | 'isWorkDetailsSaved' | 'isPrivacyPolicyAccepted' | 'profileId'
  > & {
    designation: DesignationNestedClientI | null;
    entity: EntityNestedClientI | null;
    country: CityNestedClientI | null;
  };

export type ProfileRequestI = {
  status: 'PENDING' | 'CONNECTED' | null;
  senderRequestId: StringNullI;
};
export type ProfileInstitutionRawDataI = {
  id: string;
  profileId: string;
  entityId: StringNullI;
  entityRawDataId: StringNullI;
  degreeId: StringNullI;
  degreeRawDataId: StringNullI;
  entityName: StringNullI;
  entityRawDataName: StringNullI;
  degreeName: StringNullI;
  degreeRawDataName: StringNullI;
  fromDate: DateNullI;
  toDate: DateNullI;
  createdAt: Date;
};
export type ProfileInstitutionI = Pick<ProfileInstitutionRawDataI, 'id' | 'fromDate' | 'toDate'> & {
  degree: DegreeNestedClientI | null;
  entity: EntityNestedExternalI | null;
};
export type ProfileMetaI = {
  educationCount: NumberNullI;
  statutoryCertCount: NumberNullI;
  valueAddedCertCount: NumberNullI;
  identityCount: NumberNullI;
  visaCount: NumberNullI;
  maritimeSkillsTotal: NumberNullI;
  mutualTotal: NumberNullI;
};
export type ProfileIdentityI = {
  id: string;
  type: string;
  fromDate: DateNullI;
  untilDate: DateNullI;
  countryName: string;
};
export type ProfileStatutoryCertRawDataI = {
  id: string;
  entityId: StringNullI;
  entityName: StringNullI;
  entityRawDataId: StringNullI;
  entityRawDataName: StringNullI;
  certificateCourseId: StringNullI;
  certificateCourseName: StringNullI;
  certificateCourseRawDataId: StringNullI;
  certificateCourseRawDataName: StringNullI;
  fromDate: DateNullI;
  untilDate: DateNullI;
};
export type ProfileStatutoryCertI = Pick<ProfileStatutoryCertRawDataI, 'id' | 'fromDate' | 'untilDate'> & {
  name: StringNullI;
  entity: NullableI<EntityNestedExternalI>;
};
export type ProfileFetchAboutResultTempI = {
  profileId: string;
  username: StringNullI;
  name: StringNullI;
  avatar: StringNullI;
  designationText: StringNullI;
  designationAlternativeId: StringNullI;
  designationRawDataId: StringNullI;
  entityText: StringNullI;
  entityId: StringNullI;
  entityRawDataId: StringNullI;
  followersCount: number;
  followingsCount: number;
  description: StringNullI;
  isFollowing: boolean;
  meta: NullableI<ProfileMetaI>;
  request: ProfileRequestI;
  institutions: NullableI<ProfileInstitutionRawDataI[]>;
  identities: NullableI<ProfileIdentityI[]>;
  statutoryCerts: NullableI<ProfileStatutoryCertRawDataI[]>;
  maritimeSkills: NullableI<ProfileSkillDataI[]>;
  mutuals: NullableI<ProfileExternalI[]>;
};
export type ProfileFetchAboutResultI = Pick<
  ProfileFetchAboutResultTempI,
  | 'profileId'
  | 'username'
  | 'name'
  | 'avatar'
  | 'followersCount'
  | 'followingsCount'
  | 'description'
  | 'isFollowing'
  | 'request'
> & {
  designation: DesignationNestedClientI | null;
  entity: EntityNestedExternalI | null;
  meta: ProfileMetaI;
  institutions: ProfileInstitutionI[];
  statutoryCerts: ProfileStatutoryCertI[];
  identities: ProfileIdentityI[];
  maritimeSkills: SkillNestedExternalI[];
  mutuals: NullableI<ProfileExternalI[]>;
};
export type ProfileForClientI = Pick<Profile, 'id' | 'name' | 'avatar' | 'designationText' | 'entityText'>;

export type ProfileForDataI = Pick<Profile, 'id' | 'name' | 'avatar'>;

export type ProfileIdI = Pick<Profile, 'id'>;

export type UsernameForClient = Pick<Profile, 'id' | 'name' | 'username' | 'avatar'>;
