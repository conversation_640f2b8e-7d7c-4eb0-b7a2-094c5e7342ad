import type { FastifyRequest } from 'fastify';
export type FastifyProfileType = 'ENTITY' | 'USER';
export type FastifyStateI = {
  profileId?: string;
  sessionId?: string;
  profileType?: FastifyProfileType;
  entityProfileId?: string;
};
export type FastifyRequestI<
  Q = unknown, // Query string type (defaults to unknown)
  P = unknown, // Params type
  B = unknown, // Body type
> = FastifyRequest &
  FastifyStateI & {
    query: Q;
    params: P;
    body: B;
  };
