export interface NewsItemI {
  id: string;
  title: string;
  link: string;
  publishedDate: string;
  provider: {
    id: string;
    name: string;
    baseUrl: string;
  };
  scrapedAt: string;
  cursorId: string;
  topics: NewsTopicI[];
}

export interface NewsTopicI {
  id: string;
  name: string;
  slug: string;
  description: string;
}

export interface NewsFetchManyResultI {
  id: string;
  title: string;
  link: string;
  publishedDate: string;
  scrapedAt: Date;
  cursorId: string;
  NewsProvider: {
    id: string;
    name: string;
  };
  NewsTopicMapping: {
    Topic: {
      id: string;
      name: string;
      slug: string;
    };
  }[];
}

export interface NewsFetchManySQLI {
  id: string;
  title: string;
  link: string;
  publishedDate: string;
  scrapedAt: string;
  cursorId: string;
  providerId: string;
  providerName: string;
  providerBaseUrl: string;
  topics: string[];
}
