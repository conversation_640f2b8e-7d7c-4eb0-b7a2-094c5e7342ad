import { IdentityPatchBodyI, IdentityPostBodyI } from '@schemas/document/identity';
import { Prisma } from '@prisma/postgres';
import type { Country, Identity } from '@prisma/postgres';
import { DocumentExpiryStatusI } from '@consts/document/common';
import { IdNameDataTypeI } from '@interfaces/common/data';
export type IdentityCreateOneParamsI = IdentityPostBodyI;

export type IdentityForExternalClientI = Pick<Identity, 'id' | 'fromDate' | 'untilDate' | 'type'> & {
  country: Pick<Country, 'name'>;
  expiryStatus: DocumentExpiryStatusI;
};

export type IdentityCreateForClientI = Pick<Identity, 'id' | 'fileUrl'> & {
  expiryStatus: DocumentExpiryStatusI;
};
export type IdentityUpdateForClientI = Pick<Identity, 'id' | 'fileUrl'> & {
  expiryStatus: DocumentExpiryStatusI;
};
export type IdentityForInternalClientI = Pick<
  Identity,
  'id' | 'documentNo' | 'fromDate' | 'untilDate' | 'type' | 'fileUrl'
> & {
  country: Pick<Country, 'iso2' | 'name'>;
  expiryStatus: DocumentExpiryStatusI;
  documentType: IdNameDataTypeI
};
export type IdentityUpdateOneI = IdentityPatchBodyI;
export type IdentityUpdateOneDataI = Prisma.XOR<Prisma.IdentityUpdateInput, Prisma.IdentityUncheckedUpdateInput>;
