import { DBDataTypeI } from '@consts/common/data';
import { EntityBenefitRawData, Prisma } from '@prisma/postgres';

export type EntityBenefitClientI = EntityBenefitRawData & {
  dataType: DBDataTypeI;
};
export type EntityBenefitNestedClientI = Pick<EntityBenefitRawData, 'id' | 'title'> & {
  dataType: DBDataTypeI;
};
export type EntityBenefitPaginatedClientI = EntityBenefitNestedClientI & {
  cursorId: number;
};

export type EntityBenefitRawQueryFetchsertResultI = {
  id: string;
  title: string;
  dataType: DBDataTypeI;
};
export type EntityBenefitModuleFetchsertParamsI = Pick<Prisma.EntityBenefitRawDataCreateInput, 'title'>;
