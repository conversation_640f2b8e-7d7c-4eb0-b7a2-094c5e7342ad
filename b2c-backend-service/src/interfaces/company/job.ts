import { IdLabelCountDataTypeI } from '@interfaces/common/data';

export type JobFiltersParamsI = {
  locations: IdLabelCountDataTypeI[];
  designations: IdLabelCountDataTypeI[];
  shipTypes: IdLabelCountDataTypeI[];
  internetLimits: IdLabelCountDataTypeI[]
};

export type PostedJobsFiltersParamsI = {
  designations: IdLabelCountDataTypeI[];
  shipTypes: IdLabelCountDataTypeI[];
};

export type ApplicantFilterForEntityMemberParamsI = {
  locations: IdLabelCountDataTypeI[];
  designations: IdLabelCountDataTypeI[];
  yearsOfExperiences: IdLabelCountDataTypeI[];
};
