import { DBDataTypeI } from '@consts/common/data';
import { Optional } from '@interfaces/common/data';
import { Entity, Prisma } from '@prisma/postgres';

export type EntityClientI = Entity & {
  dataType: DBDataTypeI;
};
export type EntityModuleFetchsertParamsI = Optional<
  Pick<Prisma.EntityRawDataCreateInput, 'name' | 'type' | 'website'>,
  'website'
>;
export type EntityFilterI = Pick<Entity, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
export type EntityNestedClientI = Pick<Entity, 'id' | 'name' | 'type'> & {
  dataType: DBDataTypeI;
};

export type EntityGlobalSearchNestedClientI = Pick<Entity, 'id' | 'name' | 'type'> & {
  dataType: DBDataTypeI;
  entityProfileId: string | null;
};

export type EntityNestedExternalI = Pick<Entity, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};
