import type { DocumentTypeClientI } from '@interfaces/document/documentType';

export interface JobDocumentRequirementI {
  id?: string;
  documentTypeId?: string;
  documentTypeRawDataId?: string;
  countries?: string[];
  isMandatory: boolean;
  description?: string;
  sequence?: number;
  DocumentType?: DocumentTypeClientI;
  DocumentTypeRawData?: DocumentTypeClientI;
}

export interface JobDocumentRequirementCreateI {
  documentTypeId?: string;
  documentTypeRawDataId?: string;
  countries?: string[];
  isMandatory: boolean;
  description?: string;
  sequence?: number;
}