import { EntityProfile } from '@prisma/postgres';

export type EntityTabsClientI = {
  peopleTab: boolean;
  alumniTab: boolean;
  jobPostingTab: boolean;
};

export type EntityProfileForExternalClient = Pick<EntityProfile, 'id' | 'name' | 'avatar'> & {
  role?: string;
};

export type EntityProfileForClient = Array<
  Pick<EntityProfile, 'id' | 'name' | 'avatar'> & {
    role?: string;
  }
>;

export type EntityProfileBasicDetails = Pick<
  EntityProfile,
  'id' | 'description' | 'overview' | 'website' | 'foundedAt'
>;

export type EntityProfileAboutForExternalClient = Pick<
  EntityProfile,
  'name' | 'avatar' | 'description' | 'overview' | 'foundedAt' | 'website'
> & {
  entityProfileId: string;
  isFollowing: boolean;
};

export type EntityProfileExternalI = Pick<EntityProfile, 'id' | 'name' | 'avatar'> & {
  cursorId?: number;
  isFollowing?: boolean;
};

export type EntityProfileDataI = Pick<EntityProfile, 'id' | 'name' | 'avatar'>;
