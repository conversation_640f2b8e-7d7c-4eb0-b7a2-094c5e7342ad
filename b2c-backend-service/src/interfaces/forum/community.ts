import { MemberTypeI } from '@consts/forum/member';
import { CommunityRequestStatusI } from '@consts/forum/request';

export type FetchCommunityClientI = {
  id: string;
  cursorId: string;
  name: string;
  description: string;
  access: string;
  isRestricted: boolean;
  memberCount: number;
  questionCount: number;
  avatar: string | null;
  role?: MemberTypeI;
  requestStatus?: CommunityRequestStatusI | null;
  CommunityMember: Array<{
    profileId: string;
    type: MemberTypeI;
  }>;
};
export type CommunityFetchOneForExternalClientResultI = {
  id: string;
};
