import { FastifyProfileType } from '@interfaces/common/declaration';
import type { PostForNotificationI } from '@interfaces/feed/post';
import type { EntityProfile, Profile } from '@prisma/postgres';

export type NotificationFetchManyResultItemI = (
  | Pick<Profile, 'id' | 'name' | 'avatar'>
  | Pick<EntityProfile, 'id' | 'name' | 'avatar'>
) & {
  type: FastifyProfileType;
};
export type NotificationFetchManyResultI = {
  profiles: NotificationFetchManyResultItemI[];
  posts: PostForNotificationI[];
};
