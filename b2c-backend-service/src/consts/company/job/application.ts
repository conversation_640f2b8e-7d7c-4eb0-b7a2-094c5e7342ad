import { z } from 'zod';
import { ApplicationStatus } from '@prisma/postgres';
export namespace ApplicationE {
  // Zod schema using the imported enum values
  export const Status = z.enum([
    ApplicationStatus.PENDING,
    ApplicationStatus.WITHDREW,
    ApplicationStatus.SHORTLISTED,
    ApplicationStatus.REJECTED_BY_ENTITY,
    ApplicationStatus.OFFERED,
    ApplicationStatus.ACCEPTED_BY_APPLICANT,
  ]);

  // TypeScript type derived from the Zod schema
  export type StatusI = z.infer<typeof Status>;
}
