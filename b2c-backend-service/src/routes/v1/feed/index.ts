import { FastifyInstance } from 'fastify';
import postRoutes from './post';
import reactionRoutes from './reaction';
import commentRoutes from './comment';
import entityPostRoutes from './entityPost';
import savedPostRoutes from './savedPost';

const feedRoutes = (fastify: FastifyInstance): void => {
  fastify.register(postRoutes);
  fastify.register(reactionRoutes);
  fastify.register(commentRoutes);
  fastify.register(entityPostRoutes);
  fastify.register(savedPostRoutes);
};

export default feedRoutes;
