import { HttpStatus } from '@consts/common/api/status';
import { FastifyInstance, FastifyReply } from 'fastify';
import type { SavedPostFetchManyI } from '@schemas/feed/savedPost';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import FeedModule from '@modules/feed';
import {
  SavedPostCreateSchema,
  SavedPostDeleteSchema,
  SavedPostFetchManyQuerySchema,
  SavedPostFetchManySchema,
} from '@schemas/feed/savedPost';

const savedPostRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/feed/saved-post', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const bodyToParse = typeof request.body === 'string' ? JSON.parse(request.body) : request.body;
    const { data: bodyData, error: bodyError } = SavedPostCreateSchema.safeParse(bodyToParse);
    if (bodyError) {
      throw new AppError('SAVEDPOST005', bodyError);
    }
    const result = await FeedModule.SavedPostModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });

  fastify.delete('/backend/api/v1/feed/saved-post', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const bodyToParse = typeof request.body === 'string' ? JSON.parse(request.body) : request.body;
    const { data: bodyData, error: bodyError } = SavedPostDeleteSchema.safeParse(bodyToParse);
    if (bodyError) {
      throw new AppError('SAVEDPOST006', bodyError);
    }
    const result = await FeedModule.SavedPostModule.deleteOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/feed/saved-posts', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = SavedPostFetchManyQuerySchema.safeParse(request.query);
    if (queryError) {
      console.log(request.query);
      throw new AppError('SAVEDPOST007', queryError);
    }
    const { error, data } = SavedPostFetchManySchema.safeParse({
      pagination: {
        cursorId: queryData?.cursorId,
        pageSize: queryData.pageSize,
      },
    } as SavedPostFetchManyI);
    if (error) {
      throw new AppError('SAVEDPOST008', { error: error });
    }
    const result = await FeedModule.SavedPostModule.fetchMany(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default savedPostRoutes;
