import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import {
  EntityProfilePostFetchManyI,
  EntityProfilePostFetchManyQuerySchema,
  EntityProfilePostFetchManySchema,
} from '@schemas/feed/entityPost';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import FeedModule from '@modules/feed';

const entityPostRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/feed/entity-profile/posts',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityProfilePostFetchManyQuerySchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('POST009', queryError);
      }
      const { error, data } = EntityProfilePostFetchManySchema.safeParse({
        entityProfileId: queryData.entityProfileId,
        pagination: {
          cursorId: queryData?.cursorId,
          pageSize: queryData.pageSize,
        },
      } as EntityProfilePostFetchManyI);
      if (error) {
        throw new AppError('POST020', { error: error });
      }
      const result = await FeedModule.EntityPostModule.fetchEntityProfilePosts(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default entityPostRoutes;
