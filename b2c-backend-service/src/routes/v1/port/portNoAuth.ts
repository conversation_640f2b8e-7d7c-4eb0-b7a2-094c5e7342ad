import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import { UnLocodeTypeSchema } from '@schemas/port/common';
import { PortFetchSchema } from '@schemas/port/port';
import { pick } from '@utils/data/object';
import type { FastifyInstance, FastifyReply } from 'fastify';

const portNoAuthRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/unsecure/port/port/single/noauth',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = UnLocodeTypeSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('PORT005', queryError);
      }
      const result = await Port.PortModule.fetchByUnLocodeDetailedNoAuth(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.get(
    '/backend/api/v1/unsecure/port/port/search',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = PortFetchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('PORT005', queryError);
      }
      const result = await Port.PortModule.fetchForClientNoAuth(
        queryData.search,
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.get(
    '/backend/api/v1/unsecure/port/port/popular',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const result = await Port.PortModule.fetchPopularPorts(request);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default portNoAuthRoutes;
