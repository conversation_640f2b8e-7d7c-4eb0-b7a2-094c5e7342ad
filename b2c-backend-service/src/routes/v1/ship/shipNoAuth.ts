import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import { ShipImoClientSchema, ShipSearchSchema } from '@schemas/ship/ship';
import type { FastifyInstance, FastifyReply } from 'fastify';

const shipNoAuthRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/unsecure/ship/single/noauth',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = ShipImoClientSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('SHIP005', queryError);
      }
      const result = await Ship.CoreShipModule.fetchByImoDetailedNoAuth(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.get('/backend/api/v1/unsecure/ship/search', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ShipSearchSchema.safeParse(request.query);
    if (error) {
      throw new AppError('SHIP005', error);
    }
    const result = await Ship.CoreShipModule.search(data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/unsecure/ship/popular', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await Ship.CoreShipModule.fetchPopularShipsNoAuth();
    reply.status(HttpStatus.OK).send(result);
  });
};
export default shipNoAuthRoutes;
