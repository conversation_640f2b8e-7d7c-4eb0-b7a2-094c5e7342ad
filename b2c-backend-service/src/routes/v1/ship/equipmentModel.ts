import { HttpStatus } from '@consts/common/api/status';
import {
  EquipmentModelFetchForClientSchema,
  EquipmentModelFetchsertI,
  EquipmentModelFetchsertSchema,
} from '@schemas/ship/equipmentModel';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import Ship from '@modules/ship';
import AppError from '@classes/AppError';

const equipmentModelRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/ship/equipment-model/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error, data: queryData } = EquipmentModelFetchForClientSchema.safeParse(request.query);
      if (error) {
        throw new AppError('PORT005', error);
      }
      const result = await Ship.EquipmentModelModule.fetchForClient(
        pick(queryData, ['search', 'equipmentCategory', 'equipmentManufacturer']),
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/ship/equipment-model/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const body = EquipmentModelFetchsertSchema.parse(request.body);
      const result = await Ship.EquipmentModelModule.fetchsert(body as EquipmentModelFetchsertI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default equipmentModelRoutes;
