import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import NewsModule from '@modules/news';
import { NewsFetchManySchema, NewsTopicFetchAllSchema } from '@schemas/news/news';
import type { FastifyInstance, FastifyReply } from 'fastify';

const newsRoutes = (fastify: FastifyInstance): void => {
  // Get news with pagination and filtering
  fastify.get('/backend/api/v1/news', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = NewsFetchManySchema.safeParse(request.query);

    if (queryError) {
      throw new AppError('NEWS004', queryError);
    }

    const result = await NewsModule.NewsModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });

  // Get all news topics
  fastify.get('/backend/api/v1/news/topics', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError } = NewsTopicFetchAllSchema.safeParse(request.query);

    if (queryError) {
      throw new AppError('NEWS005', queryError);
    }

    const result = await NewsModule.NewsTopicModule.fetchAll();
    reply.status(HttpStatus.OK).send({ data: result });
  });
};

export default newsRoutes;
