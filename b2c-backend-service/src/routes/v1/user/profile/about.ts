import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import User from '@modules/user';
import { RouteParamsSchema } from '@schemas/common/common';
import type { FastifyInstance, FastifyReply } from 'fastify';

const aboutRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/user/profile/about', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = RouteParamsSchema.safeParse(request.query);
    if (error) {
      throw new AppError('PFL011', error);
    }
    const result = await User.ProfileModule.fetchAbout(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default aboutRoutes;
