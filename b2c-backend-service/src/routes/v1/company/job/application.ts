import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { Application } from '@schemas/company/job/application';
import { RouteParamsSchema } from '@schemas/common/common';

const jobApplicationRoutes = (fastify: FastifyInstance): void => {
  fastify.post(
    '/backend/api/v1/company/job/applications/entity-member',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Application.FetchManyForEntityMemberQuerySchema.safeParse(
        request.query,
      );
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const { error: bodyError, data: bodyData } = Application.FetchManyForEntityMemberBodySchema.safeParse(
        request.body,
      );
      if (bodyError) {
        throw new AppError('JOB002', bodyError);
      }
      const result = await Company.ApplicationModule.fetchManyForEntityMember(request, queryData, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/jobs/application/applicant',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Application.FetchManyForApplicantQuerySchema.safeParse(
        request.query,
      );
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const { error: bodyError, data: bodyData } = Application.FetchManyForApplicantBodySchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('JOB002', bodyError);
      }
      const result = await Company.ApplicationModule.fetchManyForApplicant(request, queryData, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/job/application/applicant',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = Application.UpsertOneForApplicantSchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('JOB009', bodyError);
      }
      const result = await Company.ApplicationModule.upsertOneForApplicant(request, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.patch(
    '/backend/api/v1/company/job/application/entity-member/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('JOB010', paramsError);
      }
      const { error: bodyError, data: bodyData } = Application.UpdateOneForEntityMemberSchema.safeParse({
        ...(request.body as Application.UpdateOneForEntityMemberI),
        ...paramsData,
      });
      if (bodyError) {
        throw new AppError('JOB009', bodyError);
      }
      const result = await Company.ApplicationModule.updateOneForEntityMember(request, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/job/applicant/filters',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Application.FetchFiltersForApplicantSchema.safeParse(
        request.query,
      );
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const result = await Company.ApplicationModule.fetchFiltersForApplicant(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/job/entity-member/filters',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = Application.FetchFiltersForEntityMemberQuerySchema.safeParse(
        request.query,
      );
      if (queryError) {
        throw new AppError('JOB003', queryError);
      }
      const result = await Company.ApplicationModule.fetchFiltersForEntityMember(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default jobApplicationRoutes;
