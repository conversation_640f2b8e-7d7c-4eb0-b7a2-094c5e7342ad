import { FastifyInstance } from 'fastify';
import departmentRoutes from './department/department';
import designationRoutes from './designation/designation';
import entityRoutes from './entity/options';
import certificateCourseRoutes from './certificateCourse/certificateCourse';
import degreeRoutes from './degree/degree';
import skillRoutes from './skill/options';
import entityMemberRoutes from './entity/member';
import entityProfileRoutes from './entity/profile';
import entityRequestRoutes from './entity/entityRequest';
import entityPeopleRoutes from './entity/people';
import jobApplicationRoutes from './job/application';
import jobRoutes from './job/job';
import entityNetworkRoutes from './entity/network';

const companyRoutes = (fastify: FastifyInstance): void => {
  fastify.register(certificateCourseRoutes);
  fastify.register(degreeRoutes);
  fastify.register(departmentRoutes);
  fastify.register(designationRoutes);
  fastify.register(entityRoutes);
  fastify.register(entityMemberRoutes);
  fastify.register(entityPeopleRoutes);
  fastify.register(entityProfileRoutes);
  fastify.register(entityRequestRoutes);
  fastify.register(jobApplicationRoutes);
  fastify.register(jobRoutes);
  fastify.register(entityNetworkRoutes);
  fastify.register(skillRoutes);
};

export default companyRoutes;
