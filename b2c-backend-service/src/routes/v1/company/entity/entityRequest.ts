import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';

import CompanyModule from '@modules/company';
import {
  EntityRequestBodySchema,
  EntityRequestApprovalQuerySchema,
  EntityRequestRejectionQuerySchema,
  EntityRequestRevocationQuerySchema,
} from '@schemas/company/entityRequest';
import type { FastifyInstance, FastifyReply } from 'fastify';

const entityRequestRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/company/entity-request', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = EntityRequestBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('GEN002', bodyError);
    }
    const result = await CompanyModule.EntityRequestModule.createOne(bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.post(
    '/backend/api/v1/company/entity-request-approval',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = EntityRequestApprovalQuerySchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      const result = await CompanyModule.EntityRequestModule.approveRequest(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.delete(
    '/backend/api/v1/company/entity-request-rejection',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = EntityRequestRejectionQuerySchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      await CompanyModule.EntityRequestModule.rejectRequest(request, queryData);
      reply.status(HttpStatus.NO_CONTENT).send();
    },
  );

  fastify.post(
    '/backend/api/v1/company/entity-request-revocation',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = EntityRequestRevocationQuerySchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN002', queryError);
      }
      await CompanyModule.EntityRequestModule.revokeRequest(queryData);
      reply.status(HttpStatus.NO_CONTENT).send();
    },
  );
};

export default entityRequestRoutes;
