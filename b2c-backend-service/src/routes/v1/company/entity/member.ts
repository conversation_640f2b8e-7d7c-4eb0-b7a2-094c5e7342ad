import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import {
  EntityMemberDeleteSchema,
  EntityMemberFetchSchema,
  EntityMemberUpsertSchema,
} from '@schemas/company/entityMember';

const entityMemberRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/company/entity-member', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = EntityMemberFetchSchema.safeParse(request.query);
    if (queryError) throw new AppError('ENT005', queryError);

    const result = await Company.EntityMemberModule.fetchMembers(queryData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/company/entity-member', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = EntityMemberUpsertSchema.safeParse(request.body);
    if (bodyError) throw new AppError('ENT006', bodyError);

    const result = await Company.EntityMemberModule.upsertMember(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.delete('/backend/api/v1/company/entity-member', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = EntityMemberDeleteSchema.safeParse(request.query);
    if (queryError) throw new AppError('ENT005', queryError);

    await Company.EntityMemberModule.deleteMember(request, queryData);
    reply.status(HttpStatus.NO_CONTENT).send();
  });
};

export default entityMemberRoutes;
