import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import {
  EntityAboutUpdateSchema,
  EntityCreateOneParamsSchema,
  EntityProfileIdSchema,
  EntityTabsFetchSchema,
  EntityTabsUpdateSchema,
  VerifyOTPForEntityProfileVerificationSchema,
} from '@schemas/company/entityProfile';
import { SessionUpdateProfileTypeParamsSchema } from '@schemas/auth/session';

const entityProfileRoutes = (fastify: FastifyInstance): void => {
  fastify.patch('/backend/api/v1/company/entity-profile', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = EntityAboutUpdateSchema.safeParse(request.body);
    console.log('body= ', bodyError);
    if (bodyError) throw new AppError('ENT006', bodyError);

    await Company.EntityProfileModule.updateBasicDetails(request, bodyData);
    reply.status(HttpStatus.NO_CONTENT);
  });
  fastify.get(
    '/backend/api/v1/company/entity-profile/tabs',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityTabsFetchSchema.safeParse(request.query);
      if (queryError) throw new AppError('ENT005');

      const result = await Company.EntityProfileModule.fetchTabs(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.patch(
    '/backend/api/v1/company/entity-profile/tabs',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = EntityTabsUpdateSchema.safeParse(request.body);
      if (bodyError) throw new AppError('ENT006');

      await Company.EntityProfileModule.updateTabs(request, bodyData);
      reply.status(HttpStatus.NO_CONTENT).send();
    },
  );

  fastify.post('/backend/api/v1/company/entity-profile', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = EntityCreateOneParamsSchema.safeParse(request.body);
    if (bodyError) throw new AppError('ENT006');

    const result = await Company.EntityProfileModule.createOne(bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.post(
    '/backend/api/v1/company/entity-profile/email/otp',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = EntityProfileIdSchema.safeParse(request.body);
      if (bodyError) throw new AppError('ENT006');
      await Company.EntityProfileModule.sendOTPForEntityProfileEmailVerification(bodyData);
      reply.status(HttpStatus.CREATED);
    },
  );

  fastify.patch(
    '/backend/api/v1/company/entity-profile/email/otp',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = VerifyOTPForEntityProfileVerificationSchema.safeParse(request.body);
      if (bodyError) throw new AppError('ENT006');
      await Company.EntityProfileModule.verifyOTPForEntityProfileEmailVerification(bodyData);
      reply.status(HttpStatus.CREATED);
    },
  );

  fastify.get(
    '/backend/api/v1/company/entity-profile/about',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityProfileIdSchema.safeParse(request.query);
      if (queryError) throw new AppError('ENT005');
      const result = await Company.EntityProfileModule.fetchEntityProfileAbout(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.patch('/backend/api/v1/auth/session/profile-type', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = SessionUpdateProfileTypeParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('AUTH007', error);
    }
    await Company.EntityProfileModule.updateProfileType(request, data);
    reply.status(HttpStatus.OK);
  });

  fastify.get('/backend/api/v1/company/entity-profile', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = EntityProfileIdSchema.safeParse(request.query);
    if (queryError) throw new AppError('ENT005');
    const result = await Company.EntityProfileModule.fetchEntityProfileBasicDetails(queryData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/company/entity-profiles', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await Company.EntityProfileModule.fetchEntityProfilesForClient(request);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default entityProfileRoutes;
