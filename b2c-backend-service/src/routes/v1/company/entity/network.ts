import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import {
  EntityProfileFollowOneParamsSchema,
  EntityProfileUnFollowOneParamsSchema,
} from '@schemas/company/entityNetwork';
import { EntityProfileIdCursorPaginationSchema } from '@schemas/company/entityProfile';

const entityNetworkRoutes = (fastify: FastifyInstance): void => {
  fastify.post(
    '/backend/api/v1/company/entity-profile/follow',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityProfileFollowOneParamsSchema.safeParse(request.body);
      if (queryError) {
        throw new AppError('ENT005');
      }
      await Company.EntityNetworkModule.followOne(request, queryData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
  fastify.delete(
    '/backend/api/v1/company/entity-profile/unfollow',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityProfileUnFollowOneParamsSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('ENT005', queryError);
      }
      await Company.EntityNetworkModule.unfollowOne(request, queryData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
  fastify.get('/backend/api/v1/company/network/followers', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = EntityProfileIdCursorPaginationSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FLW008', error);
    }
    const result = await Company.EntityNetworkModule.fetchManyFollowers(request, data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/company/network/followings', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = EntityProfileIdCursorPaginationSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FLW008', error);
    }
    const result = await Company.EntityNetworkModule.fetchManyFollowings(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default entityNetworkRoutes;
