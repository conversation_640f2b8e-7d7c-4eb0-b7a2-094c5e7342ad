import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { EntityBenefitModuleFetchsertParamsI } from '@interfaces/company/entityBenefit';
import Company from '@modules/company';
import { CursorPaginationSearchSchema } from '@schemas/common/common';
import { EntityBenefitTitleSchema } from '@schemas/company/entityBenefit';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const entityBenefitRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/entity-benefit/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = CursorPaginationSearchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('CRTCR005', queryError);
      }
      const result = await Company.EntityBenefitModule.fetchForClient(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/entity-benefit/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = EntityBenefitTitleSchema.safeParse(request.body);

      if (bodyError) {
        throw new AppError('CRTCR006', bodyError);
      }
      const result = await Company.EntityBenefitModule.fetchsert(bodyData as EntityBenefitModuleFetchsertParamsI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default entityBenefitRoutes;
