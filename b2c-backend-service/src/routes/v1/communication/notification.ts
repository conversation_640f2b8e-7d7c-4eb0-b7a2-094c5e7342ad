import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import CommunicationModule from '@modules/communication';
import { NotificationFetchManySchema } from '@schemas/communication/notification';
import type { FastifyInstance, FastifyReply } from 'fastify';

const notificationRoutes = (fastify: FastifyInstance): void => {
  fastify.post(
    '/backend/api/v1/communication/notifications',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data: bodyData } = NotificationFetchManySchema.safeParse(request.body);

      if (error) {
        throw new AppError('NF010');
      }
      const result = await CommunicationModule.NotificationModule.fetchMany(request, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default notificationRoutes;
