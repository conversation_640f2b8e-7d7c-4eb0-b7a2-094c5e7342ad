import AppError from "@classes/AppError";
import { HttpStatus } from "@consts/common/api/status";

import { DocumentTypeCreateParamsI } from '@interfaces/document/documentType';
import Document from '@modules/document';
import {
    DocumentTypeOptionsFetchSchema,
    DocumentTypeAllOptionsFetchSchema,
    DocumentTypeCreateSchema
} from '@schemas/document/documentType';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const documentTypeRoutes = (fastify: FastifyInstance): void => {
    fastify.get(
        '/backend/api/v1/document/document-type/identity/options',
        {},
        async (request: FastifyRequest, reply: FastifyReply) => {
            const { error: queryError, data: queryData } = DocumentTypeOptionsFetchSchema.safeParse(request.query);
            if (queryError) {
                throw new AppError('DOC006', queryError);
            }
            const result = await Document.DocumentTypeModule.fetchForClient(
                queryData.search,
                'IDENTITY',
                pick(queryData, ['page', 'pageSize']),
            );
            reply.status(HttpStatus.OK).send(result);
        },
    );

    fastify.get(
        '/backend/api/v1/document/document-type/visa/options',
        {},
        async (request: FastifyRequest, reply: FastifyReply) => {
            const { error: queryError, data: queryData } = DocumentTypeOptionsFetchSchema.safeParse(request.query);
            if (queryError) {
                throw new AppError('DOC006', queryError);
            }
            const result = await Document.DocumentTypeModule.fetchForClient(
                queryData.search,
                'VISA',
                pick(queryData, ['page', 'pageSize']),
            );
            reply.status(HttpStatus.OK).send(result);
        },
    );

    fastify.get(
        '/backend/api/v1/document/document-type/all/options',
        {},
        async (request: FastifyRequest, reply: FastifyReply) => {
            const { error: queryError, data: queryData } = DocumentTypeAllOptionsFetchSchema.safeParse(request.query);
            if (queryError) {
                throw new AppError('DOC006', queryError);
            }
            const result = await Document.DocumentTypeModule.fetchForClient(
                queryData.search,
                undefined,
                pick(queryData, ['page', 'pageSize']),
            );
            reply.status(HttpStatus.OK).send(result);
        },
    );

    fastify.post(
        '/backend/api/v1/document/document-type/options',
        {},
        async (request: FastifyRequest, reply: FastifyReply) => {
            const { error: bodyError, data: bodyData } = DocumentTypeCreateSchema.safeParse(request.body);

            if (bodyError) {
                throw new AppError('DOC007', bodyError);
            }
            const result = await Document.DocumentTypeModule.fetchsert(
                bodyData as DocumentTypeCreateParamsI,
            );
            reply.status(HttpStatus.OK).send(result);
        },
    );
};

export default documentTypeRoutes;
