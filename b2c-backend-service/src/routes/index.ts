import { FastifyInstance } from 'fastify';
import unsecureRoutes from './unsecureRoutes';
import secureRoutes from './secureRoutes';
import fastifyRateLimit from '@fastify/rate-limit';
import { rateLimitConfig } from '@config/rateLimit';

const routes = async (fastify: FastifyInstance): Promise<void> => {
  await fastify.register(async function secureRoutesGroup(fastify: FastifyInstance) {
    await fastify.register(fastifyRateLimit, rateLimitConfig.secure);
    fastify.register(secureRoutes);
  });

  await fastify.register(async function unsecuredRoutesGroup(fastify: FastifyInstance) {
    await fastify.register(fastifyRateLimit, rateLimitConfig.unsecure);
    fastify.register(unsecureRoutes);
  });
};

export default routes;
