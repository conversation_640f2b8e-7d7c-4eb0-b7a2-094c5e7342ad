import { Client } from '@elastic/elasticsearch';

const client = new Client({
  node: 'https://elasticsearch.navicater.com',
  auth: {
    username: 'elastic',
    password: 'DBI4-IiSjMHSohWqUDM9',
  },
  tls: {
    rejectUnauthorized: false,
  },
});

export interface Document {
  id?: string;
  [key: string]: unknown;
}

export interface SearchQuery {
  query?: Record<string, unknown>;
  sort?: Record<string, unknown>[];
  size?: number;
  from?: number;
  track_total_hits?: boolean;
}

export interface SearchHit {
  _id: string;
  _score: number;
  _source: Record<string, unknown>;
}

export interface SearchResponse {
  hits: {
    hits: SearchHit[];
    total?: {
      value: number;
      relation: string;
    };
  };
}

export interface SearchResult {
  id: string;
  score: number;
  [key: string]: unknown;
}

export interface GetResponse {
  _id: string;
  _source: Record<string, unknown>;
}

export const insertDocument = async (index: string, document: Document): Promise<string> => {
  const { id, ...docWithoutId } = document;
  const response = await client.index({
    index,
    id,
    document: docWithoutId,
  });
  return response._id;
};

export const insertDocuments = async (index: string, documents: Document[]): Promise<void> => {
  const operations = documents.flatMap((doc) => {
    const { id, ...docWithoutId } = doc;
    return [{ index: { _index: index, _id: id } }, docWithoutId];
  });

  await client.bulk({ operations });
};

export const searchDocuments = async (
  index: string,
  query: SearchQuery = {},
): Promise<SearchResult[] & { total?: { value: number } }> => {
  const response = (await client.search({
    index,
    ...query,
  })) as SearchResponse;

  const results = response.hits.hits.map((hit: SearchHit) => ({
    id: hit._id,
    score: hit._score,
    ...hit._source,
  }));

  return Object.assign(results, { total: response.hits.total });
};

export const searchByText = async (
  index: string,
  text: string,
  fields: string[] = ['*'],
  size = 10,
  from = 0,
  countonly = false,
): Promise<SearchResult[] | number> => {
  if (countonly) {
    const result = await searchDocuments(index, {
      query: {
        multi_match: {
          query: text,
          fields,
          type: 'best_fields',
        },
      },
      size: 0,
      track_total_hits: true,
    });
    return result.total?.value || 0;
  }

  return searchDocuments(index, {
    query: {
      multi_match: {
        query: text,
        fields,
        type: 'best_fields',
      },
    },
    size,
    from,
  });
};

export const searchByField = async (index: string, field: string, value: unknown): Promise<SearchResult[]> => {
  return searchDocuments(index, {
    query: {
      term: {
        [field]: value,
      },
    },
  });
};

export const fuzzySearch = async (index: string, field: string, value: string): Promise<SearchResult[]> => {
  return searchDocuments(index, {
    query: {
      fuzzy: {
        [field]: {
          value,
          fuzziness: 'AUTO',
        },
      },
    },
  });
};

export const getDocument = async (index: string, id: string): Promise<Record<string, unknown> & { id: string }> => {
  const response = (await client.get({
    index,
    id,
  })) as GetResponse;

  return {
    id: response._id,
    ...response._source,
  };
};

export const updateDocument = async (index: string, id: string, updates: Partial<Document>): Promise<void> => {
  await client.update({
    index,
    id,
    doc: updates,
  });
};

export const deleteDocument = async (index: string, id: string): Promise<void> => {
  await client.delete({
    index,
    id,
  });
};

export const createIndex = async (index: string, mapping?: Record<string, unknown>): Promise<void> => {
  const exists = await client.indices.exists({ index });

  if (!exists) {
    await client.indices.create({
      index,
      ...(mapping && { mappings: mapping }),
    });
  }
};

export const deleteIndex = async (index: string): Promise<void> => {
  await client.indices.delete({ index });
};

export const refreshIndex = async (index: string): Promise<void> => {
  await client.indices.refresh({ index });
};
