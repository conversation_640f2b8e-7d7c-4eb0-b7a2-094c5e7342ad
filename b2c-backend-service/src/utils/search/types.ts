import {
  SearchHit as ElasticsearchSearchHit,
  SearchResponse as ElasticsearchSearchResponse,
} from '@elastic/elasticsearch/lib/api/types';

export interface Document {
  id?: string;
  [key: string]: unknown;
}

export interface SearchQuery {
  query?: Record<string, unknown>;
  sort?: Record<string, unknown>[];
  size?: number;
  from?: number;
}

export type SearchHit = ElasticsearchSearchHit<Record<string, unknown>>;

export type SearchResponse = ElasticsearchSearchResponse<Record<string, unknown>>;

export interface SearchResult {
  id: string;
  score?: number;
  [key: string]: unknown;
}

export interface PaginatedSearchResult {
  results: SearchResult[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface GetResponse {
  _id: string;
  _source: Record<string, unknown>;
}

export interface SearchOptions {
  page?: number;
  limit?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AggregationBucket {
  key: string | number;
  doc_count: number;
  [key: string]: unknown;
}

export interface AggregationResult {
  buckets?: AggregationBucket[];
  value?: number;
  doc_count?: number;
  [key: string]: unknown;
}

export interface HighlightResult {
  [field: string]: string[];
}

export interface SearchHitWithHighlight extends SearchHit {
  highlight?: HighlightResult;
}

export interface AdvancedSearchParams {
  text?: string;
  fields?: string[];
  filters?: Record<string, unknown>;
  mustNot?: Record<string, unknown>[];
  should?: Record<string, unknown>[];
  minimumShouldMatch?: number;
}

export interface HighlightSearchParams {
  text: string;
  fields?: string[];
  highlightFields?: string[];
}

export interface BulkUpdateItem {
  id: string;
  doc: Partial<Document>;
}

export interface DocumentWithId extends Record<string, unknown> {
  id: string;
}

export interface BoolQuery {
  must: Record<string, unknown>[];
  must_not: Record<string, unknown>[];
  should: Record<string, unknown>[];
  minimum_should_match?: number;
}
