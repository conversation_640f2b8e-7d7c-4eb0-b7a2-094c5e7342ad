import { UUIDI } from '@schemas/common/common';

export namespace JobUtil {
  export namespace Sql {
    export const getMatching = (selfProfileId: UUIDI) => {
      return `
     COALESCE(
        a."matching",
        (
          -- Designation match
          CASE
            WHEN EXISTS(
              SELECT 1
              FROM "career"."ExperienceDesignation" xdsg
              LEFT JOIN "company"."DesignationAlternative" nestedDsg
                ON nestedDsg."id" = xdsg."designationAlternativeId"
              WHERE xdsg."profileId" = ${selfProfileId}
                AND (
                  xdsg."designationRawDataId" = j."designationRawDataId"
                  OR nestedDsg."designationId" = dsg."designationId"
                )
            ) THEN 10.0
            ELSE 0.0
          END
          +
          -- Department match
          CASE
            WHEN EXISTS(
              SELECT 1
              FROM "career"."ExperienceShip" xs
              LEFT JOIN "company"."DepartmentAlternative" nestedDep
                ON nestedDep."id" = xs."departmentAlternativeId"
              WHERE xs."profileId" = ${selfProfileId}
                AND (
                  xs."departmentRawDataId" = j."departmentRawDataId"
                  OR nestedDep."departmentId" = dep."departmentId"
                )
            ) THEN 10.0
            ELSE 0.0
          END
          +
          -- Ship match
          CASE
            WHEN EXISTS(
              SELECT 1
              FROM "career"."ExperienceShip" xs
              WHERE xs."profileId" = ${selfProfileId}
                AND (
                  xs."shipImo" = j."shipImo"
                  OR xs."shipRawDataImo" = j."shipRawDataImo"
                )
            ) THEN 10.0
            ELSE 0.0
          END
          +
          -- Years of experience (simplified for now)
          CASE
            WHEN (
              SELECT COALESCE(
                SUM(
                  EXTRACT(YEAR FROM (COALESCE(nestedXDsg."toDate", CURRENT_DATE) - nestedXDsg."fromDate"))
                ),
                0
              )
              FROM "career"."ExperienceDesignation" nestedXDsg
              WHERE nestedXDsg."profileId" = ${selfProfileId}
            ) >= j."minYears" THEN 10.0
            ELSE 0.0
          END
          +
          -- Equipment category match
          CASE
            WHEN EXISTS(
              SELECT 1
              FROM "company"."JobEquipmentCategory" jeCag
              INNER JOIN "career"."ExperienceEquipmentCategory" xeCag
                ON xeCag."profileId" = ${selfProfileId}
                AND (
                  xeCag."equipmentCategoryId" = jeCag."equipmentCategoryId"
                  OR xeCag."equipmentCategoryRawDataId" = jeCag."equipmentCategoryRawDataId"
                )
            ) THEN 5.0
            ELSE 0.0
          END
          +
          -- Equipment manufacturer match
          CASE
            WHEN EXISTS(
              SELECT 1
              FROM "company"."JobEquipmentManufacturer" jeMan
              INNER JOIN "career"."ExperienceEquipmentManufacturer" xeMan
                ON xeMan."profileId" = ${selfProfileId}
                AND (
                  xeMan."equipmentManufacturerId" = jeMan."equipmentManufacturerId"
                  OR xeMan."equipmentManufacturerRawDataId" = jeMan."equipmentManufacturerRawDataId"
                )
            ) THEN 5.0
            ELSE 0.0
          END
          +
          -- Equipment model match
          CASE
            WHEN EXISTS(
              SELECT 1
              FROM "company"."JobEquipmentModel" jeMod
              INNER JOIN "career"."ExperienceEquipmentModel" xeMod
                ON xeMod."profileId" = ${selfProfileId}
                AND (
                  xeMod."equipmentModelId" = jeMod."equipmentModelId"
                  OR xeMod."equipmentModelRawDataId" = jeMod."equipmentModelRawDataId"
                )
            ) THEN 5.0
            ELSE 0.0
          END
        )
      ) AS "matching"
    `;
    };
  }
}
