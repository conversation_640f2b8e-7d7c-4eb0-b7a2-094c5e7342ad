import type { ObjUnknownI, SortOrderI } from '@interfaces/common/data';
import { CreateAndRemoveDataI, MasterAndRawDataI } from '@interfaces/master/common';
import { IdI, IdTypeI, IdTypeOprI } from '@schemas/common/common';

export const sortArrayByString = <T>(arr: T[], key: keyof T, order: SortOrderI): T[] =>
  arr.sort(
    order === 'asc'
      ? (a, b) => String(a[key]).localeCompare(String(b[key]))
      : (a, b) => String(b[key]).localeCompare(String(a[key])),
  );
export const uniqueArrayObj = <T extends { id?: string; [key: string]: unknown }>(data: T[]): T[] => {
  const ids = new Set<string>();
  const filteredArray = data.filter((item) => {
    if (!ids.has(item.id)) {
      ids.add(item.id);
      return true;
    }
    return false;
  });
  return filteredArray;
};

export const uniqueArrayStr = (data: string[]): string[] => {
  const ids = new Set<string>(...data);
  return Array.from(ids);
};
export const separateMasterAndRawData = (data: IdTypeI[]): MasterAndRawDataI => {
  data = uniqueArrayObj(data);
  const result = data.reduce(
    (acc, curr) => {
      if (curr.dataType === 'master') {
        acc.master.push(curr.id);
      } else if (curr.dataType === 'raw') {
        acc.rawData.push(curr.id);
      }
      return acc;
    },
    { master: [], rawData: [] } as MasterAndRawDataI,
  );
  return result;
};

export const separateCreateAndDeleteData = (
  data: IdTypeOprI[],
  existing: (ObjUnknownI & IdI)[],
): CreateAndRemoveDataI => {
  data = uniqueArrayObj(data);

  const existingIdSet = new Set<string>(existing.map(({ id }) => id));

  const result = data.reduce(
    (acc, curr) => {
      if (existingIdSet.has(curr.id)) {
        if (curr.opr === 'DELETE') {
          if (curr.dataType === 'master') {
            acc.remove.master.push(curr.id);
          } else {
            acc.remove.rawData.push(curr.id);
          }
        }
      } else {
        if (curr.opr === 'CREATE') {
          if (curr.dataType === 'master') {
            acc.create.master.push(curr.id);
          } else {
            acc.create.rawData.push(curr.id);
          }
        }
      }
      return acc;
    },
    { create: { master: [], rawData: [] }, remove: { master: [], rawData: [] } } as CreateAndRemoveDataI,
  );
  return result;
};
