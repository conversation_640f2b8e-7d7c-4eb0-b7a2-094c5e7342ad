let nanoidInstance = null;

const getNanoid = async () => {
  if (!nanoidInstance) {
    const { customAlphabet } = await import('nanoid');
    nanoidInstance = customAlphabet('1234567890abcdef', 5);
  }
  return nanoidInstance;
};

export const generateSlug = async (
  text: string,
  options: { maxLength?: number; keepCase?: boolean } = {},
): Promise<string> => {
  const { maxLength = 60, keepCase = false } = options;

  const nanoid = await getNanoid();

  let slug = text
    .toString()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');

  if (!keepCase) {
    slug = slug.toLowerCase();
  }

  if (slug.length > maxLength) {
    slug = slug.substring(0, maxLength).replace(/-+$/, '');
  }

  if (slug.length === 0) {
    return nanoid();
  }

  return `${slug}-${nanoid()}`;
};
