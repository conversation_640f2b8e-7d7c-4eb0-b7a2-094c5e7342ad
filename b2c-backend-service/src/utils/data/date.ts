import { DocumentExpiryStatusI } from '@consts/document/common';

export const addMsToDate = ({ date = new Date(), ms }: { date?: Date; ms: number }) => new Date(date.getTime() + ms);
export const subMsToDate = ({ date = new Date(), ms }: { date?: Date; ms: number }) => new Date(date.getTime() - ms);
export const addDaysToDateOnly = ({ date = new Date(), days }: { date?: Date; days: number }) => {
  date.setDate(date.getDate() + days);
  date.setHours(0, 0, 0, 0);
  return date;
};

export const getCurrentDate = (): Date => new Date();
export const getDateOnly = (date = new Date()): Date => {
  date.setHours(0, 0, 0, 0);
  return date;
};
export const getExpiryStatus = ({
  fromDate,
  untilDate,
}: {
  fromDate: Date;
  untilDate: Date;
}): DocumentExpiryStatusI => {
  if (fromDate > untilDate) {
    return 'EXPIRED';
  } else if (untilDate.getMonth() - fromDate.getMonth() <= 6) {
    return 'EXPIRES_SOON';
  }
  return 'VALID';
};

export const isLessThanEqualDateRange = (startDate, endDate): boolean => startDate <= endDate;

export const getMonthYearDifference = (
  startDate: Date | string,
  endDate?: Date | string,
): { years: number; months: number } => {
  const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const endDateTemp = endDate ? (typeof endDate === 'string' ? new Date(endDate) : endDate) : new Date();
  const startYear = startDateObj.getFullYear();
  const endYear = endDateTemp.getFullYear();
  const startMonth = startDateObj.getMonth();
  const endMonth = endDateTemp.getMonth();

  let years = endYear - startYear;
  let months = endMonth - startMonth;

  if (months < 0) {
    years--;
    months += 12;
  }
  const startDay = startDateObj.getDate();
  const endDay = endDateTemp.getDate();

  if (endDay < startDay) {
    months--;

    if (months < 0) {
      years--;
      months += 12;
    }
  }
  months = Math.max(0, months);

  return { years, months };
};
