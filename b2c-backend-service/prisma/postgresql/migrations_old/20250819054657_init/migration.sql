-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "announcement";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "app";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "auth";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "career";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "company";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "document";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "feed";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "forum";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "leaderboard";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "master";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "network";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "port";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "rawData";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "referral";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "score";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "ship";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "user";

-- CreateEnum
CREATE TYPE "app"."PlatformE" AS ENUM ('android', 'ios', 'web_app');

-- CreateEnum
CREATE TYPE "app"."PolicyTypeE" AS ENUM ('SIGNUP_PRIVACY_POLICY', 'TERMS_OF_USE', 'REFERRAL_TERMS');

-- CreateEnum
CREATE TYPE "auth"."ProfileTypeE" AS ENUM ('USER', 'ENTITY');

-- CreateEnum
CREATE TYPE "company"."JobStatus" AS ENUM ('ACTIVE', 'DELETED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "company"."ApplicationStatus" AS ENUM ('PENDING', 'WITHDREW', 'SHORTLISTED', 'REJECTED_BY_ENTITY', 'OFFERED', 'ACCEPTED_BY_APPLICANT');

-- CreateEnum
CREATE TYPE "feed"."PostFileExtensionE" AS ENUM ('webp', 'jpeg', 'jpg', 'mp4');

-- CreateEnum
CREATE TYPE "forum"."CommunityAccessE" AS ENUM ('GLOBAL', 'PUBLIC', 'PRIVATE');

-- CreateEnum
CREATE TYPE "forum"."ForumFileExtensionE" AS ENUM ('webp', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'mp4');

-- CreateEnum
CREATE TYPE "forum"."AnswerStatusE" AS ENUM ('PROPOSED_SOLUTION', 'VERIFIED_SOLUTION');

-- CreateEnum
CREATE TYPE "forum"."VoteTypeE" AS ENUM ('UPVOTE', 'DOWNVOTE');

-- CreateEnum
CREATE TYPE "forum"."QuestionTypeE" AS ENUM ('NORMAL', 'TROUBLESHOOT');

-- CreateEnum
CREATE TYPE "forum"."MemberTypeE" AS ENUM ('ADMIN', 'MODERATOR', 'CONTRIBUTOR', 'MEMBER');

-- CreateEnum
CREATE TYPE "forum"."CommunityRequestStatusE" AS ENUM ('PENDING', 'ACCEPTED', 'PARTIALLY_ACCEPTED', 'REJECTED', 'REVOKED');

-- CreateEnum
CREATE TYPE "network"."RequestStatusE" AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'REVOKED', 'DISCONNECTED', 'BLOCKED');

-- CreateEnum
CREATE TYPE "network"."FollowTypeE" AS ENUM ('USER', 'ENTITY');

-- CreateEnum
CREATE TYPE "port"."PortStatusE" AS ENUM ('PENDING', 'REJECTED', 'APPROVED', 'SAVED');

-- CreateEnum
CREATE TYPE "port"."ScrapBookPostStatusE" AS ENUM ('ACTIVE', 'DELETED', 'FLAGGED');

-- CreateEnum
CREATE TYPE "port"."ScrapBookReactionTypeE" AS ENUM ('LIKE', 'SUPPORT', 'CELEBRATE', 'FUNNY');

-- CreateEnum
CREATE TYPE "rawData"."StatusRawData" AS ENUM ('PENDING', 'REJECTED', 'APPROVED', 'SAVED');

-- CreateEnum
CREATE TYPE "rawData"."ServiceRawDataStatusE" AS ENUM ('ACTIVE', 'DETAINED', 'SCRAPPED');

-- CreateEnum
CREATE TYPE "rawData"."ShipCapacityUnitTypeRawDataE" AS ENUM ('DEADWEIGHT_TONNAGE', 'NOS', 'LITRE', 'LITRES_PER_HOUR', 'CUBIC_METRE', 'CUBIC_METRE_PER_HOUR', 'TONNES', 'TONNES_PER_HOUR', 'TWENTY_FOOT_EQUIVALENT_UNIT');

-- CreateEnum
CREATE TYPE "rawData"."EntityTypeRawDataE" AS ENUM ('COMPANY', 'EDUCATION', 'GO', 'NGO', 'IGO', 'SMO', 'OTHER');

-- CreateEnum
CREATE TYPE "rawData"."SkillCategoryRawDataE" AS ENUM ('MARITIME', 'OTHER');

-- CreateEnum
CREATE TYPE "rawData"."CertificateCourseRawDataTypeE" AS ENUM ('STATUTORY', 'VALUE_ADDED');

-- CreateEnum
CREATE TYPE "rawData"."PlaceSourceE" AS ENUM ('PROFILE', 'MAPBOX');

-- CreateEnum
CREATE TYPE "score"."ScoreTypeE" AS ENUM ('CONTRIBUTION', 'POST_CONTRIBUTION', 'QNA_ANSWER', 'TROUBLESHOOT_ANSWER', 'INVITE_CONTRIBUTION', 'QNA_ANSWER_VERIFIED', 'QNA_ANSWER_LESS_THAN_TWO_HOURS', 'QNA_ANSWER_TWO_TO_FOUR_HOURS', 'QNA_ANSWER_FOUR_TO_SIX_HOURS', 'QNA_ANSWER_SIX_TO_EIGHT_HOURS', 'QNA_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS', 'TROUBLESHOOT_ANSWER_VERIFIED', 'TROUBLESHOOT_ANSWER_LESS_THAN_TWO_HOURS', 'TROUBLESHOOT_ANSWER_TWO_TO_FOUR_HOURS', 'TROUBLESHOOT_ANSWER_FOUR_TO_SIX_HOURS', 'TROUBLESHOOT_ANSWER_SIX_TO_EIGHT_HOURS', 'TROUBLESHOOT_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS');

-- CreateEnum
CREATE TYPE "ship"."ServiceStatusE" AS ENUM ('ACTIVE', 'DETAINED', 'SCRAPPED');

-- CreateEnum
CREATE TYPE "ship"."ShipStatusE" AS ENUM ('PENDING', 'REJECTED', 'APPROVED', 'SAVED');

-- CreateEnum
CREATE TYPE "ship"."ShipContributionLabelE" AS ENUM ('mmsi', 'callSign', 'name', 'flagCountryIso2', 'generalVesselType', 'otherVesselType', 'status', 'portOfRegistry', 'yearBuilt');

-- CreateEnum
CREATE TYPE "ship"."ShipCapacityUnitTypeE" AS ENUM ('DEADWEIGHT_TONNAGE', 'NOS', 'LITRE', 'LITRES_PER_HOUR', 'CUBIC_METRE', 'CUBIC_METRE_PER_HOUR', 'TONNES', 'TONNES_PER_HOUR', 'TWENTY_FOOT_EQUIVALENT_UNIT');

-- CreateEnum
CREATE TYPE "user"."GenderE" AS ENUM ('MALE', 'FEMALE', 'OTHER');

-- CreateEnum
CREATE TYPE "user"."ProfileStatusE" AS ENUM ('ACTIVE', 'INACTIVE', 'SCHEDULED_FOR_DELETION', 'BLOCKED', 'DELETED');

-- CreateEnum
CREATE TYPE "feed"."PostStatusE" AS ENUM ('ACTIVE', 'DELETED', 'FLAGGED');

-- CreateEnum
CREATE TYPE "feed"."ReactionTypeE" AS ENUM ('LIKE', 'SUPPORT', 'CELEBRATE', 'FUNNY');

-- CreateEnum
CREATE TYPE "company"."EntityTypeE" AS ENUM ('COMPANY', 'EDUCATION', 'GO', 'NGO', 'IGO', 'SMO', 'OTHER');

-- CreateEnum
CREATE TYPE "company"."EntityMemberRoleE" AS ENUM ('ADMIN', 'MAINTAINER', 'MEMBER');

-- CreateEnum
CREATE TYPE "company"."EntityRequestStatusE" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'REVOKED');

-- CreateEnum
CREATE TYPE "company"."SkillCategoryE" AS ENUM ('MARITIME', 'OTHER');

-- CreateEnum
CREATE TYPE "company"."CertificateCourseTypeE" AS ENUM ('STATUTORY', 'VALUE_ADDED');

-- CreateEnum
CREATE TYPE "career"."EquipmentCategoryPowerCapacityUnitE" AS ENUM ('KILO_WATT', 'LITRE', 'LITRES_PER_HOUR', 'CUBIC_METRE', 'CUBIC_METRE_PER_HOUR', 'TONNES', 'TONNES_PER_HOUR');

-- CreateEnum
CREATE TYPE "document"."IdentityTypeE" AS ENUM ('PASSPORT', 'CDC', 'SID');

-- CreateTable
CREATE TABLE "announcement"."Announcement" (
    "id" UUID NOT NULL,
    "profileId" UUID,
    "cursorId" BIGSERIAL NOT NULL,
    "title" VARCHAR(100) NOT NULL,
    "description" VARCHAR(1000) NOT NULL,
    "latitude" DECIMAL(8,6),
    "longitude" DECIMAL(9,6),
    "addressRawDataId" UUID,
    "cityId" UUID,
    "cityRawDataId" UUID,
    "portUnLocode" VARCHAR(5),
    "portRawDataUnLocode" VARCHAR(5),
    "countryIso2" CHAR(2),
    "startDate" DATE NOT NULL,
    "endDate" DATE NOT NULL,
    "startTime" TIME NOT NULL,
    "endTime" TIME NOT NULL,
    "totalAttendees" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Announcement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "announcement"."RSVP" (
    "cursorId" BIGSERIAL NOT NULL,
    "profileId" UUID NOT NULL,
    "announcementId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RSVP_pkey" PRIMARY KEY ("profileId","announcementId")
);

-- CreateTable
CREATE TABLE "app"."Platform" (
    "id" "app"."PlatformE" NOT NULL,
    "url" VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Platform_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app"."AppVersion" (
    "id" UUID NOT NULL,
    "platformId" "app"."PlatformE" NOT NULL,
    "versionNo" VARCHAR(15) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "AppVersion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app"."Policy" (
    "id" UUID NOT NULL,
    "content" TEXT NOT NULL,
    "type" "app"."PolicyTypeE",
    "isActive" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Policy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "auth"."Session" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "profileId" UUID NOT NULL,
    "ipAddress" VARCHAR(39) NOT NULL,
    "platformId" "app"."PlatformE" NOT NULL,
    "appVersionId" UUID NOT NULL,
    "deviceId" UUID NOT NULL,
    "deviceToken" TEXT,
    "latitude" DECIMAL(8,6),
    "longitude" DECIMAL(9,6),
    "expiryDate" TIMESTAMP(3) NOT NULL,
    "profileType" "auth"."ProfileTypeE" NOT NULL DEFAULT 'USER',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastActivity" TIMESTAMP NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ProfileEducation" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "profileId" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "degreeId" UUID,
    "degreeRawDataId" UUID,
    "fromDate" DATE NOT NULL,
    "toDate" DATE,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileEducation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ProfileSkill" (
    "id" UUID NOT NULL,
    "skillId" UUID,
    "skillRawDataId" UUID,
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ProfileSkillEntityDegree" (
    "id" UUID NOT NULL,
    "skillId" UUID,
    "skillRawDataId" UUID,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "degreeId" UUID,
    "degreeRawDataId" UUID,
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileSkillEntityDegree_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ProfileSkillEntityCertificate" (
    "id" UUID NOT NULL,
    "skillId" UUID,
    "skillRawDataId" UUID,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "certificateId" UUID,
    "certificateRawDataId" UUID,
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileSkillEntityCertificate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ProfileSkillExperienceShip" (
    "id" UUID NOT NULL,
    "skillId" UUID,
    "skillRawDataId" UUID,
    "experienceShipId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileSkillExperienceShip_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ProfileCertificate" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "certificateCourseId" UUID,
    "certificateCourseRawDataId" UUID,
    "fromDate" DATE NOT NULL,
    "untilDate" DATE,
    "fileUrl" TEXT,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileCertificate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."Experience" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "profileId" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "years" INTEGER NOT NULL DEFAULT 0,
    "months" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "Experience_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ExperienceDesignation" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "experienceId" UUID NOT NULL,
    "designationAlternativeId" UUID,
    "designationRawDataId" UUID,
    "fromDate" DATE NOT NULL,
    "toDate" DATE,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ExperienceDesignation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ExperienceShip" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "experienceDesignationId" UUID NOT NULL,
    "shipImo" VARCHAR(7),
    "shipRawDataImo" VARCHAR(7),
    "name" VARCHAR(100) NOT NULL,
    "subVesselTypeId" UUID,
    "subVesselTypeRawDataId" UUID,
    "sizeGt" DECIMAL(10,2) NOT NULL,
    "powerKw" DECIMAL(8,2),
    "dwt" INTEGER,
    "details" VARCHAR(255),
    "fromDate" DATE NOT NULL,
    "toDate" DATE,
    "departmentAlternativeId" UUID,
    "departmentRawDataId" UUID,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ExperienceShip_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ExperienceEquipmentCategory" (
    "id" UUID NOT NULL,
    "experienceShipId" UUID NOT NULL,
    "equipmentCategoryId" UUID,
    "equipmentCategoryRawDataId" UUID,
    "manufacturerName" VARCHAR(100) NOT NULL,
    "model" VARCHAR(100) NOT NULL,
    "powerCapacity" DECIMAL(8,2),
    "details" VARCHAR(150),
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ExperienceEquipmentCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ExperienceFuelType" (
    "id" UUID NOT NULL,
    "experienceEquipmentCategoryId" UUID NOT NULL,
    "fuelTypeId" UUID,
    "fuelTypeRawDataId" UUID,

    CONSTRAINT "ExperienceFuelType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "career"."ExperienceCargo" (
    "id" UUID NOT NULL,
    "experienceShipId" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" VARCHAR(150),
    "fromDate" DATE NOT NULL,
    "toDate" DATE,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ExperienceCargo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."Entity" (
    "id" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "memberCount" INTEGER NOT NULL DEFAULT 0,
    "countryIso2" CHAR(2),
    "website" VARCHAR(255),
    "type" "company"."EntityTypeE" NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Entity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."EntitySubType" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "type" "company"."EntityTypeE" NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EntitySubType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."EntitySubTypeEntity" (
    "entityId" UUID NOT NULL,
    "subEntityTypeId" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL
);

-- CreateTable
CREATE TABLE "company"."EntityMember" (
    "id" UUID NOT NULL,
    "entityProfileId" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "profileId" UUID NOT NULL,
    "role" "company"."EntityMemberRoleE" NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "EntityMember_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."EntityTab" (
    "id" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "peopleTab" BOOLEAN NOT NULL DEFAULT false,
    "alumniTab" BOOLEAN NOT NULL DEFAULT false,
    "jobPostingTab" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "EntityTab_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."EntityRequest" (
    "id" UUID NOT NULL,
    "entityProfileId" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "profileId" UUID NOT NULL,
    "status" "company"."EntityRequestStatusE" NOT NULL DEFAULT 'PENDING',
    "purpose" VARCHAR(1000),
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "EntityRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."EntityProfile" (
    "id" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "profileId" UUID NOT NULL,
    "type" "company"."EntityTypeE" NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "avatar" TEXT,
    "website" VARCHAR(255),
    "email" VARCHAR(255) NOT NULL,
    "overview" VARCHAR(1000),
    "foundedAt" TIMESTAMP(3),
    "description" VARCHAR(511),
    "verificationStatus" BOOLEAN NOT NULL DEFAULT false,
    "emailVerificationFirstAttemptAt" TIMESTAMP(3),
    "emailVerificationCount" INTEGER NOT NULL DEFAULT 0,
    "followersCount" INTEGER NOT NULL DEFAULT 0,
    "followingsCount" INTEGER NOT NULL DEFAULT 0,
    "status" "user"."ProfileStatusE" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "EntityProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."Job" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "isUrgent" BOOLEAN NOT NULL DEFAULT false,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "designationAlternativeId" UUID,
    "designationRawDataId" UUID,
    "departmentAlternativeId" UUID,
    "departmentRawDataId" UUID,
    "shipImo" VARCHAR(7),
    "shipRawDataImo" VARCHAR(7),
    "minYears" INTEGER NOT NULL,
    "maxYears" INTEGER,
    "minSalary" INTEGER NOT NULL,
    "maxSalary" INTEGER,
    "status" "company"."JobStatus" NOT NULL DEFAULT 'ACTIVE',
    "expiryDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Job_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."JobEquipmentManufacturer" (
    "id" UUID NOT NULL,
    "jobId" UUID NOT NULL,
    "equipmentManufacturerId" UUID,
    "equipmentManufacturerRawDataId" UUID,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "JobEquipmentManufacturer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."JobEquipmentModel" (
    "id" UUID NOT NULL,
    "jobId" UUID NOT NULL,
    "equipmentModelId" UUID,
    "equipmentModelRawDataId" UUID,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "JobEquipmentModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."JobEquipmentCategory" (
    "id" UUID NOT NULL,
    "jobId" UUID NOT NULL,
    "equipmentCategoryId" UUID,
    "equipmentCategoryRawDataId" UUID,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "JobEquipmentCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."JobEntityBenefit" (
    "id" UUID NOT NULL,
    "jobId" UUID NOT NULL,
    "entityBenefitRawDataId" UUID,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "JobEntityBenefit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."JobApplication" (
    "id" UUID NOT NULL,
    "jobId" UUID NOT NULL,
    "applicantId" UUID NOT NULL,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "decisionMakerProfileId" UUID,
    "matching" INTEGER NOT NULL,
    "status" "company"."ApplicationStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "JobApplication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."Department" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."DepartmentAlternative" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "departmentId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "DepartmentAlternative_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."Designation" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Designation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."DesignationAlternative" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "designationId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "DesignationAlternative_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."Skill" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "category" "company"."SkillCategoryE" NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Skill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."Degree" (
    "id" UUID NOT NULL,
    "name" VARCHAR(150) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Degree_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company"."CertificateCourse" (
    "id" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "type" "company"."CertificateCourseTypeE" NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "CertificateCourse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document"."Identity" (
    "id" UUID NOT NULL,
    "documentNo" VARCHAR(50) NOT NULL,
    "profileId" UUID NOT NULL,
    "fileUrl" TEXT,
    "type" "document"."IdentityTypeE" NOT NULL,
    "orderIndex" INTEGER NOT NULL,
    "countryIso2" CHAR(2) NOT NULL,
    "fromDate" DATE NOT NULL,
    "untilDate" DATE,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Identity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document"."Visa" (
    "id" UUID NOT NULL,
    "documentNo" VARCHAR(50) NOT NULL,
    "name" VARCHAR(150),
    "profileId" UUID NOT NULL,
    "fileUrl" TEXT,
    "countryIso2" CHAR(2) NOT NULL,
    "fromDate" DATE NOT NULL,
    "untilDate" DATE,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Visa_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feed"."Post" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "caption" VARCHAR(2000),
    "reactionsCount" INTEGER NOT NULL DEFAULT 0,
    "parentCommentsCount" INTEGER NOT NULL DEFAULT 0,
    "totalCommentsCount" INTEGER NOT NULL DEFAULT 0,
    "profileId" UUID NOT NULL,
    "entityProfileId" UUID,
    "status" "feed"."PostStatusE" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Post_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feed"."PostMedia" (
    "id" UUID NOT NULL,
    "postId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "entityProfileId" UUID,
    "caption" VARCHAR(1000),
    "fileUrl" TEXT NOT NULL,
    "fileExtension" "feed"."PostFileExtensionE" NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "PostMedia_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feed"."PostReaction" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "entityProfileId" UUID,
    "postId" UUID NOT NULL,
    "reactionType" "feed"."ReactionTypeE" NOT NULL DEFAULT 'LIKE',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PostReaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feed"."PostComment" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "text" VARCHAR(255) NOT NULL,
    "profileId" UUID NOT NULL,
    "entityProfileId" UUID,
    "postId" UUID NOT NULL,
    "status" "feed"."PostStatusE" NOT NULL DEFAULT 'ACTIVE',
    "repliesCount" INTEGER,
    "reactionsCount" INTEGER NOT NULL DEFAULT 0,
    "parentCommentId" UUID,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "PostComment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feed"."PostCommentReaction" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "entityProfileId" UUID,
    "commentId" UUID NOT NULL,
    "reactionType" "feed"."ReactionTypeE" NOT NULL DEFAULT 'LIKE',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PostCommentReaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."Community" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" VARCHAR(300),
    "memberCount" INTEGER NOT NULL DEFAULT 0,
    "questionCount" INTEGER NOT NULL DEFAULT 0,
    "access" "forum"."CommunityAccessE" NOT NULL,
    "creatorId" UUID,
    "isRestricted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,
    "avatar" TEXT,

    CONSTRAINT "Community_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."CommunityMember" (
    "communityId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "type" "forum"."MemberTypeE" NOT NULL DEFAULT 'MEMBER',
    "name" VARCHAR(100),
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "CommunityMember_pkey" PRIMARY KEY ("communityId","profileId")
);

-- CreateTable
CREATE TABLE "forum"."CommunityRequest" (
    "communityId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "requestedType" "forum"."MemberTypeE" NOT NULL,
    "acceptedType" "forum"."MemberTypeE",
    "status" "forum"."CommunityRequestStatusE" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "CommunityRequest_pkey" PRIMARY KEY ("communityId","profileId")
);

-- CreateTable
CREATE TABLE "forum"."Question" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "slug" VARCHAR(255),
    "title" VARCHAR(150) NOT NULL,
    "description" VARCHAR(2000) NOT NULL,
    "type" "forum"."QuestionTypeE" NOT NULL DEFAULT 'NORMAL',
    "communityId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "upvoteCount" INTEGER NOT NULL DEFAULT 0,
    "downvoteCount" INTEGER NOT NULL DEFAULT 0,
    "answerCount" INTEGER NOT NULL DEFAULT 0,
    "commentCount" INTEGER NOT NULL DEFAULT 0,
    "isLive" BOOLEAN NOT NULL,
    "liveStartedAt" TIMESTAMPTZ,
    "shipImo" VARCHAR(7),
    "shipRawDataImo" VARCHAR(7),
    "mainVesselTypeId" UUID,
    "mainVesselTypeRawDataId" UUID,
    "subVesselTypeId" UUID,
    "subVesselTypeRawDataId" UUID,
    "isSolved" BOOLEAN NOT NULL DEFAULT false,
    "isEdited" BOOLEAN NOT NULL DEFAULT false,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT false,
    "equipmentCategoryId" UUID,
    "equipmentCategoryRawDataId" UUID,
    "equipmentModelId" UUID,
    "equipmentModelRawDataId" UUID,
    "equipmentManufacturerId" UUID,
    "equipmentManufacturerRawDataId" UUID,
    "departmentAlternativeId" UUID,
    "departmentRawDataId" UUID,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Question_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."QuestionMedia" (
    "id" UUID NOT NULL,
    "questionId" UUID NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "communityId" UUID NOT NULL,
    "fileExtension" "forum"."ForumFileExtensionE" NOT NULL,

    CONSTRAINT "QuestionMedia_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."Answer" (
    "id" UUID NOT NULL,
    "slug" VARCHAR(255),
    "cursorId" BIGSERIAL NOT NULL,
    "text" VARCHAR(2000) NOT NULL,
    "upvoteCount" INTEGER NOT NULL DEFAULT 0,
    "downvoteCount" INTEGER NOT NULL DEFAULT 0,
    "commentCount" INTEGER NOT NULL DEFAULT 0,
    "questionId" UUID NOT NULL,
    "communityId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "status" "forum"."AnswerStatusE" NOT NULL DEFAULT 'PROPOSED_SOLUTION',
    "isEdited" BOOLEAN NOT NULL DEFAULT false,
    "validatedBy" UUID,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Answer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."AnswerMedia" (
    "id" UUID NOT NULL,
    "answerId" UUID NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "communityId" UUID NOT NULL,
    "fileExtension" "forum"."ForumFileExtensionE" NOT NULL,

    CONSTRAINT "AnswerMedia_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."AnswerVote" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "answerId" UUID NOT NULL,
    "communityId" UUID NOT NULL,
    "questionId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "type" "forum"."VoteTypeE" NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "AnswerVote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."QuestionVote" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "questionId" UUID NOT NULL,
    "communityId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "type" "forum"."VoteTypeE" NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "QuestionVote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."Topic" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Topic_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."QuestionTopic" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "communityId" UUID NOT NULL,
    "questionId" UUID NOT NULL,
    "topicId" UUID,
    "topicRawDataId" UUID,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "QuestionTopic_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."QuestionComment" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "questionId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "text" VARCHAR(255) NOT NULL,
    "parentCommentId" UUID,
    "replyCount" INTEGER,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "QuestionComment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forum"."AnswerComment" (
    "id" UUID NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "communityId" UUID NOT NULL,
    "questionId" UUID NOT NULL,
    "answerId" UUID NOT NULL,
    "parentCommentId" UUID,
    "profileId" UUID NOT NULL,
    "replyCount" INTEGER,
    "text" VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "AnswerComment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "master"."City" (
    "id" UUID NOT NULL,
    "geoNameId" VARCHAR(15),
    "name" VARCHAR(50) NOT NULL,
    "countryIso2" CHAR(2) NOT NULL,
    "latitude" DECIMAL(8,6),
    "longitude" DECIMAL(9,6),
    "mapboxId" VARCHAR(50),
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "City_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "master"."Country" (
    "iso2" CHAR(2) NOT NULL,
    "iso3" CHAR(3) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "native" VARCHAR(100),
    "continent" VARCHAR(50),
    "capital" VARCHAR(100),
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Country_pkey" PRIMARY KEY ("iso2")
);

-- CreateTable
CREATE TABLE "master"."CallingCode" (
    "countryIso2" CHAR(2) NOT NULL,
    "code" VARCHAR(10) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "CallingCode_pkey" PRIMARY KEY ("countryIso2")
);

-- CreateTable
CREATE TABLE "master"."Timezone" (
    "timezone" VARCHAR(500) NOT NULL,
    "countryIso2" VARCHAR(2) NOT NULL,
    "utcOffset" INTEGER NOT NULL,
    "dstOffset" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Timezone_pkey" PRIMARY KEY ("timezone","countryIso2")
);

-- CreateTable
CREATE TABLE "master"."Currency" (
    "countryIso2" CHAR(2) NOT NULL,
    "code" VARCHAR(5) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "symbol" VARCHAR(50) NOT NULL,
    "numeric" INTEGER NOT NULL,
    "decimal" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Currency_pkey" PRIMARY KEY ("countryIso2")
);

-- CreateTable
CREATE TABLE "network"."Request" (
    "id" UUID NOT NULL,
    "senderProfileId" UUID NOT NULL,
    "receiverProfileId" UUID NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 1,
    "status" "network"."RequestStatusE" NOT NULL DEFAULT 'PENDING',
    "requestSentAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Request_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "network"."Connection" (
    "cursorId" BIGSERIAL NOT NULL,
    "profileId" UUID NOT NULL,
    "connectedId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL
);

-- CreateTable
CREATE TABLE "network"."BlockedProfile" (
    "blockerId" UUID NOT NULL,
    "blockedId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL
);

-- CreateTable
CREATE TABLE "network"."Follow" (
    "cursorId" BIGSERIAL NOT NULL,
    "followerProfileId" UUID NOT NULL,
    "followeeProfileId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL
);

-- CreateTable
CREATE TABLE "network"."EntityProfileFollow" (
    "cursorId" BIGSERIAL NOT NULL,
    "followerId" UUID NOT NULL,
    "followeeId" UUID NOT NULL,
    "followerType" "network"."FollowTypeE" NOT NULL,
    "followeeType" "network"."FollowTypeE" NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "EntityProfileFollow_pkey" PRIMARY KEY ("followeeId","followerId")
);

-- CreateTable
CREATE TABLE "network"."BlockedEntityProfile" (
    "blockerId" UUID NOT NULL,
    "blockedId" UUID NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL
);

-- CreateTable
CREATE TABLE "port"."Port" (
    "unLocode" VARCHAR(5) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "imageUrl" TEXT,
    "cityId" UUID NOT NULL,
    "countryIso2" CHAR(2) NOT NULL,
    "timezone" VARCHAR(500),
    "latitude" DECIMAL(10,7),
    "longitude" DECIMAL(11,7),
    "noOfTerminals" INTEGER,
    "noOfBerths" INTEGER,
    "maxDraught" DECIMAL(10,2),
    "maxDeadweight" DECIMAL(8,2),
    "maxLength" DECIMAL(7,2),
    "maxAirDraught" DECIMAL(10,2),
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Port_pkey" PRIMARY KEY ("unLocode")
);

-- CreateTable
CREATE TABLE "port"."PortImageContribution" (
    "id" UUID NOT NULL,
    "portUnLocode" VARCHAR(5),
    "portRawDataUnLocode" VARCHAR(5),
    "imageUrl" TEXT NOT NULL,
    "dataStatus" "port"."PortStatusE" NOT NULL DEFAULT 'PENDING',
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "PortImageContribution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "port"."PortContribution" (
    "id" UUID NOT NULL,
    "portUnLocode" VARCHAR(5),
    "portRawDataUnLocode" VARCHAR(5),
    "profileId" UUID NOT NULL,
    "label" VARCHAR(100) NOT NULL,
    "value" VARCHAR(100) NOT NULL,
    "dataStatus" "port"."PortStatusE" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "PortContribution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "port"."ScrapBookPost" (
    "id" UUID NOT NULL,
    "text" VARCHAR(1000) NOT NULL,
    "textPreview" VARCHAR(120) NOT NULL,
    "profileId" UUID NOT NULL,
    "status" "port"."ScrapBookPostStatusE" NOT NULL DEFAULT 'ACTIVE',
    "reactionCount" INTEGER NOT NULL DEFAULT 0,
    "commentCount" INTEGER NOT NULL DEFAULT 0,
    "portUnLocode" VARCHAR(5) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ScrapBookPost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "port"."ScrapBookReaction" (
    "profileId" UUID NOT NULL,
    "scrapBookPostId" UUID NOT NULL,
    "reactionType" "port"."ScrapBookReactionTypeE" NOT NULL DEFAULT 'LIKE',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ScrapBookReaction_pkey" PRIMARY KEY ("profileId","scrapBookPostId")
);

-- CreateTable
CREATE TABLE "port"."ScrapBookComment" (
    "id" UUID NOT NULL,
    "text" VARCHAR(300) NOT NULL,
    "cursorId" BIGSERIAL NOT NULL,
    "scrapBookPostId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "status" "feed"."PostStatusE" NOT NULL DEFAULT 'ACTIVE',
    "parentCommentId" UUID,
    "repliesCount" INTEGER,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ScrapBookComment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "port"."ScrapBookCommentMention" (
    "id" UUID NOT NULL,
    "commentId" UUID NOT NULL,
    "mentionedProfileId" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ScrapBookCommentMention_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "port"."PortVisitor" (
    "id" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "portUnLocode" VARCHAR(5),
    "portRawDataUnLocode" VARCHAR(5),
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "PortVisitor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."DepartmentRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "DepartmentRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."DesignationRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "DesignationRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."ShipRawData" (
    "imo" VARCHAR(7) NOT NULL,
    "name" VARCHAR(100),
    "flagCountryIso2" CHAR(2),
    "callSign" VARCHAR(7),
    "mmsi" INTEGER,
    "length" INTEGER,
    "beam" INTEGER,
    "yearBuilt" INTEGER,
    "gt" INTEGER,
    "dwt" INTEGER,
    "mainVesselTypeId" UUID,
    "mainVesselTypeRawDataId" UUID,
    "subVesselTypeId" UUID,
    "subVesselTypeRawDataId" UUID,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ShipRawData_pkey" PRIMARY KEY ("imo")
);

-- CreateTable
CREATE TABLE "rawData"."CityRawData" (
    "id" UUID NOT NULL,
    "geoNameId" VARCHAR(15),
    "name" VARCHAR(50) NOT NULL,
    "countryIso2" CHAR(2) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "latitude" DECIMAL(6,6),
    "longitude" DECIMAL(7,6),
    "mapboxId" VARCHAR(50),
    "source" "rawData"."PlaceSourceE" NOT NULL DEFAULT 'PROFILE',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "CityRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."AddressRawData" (
    "id" UUID NOT NULL,
    "text" VARCHAR(1000) NOT NULL,
    "mapboxId" VARCHAR(50) NOT NULL,
    "latitude" DECIMAL(8,6) NOT NULL,
    "longitude" DECIMAL(9,6) NOT NULL,
    "cityId" UUID,
    "cityRawDataId" UUID,
    "countryIso2" TEXT NOT NULL,
    "pincode" VARCHAR(7) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "AddressRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."PortRawData" (
    "unLocode" VARCHAR(5) NOT NULL,
    "name" VARCHAR(100),
    "cityId" UUID,
    "cityRawDataId" UUID,
    "countryIso2" CHAR(2) NOT NULL,
    "timezoneIso2" CHAR(2),
    "latitude" DECIMAL(6,6),
    "longitude" DECIMAL(7,6),
    "noOfTerminals" INTEGER,
    "noOfBerths" INTEGER,
    "maxDraught" DECIMAL(10,2),
    "maxDeadweight" DECIMAL(8,2),
    "maxLength" DECIMAL(7,2),
    "maxAirDraught" DECIMAL(10,2),
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "PortRawData_pkey" PRIMARY KEY ("unLocode")
);

-- CreateTable
CREATE TABLE "rawData"."SubVesselTypeRawData" (
    "id" UUID NOT NULL,
    "mainVesselTypeId" UUID,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "SubVesselTypeRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."EntityRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "memberCount" INTEGER NOT NULL DEFAULT 0,
    "website" VARCHAR(255),
    "type" "company"."EntityTypeE" NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "EntityRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."SkillRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "category" "rawData"."SkillCategoryRawDataE" NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "SkillRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."DegreeRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(150) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "DegreeRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."TopicRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "TopicRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."CertificateCourseRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "type" "company"."CertificateCourseTypeE" NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "CertificateCourseRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."EntityBenefitRawData" (
    "id" UUID NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "cursorId" BIGSERIAL NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EntityBenefitRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."FuelTypeRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "FuelTypeRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."EquipmentCategoryRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "hasFuelType" BOOLEAN NOT NULL DEFAULT false,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EquipmentCategoryRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."EquipmentManufacturerRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EquipmentManufacturerRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."EquipmentModelRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "dataStatus" "rawData"."StatusRawData" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EquipmentModelRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."MainVesselTypeRawData" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "MainVesselTypeRawData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score"."Reward" (
    "id" UUID NOT NULL,
    "type" "score"."ScoreTypeE" NOT NULL,
    "score" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Reward_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score"."RewardAssigned" (
    "id" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rewardId" UUID NOT NULL,
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "RewardAssigned_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "score"."RewardProfile" (
    "profileId" UUID NOT NULL,
    "contributionScore" INTEGER NOT NULL DEFAULT 0,
    "qnaAnswerScore" INTEGER NOT NULL DEFAULT 0,
    "troubleshootAnswerScore" INTEGER NOT NULL DEFAULT 0,
    "totalScore" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "RewardProfile_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."ContributionWeeklyLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ContributionWeeklyLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."QnAAnswerWeeklyLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "QnAAnswerWeeklyLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."TroubleshootWeeklyLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "TroubleshootWeeklyLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."TotalWeeklyLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "TotalWeeklyLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."ContributionOverallLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ContributionOverallLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."QnAAnswerOverallLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "QnAAnswerOverallLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."TroubleshootOverallLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "TroubleshootOverallLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "leaderboard"."TotalOverallLeaderboard" (
    "profileId" UUID NOT NULL,
    "score" INTEGER NOT NULL,
    "rank" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "TotalOverallLeaderboard_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "ship"."Ship" (
    "imo" VARCHAR(7) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "flagCountryIso2" CHAR(2),
    "callSign" VARCHAR(7),
    "mmsi" INTEGER,
    "length" INTEGER,
    "beam" INTEGER,
    "yearBuilt" INTEGER,
    "gt" INTEGER,
    "dwt" INTEGER,
    "imageUrl" TEXT,
    "mainVesselTypeId" UUID NOT NULL,
    "subVesselTypeId" UUID,
    "status" "ship"."ServiceStatusE",
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Ship_pkey" PRIMARY KEY ("imo")
);

-- CreateTable
CREATE TABLE "ship"."ShipName" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "shipImo" VARCHAR(7) NOT NULL,
    "fromDate" DATE,
    "toDate" DATE,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ShipName_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."ShipImageContribution" (
    "id" UUID NOT NULL,
    "shipImo" VARCHAR(7),
    "shipRawDataImo" VARCHAR(7),
    "imageUrl" TEXT NOT NULL,
    "dataStatus" "ship"."ShipStatusE" NOT NULL DEFAULT 'PENDING',
    "profileId" UUID,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ShipImageContribution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."ShipContribution" (
    "id" UUID NOT NULL,
    "shipImo" VARCHAR(7),
    "shipRawDataImo" VARCHAR(7),
    "profileId" UUID NOT NULL,
    "label" "ship"."ShipContributionLabelE" NOT NULL,
    "value" VARCHAR(100) NOT NULL,
    "dataStatus" "ship"."ShipStatusE" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "ShipContribution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."FuelType" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "FuelType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."EquipmentManufacturer" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EquipmentManufacturer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."EquipmentModel" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EquipmentModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."EquipmentCategory" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "hasFuelType" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "EquipmentCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."MainVesselType" (
    "id" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "MainVesselType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ship"."SubVesselType" (
    "id" UUID NOT NULL,
    "mainVesselTypeId" UUID NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "SubVesselType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user"."Profile" (
    "id" UUID NOT NULL,
    "email" VARCHAR(255),
    "password" TEXT,
    "username" VARCHAR(25),
    "googleSub" VARCHAR(255),
    "appleSub" VARCHAR(255),
    "name" VARCHAR(50),
    "avatar" TEXT,
    "gender" "user"."GenderE",
    "status" "user"."ProfileStatusE" NOT NULL DEFAULT 'ACTIVE',
    "description" VARCHAR(255),
    "countryIso2" CHAR(2),
    "designationText" VARCHAR(100),
    "entityText" VARCHAR(255),
    "designationAlternativeId" UUID,
    "designationRawDataId" UUID,
    "entityId" UUID,
    "entityRawDataId" UUID,
    "followersCount" INTEGER NOT NULL DEFAULT 0,
    "followingsCount" INTEGER NOT NULL DEFAULT 0,
    "connectionsCount" INTEGER NOT NULL DEFAULT 0,
    "cursorId" BIGSERIAL NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "Profile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user"."ProfileStatus" (
    "profileId" UUID NOT NULL,
    "isPasswordSaved" BOOLEAN NOT NULL DEFAULT false,
    "isEmailVerified" BOOLEAN NOT NULL DEFAULT false,
    "isMobileVerified" BOOLEAN NOT NULL DEFAULT false,
    "isPersonalDetailsSaved" BOOLEAN NOT NULL DEFAULT false,
    "isWorkDetailsSaved" BOOLEAN NOT NULL DEFAULT false,
    "isPrivacyPolicyAccepted" BOOLEAN NOT NULL DEFAULT false,
    "deletionInitiatedAt" TIMESTAMP(3),
    "isAnonymous" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileStatus_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "user"."ProfileMeta" (
    "profileId" UUID NOT NULL,
    "receivedRequestCount" INTEGER NOT NULL DEFAULT 0,
    "sentRequestCount" INTEGER NOT NULL DEFAULT 0,
    "postCount" INTEGER NOT NULL DEFAULT 0,
    "educationCount" INTEGER NOT NULL DEFAULT 0,
    "statutoryCertCount" INTEGER NOT NULL DEFAULT 0,
    "valueAddedCertCount" INTEGER NOT NULL DEFAULT 0,
    "identityCount" INTEGER NOT NULL DEFAULT 0,
    "visaCount" INTEGER NOT NULL DEFAULT 0,
    "maritimeSkillsCount" INTEGER NOT NULL DEFAULT 0,
    "otherSkillsCount" INTEGER NOT NULL DEFAULT 0,
    "answerCount" INTEGER NOT NULL DEFAULT 0,
    "solutionCount" INTEGER NOT NULL DEFAULT 0,
    "normalQuestionScore" INTEGER NOT NULL DEFAULT 0,
    "troubleshootQuestionScore" INTEGER NOT NULL DEFAULT 0,
    "contributionScore" INTEGER NOT NULL DEFAULT 0,
    "emailVerificationCount" INTEGER NOT NULL DEFAULT 0,
    "emailVerificationFirstAttemptAt" TIMESTAMP(3),
    "passwordResetCount" INTEGER NOT NULL DEFAULT 0,
    "passwordResetFirstAttemptAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL,

    CONSTRAINT "ProfileMeta_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "referral"."ReferralCodes" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "profileId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "ReferralCodes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral"."ReferralStatus" (
    "id" TEXT NOT NULL,
    "referredProfileId" UUID NOT NULL,
    "referrerProfileId" UUID NOT NULL,
    "referralCodeId" TEXT NOT NULL,
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isReferrerRewarded" BOOLEAN NOT NULL DEFAULT false,
    "isReferredRewarded" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "ReferralStatus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rawData"."_CityRawDataToPort" (
    "A" UUID NOT NULL,
    "B" VARCHAR(5) NOT NULL,

    CONSTRAINT "_CityRawDataToPort_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "AppVersion_platformId_versionNo_key" ON "app"."AppVersion"("platformId", "versionNo");

-- CreateIndex
CREATE INDEX "Entity_name_idx" ON "company"."Entity"("name");

-- CreateIndex
CREATE UNIQUE INDEX "EntitySubTypeEntity_entityId_subEntityTypeId_key" ON "company"."EntitySubTypeEntity"("entityId", "subEntityTypeId");

-- CreateIndex
CREATE INDEX "EntityMember_profileId_role_idx" ON "company"."EntityMember"("profileId", "role");

-- CreateIndex
CREATE UNIQUE INDEX "EntityMember_entityId_profileId_key" ON "company"."EntityMember"("entityId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "EntityMember_entityRawDataId_profileId_key" ON "company"."EntityMember"("entityRawDataId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "EntityTab_entityId_key" ON "company"."EntityTab"("entityId");

-- CreateIndex
CREATE UNIQUE INDEX "EntityTab_entityRawDataId_key" ON "company"."EntityTab"("entityRawDataId");

-- CreateIndex
CREATE UNIQUE INDEX "EntityProfile_entityId_profileId_key" ON "company"."EntityProfile"("entityId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "EntityProfile_entityRawDataId_profileId_key" ON "company"."EntityProfile"("entityRawDataId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "JobApplication_applicantId_jobId_key" ON "company"."JobApplication"("applicantId", "jobId");

-- CreateIndex
CREATE INDEX "Identity_profileId_type_idx" ON "document"."Identity"("profileId", "type");

-- CreateIndex
CREATE INDEX "Identity_profileId_documentNo_idx" ON "document"."Identity"("profileId", "documentNo");

-- CreateIndex
CREATE UNIQUE INDEX "Visa_profileId_documentNo_key" ON "document"."Visa"("profileId", "documentNo");

-- CreateIndex
CREATE INDEX "Post_profileId_idx" ON "feed"."Post"("profileId");

-- CreateIndex
CREATE INDEX "Post_entityProfileId_idx" ON "feed"."Post"("entityProfileId");

-- CreateIndex
CREATE INDEX "Post_cursorId_idx" ON "feed"."Post"("cursorId");

-- CreateIndex
CREATE INDEX "PostMedia_postId_idx" ON "feed"."PostMedia"("postId");

-- CreateIndex
CREATE INDEX "PostMedia_profileId_idx" ON "feed"."PostMedia"("profileId");

-- CreateIndex
CREATE INDEX "PostMedia_entityProfileId_idx" ON "feed"."PostMedia"("entityProfileId");

-- CreateIndex
CREATE INDEX "PostReaction_postId_idx" ON "feed"."PostReaction"("postId");

-- CreateIndex
CREATE UNIQUE INDEX "PostReaction_profileId_postId_key" ON "feed"."PostReaction"("profileId", "postId");

-- CreateIndex
CREATE INDEX "PostComment_postId_idx" ON "feed"."PostComment"("postId");

-- CreateIndex
CREATE INDEX "PostComment_profileId_idx" ON "feed"."PostComment"("profileId");

-- CreateIndex
CREATE INDEX "PostComment_parentCommentId_idx" ON "feed"."PostComment"("parentCommentId");

-- CreateIndex
CREATE INDEX "PostCommentReaction_commentId_idx" ON "feed"."PostCommentReaction"("commentId");

-- CreateIndex
CREATE UNIQUE INDEX "CommunityMember_communityId_profileId_key" ON "forum"."CommunityMember"("communityId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "CommunityRequest_communityId_profileId_key" ON "forum"."CommunityRequest"("communityId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "Question_slug_key" ON "forum"."Question"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Answer_slug_key" ON "forum"."Answer"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "AnswerVote_answerId_profileId_key" ON "forum"."AnswerVote"("answerId", "profileId");

-- CreateIndex
CREATE UNIQUE INDEX "QuestionVote_questionId_profileId_key" ON "forum"."QuestionVote"("questionId", "profileId");

-- CreateIndex
CREATE INDEX "City_name_idx" ON "master"."City"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Country_iso2_key" ON "master"."Country"("iso2");

-- CreateIndex
CREATE UNIQUE INDEX "Country_iso3_key" ON "master"."Country"("iso3");

-- CreateIndex
CREATE INDEX "Country_name_idx" ON "master"."Country"("name");

-- CreateIndex
CREATE UNIQUE INDEX "CallingCode_countryIso2_key" ON "master"."CallingCode"("countryIso2");

-- CreateIndex
CREATE INDEX "CallingCode_code_idx" ON "master"."CallingCode"("code");

-- CreateIndex
CREATE INDEX "Timezone_timezone_idx" ON "master"."Timezone"("timezone");

-- CreateIndex
CREATE UNIQUE INDEX "Currency_countryIso2_key" ON "master"."Currency"("countryIso2");

-- CreateIndex
CREATE INDEX "Currency_code_name_idx" ON "master"."Currency"("code", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Request_senderProfileId_receiverProfileId_key" ON "network"."Request"("senderProfileId", "receiverProfileId");

-- CreateIndex
CREATE INDEX "Connection_profileId_connectedId_idx" ON "network"."Connection"("profileId", "connectedId");

-- CreateIndex
CREATE UNIQUE INDEX "Connection_profileId_connectedId_key" ON "network"."Connection"("profileId", "connectedId");

-- CreateIndex
CREATE UNIQUE INDEX "BlockedProfile_blockerId_blockedId_key" ON "network"."BlockedProfile"("blockerId", "blockedId");

-- CreateIndex
CREATE INDEX "Follow_cursorId_idx" ON "network"."Follow"("cursorId");

-- CreateIndex
CREATE UNIQUE INDEX "Follow_followerProfileId_followeeProfileId_key" ON "network"."Follow"("followerProfileId", "followeeProfileId");

-- CreateIndex
CREATE INDEX "EntityProfileFollow_cursorId_idx" ON "network"."EntityProfileFollow"("cursorId");

-- CreateIndex
CREATE UNIQUE INDEX "BlockedEntityProfile_blockerId_blockedId_key" ON "network"."BlockedEntityProfile"("blockerId", "blockedId");

-- CreateIndex
CREATE INDEX "Port_unLocode_idx" ON "port"."Port"("unLocode");

-- CreateIndex
CREATE INDEX "Port_name_idx" ON "port"."Port"("name");

-- CreateIndex
CREATE UNIQUE INDEX "PortContribution_profileId_portUnLocode_portRawDataUnLocode_key" ON "port"."PortContribution"("profileId", "portUnLocode", "portRawDataUnLocode", "label");

-- CreateIndex
CREATE INDEX "CityRawData_name_idx" ON "rawData"."CityRawData"("name");

-- CreateIndex
CREATE UNIQUE INDEX "PortRawData_unLocode_key" ON "rawData"."PortRawData"("unLocode");

-- CreateIndex
CREATE UNIQUE INDEX "ShipContribution_profileId_shipImo_shipRawDataImo_label_key" ON "ship"."ShipContribution"("profileId", "shipImo", "shipRawDataImo", "label");

-- CreateIndex
CREATE UNIQUE INDEX "ReferralCodes_code_key" ON "referral"."ReferralCodes"("code");

-- CreateIndex
CREATE UNIQUE INDEX "ReferralCodes_profileId_key" ON "referral"."ReferralCodes"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "ReferralStatus_referredProfileId_key" ON "referral"."ReferralStatus"("referredProfileId");

-- CreateIndex
CREATE INDEX "_CityRawDataToPort_B_index" ON "rawData"."_CityRawDataToPort"("B");

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_addressRawDataId_fkey" FOREIGN KEY ("addressRawDataId") REFERENCES "rawData"."AddressRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_cityId_fkey" FOREIGN KEY ("cityId") REFERENCES "master"."City"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_cityRawDataId_fkey" FOREIGN KEY ("cityRawDataId") REFERENCES "rawData"."CityRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_portUnLocode_fkey" FOREIGN KEY ("portUnLocode") REFERENCES "port"."Port"("unLocode") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_portRawDataUnLocode_fkey" FOREIGN KEY ("portRawDataUnLocode") REFERENCES "rawData"."PortRawData"("unLocode") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."Announcement" ADD CONSTRAINT "Announcement_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."RSVP" ADD CONSTRAINT "RSVP_announcementId_fkey" FOREIGN KEY ("announcementId") REFERENCES "announcement"."Announcement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announcement"."RSVP" ADD CONSTRAINT "RSVP_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auth"."Session" ADD CONSTRAINT "Session_platformId_fkey" FOREIGN KEY ("platformId") REFERENCES "app"."Platform"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auth"."Session" ADD CONSTRAINT "Session_appVersionId_fkey" FOREIGN KEY ("appVersionId") REFERENCES "app"."AppVersion"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileEducation" ADD CONSTRAINT "ProfileEducation_degreeId_fkey" FOREIGN KEY ("degreeId") REFERENCES "company"."Degree"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileEducation" ADD CONSTRAINT "ProfileEducation_degreeRawDataId_fkey" FOREIGN KEY ("degreeRawDataId") REFERENCES "rawData"."DegreeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileEducation" ADD CONSTRAINT "ProfileEducation_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileEducation" ADD CONSTRAINT "ProfileEducation_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileEducation" ADD CONSTRAINT "ProfileEducation_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkill" ADD CONSTRAINT "ProfileSkill_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "company"."Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkill" ADD CONSTRAINT "ProfileSkill_skillRawDataId_fkey" FOREIGN KEY ("skillRawDataId") REFERENCES "rawData"."SkillRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkill" ADD CONSTRAINT "ProfileSkill_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityDegree" ADD CONSTRAINT "ProfileSkillEntityDegree_degreeId_fkey" FOREIGN KEY ("degreeId") REFERENCES "company"."Degree"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityDegree" ADD CONSTRAINT "ProfileSkillEntityDegree_degreeRawDataId_fkey" FOREIGN KEY ("degreeRawDataId") REFERENCES "rawData"."DegreeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityDegree" ADD CONSTRAINT "ProfileSkillEntityDegree_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityDegree" ADD CONSTRAINT "ProfileSkillEntityDegree_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityDegree" ADD CONSTRAINT "ProfileSkillEntityDegree_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "company"."Skill"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityDegree" ADD CONSTRAINT "ProfileSkillEntityDegree_skillRawDataId_fkey" FOREIGN KEY ("skillRawDataId") REFERENCES "rawData"."SkillRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityCertificate" ADD CONSTRAINT "ProfileSkillEntityCertificate_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityCertificate" ADD CONSTRAINT "ProfileSkillEntityCertificate_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityCertificate" ADD CONSTRAINT "ProfileSkillEntityCertificate_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "company"."Skill"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityCertificate" ADD CONSTRAINT "ProfileSkillEntityCertificate_skillRawDataId_fkey" FOREIGN KEY ("skillRawDataId") REFERENCES "rawData"."SkillRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillEntityCertificate" ADD CONSTRAINT "ProfileSkillEntityCertificate_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillExperienceShip" ADD CONSTRAINT "ProfileSkillExperienceShip_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "company"."Skill"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillExperienceShip" ADD CONSTRAINT "ProfileSkillExperienceShip_skillRawDataId_fkey" FOREIGN KEY ("skillRawDataId") REFERENCES "rawData"."SkillRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileSkillExperienceShip" ADD CONSTRAINT "ProfileSkillExperienceShip_experienceShipId_fkey" FOREIGN KEY ("experienceShipId") REFERENCES "career"."ExperienceShip"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileCertificate" ADD CONSTRAINT "ProfileCertificate_certificateCourseId_fkey" FOREIGN KEY ("certificateCourseId") REFERENCES "company"."CertificateCourse"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileCertificate" ADD CONSTRAINT "ProfileCertificate_certificateCourseRawDataId_fkey" FOREIGN KEY ("certificateCourseRawDataId") REFERENCES "rawData"."CertificateCourseRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileCertificate" ADD CONSTRAINT "ProfileCertificate_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileCertificate" ADD CONSTRAINT "ProfileCertificate_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ProfileCertificate" ADD CONSTRAINT "ProfileCertificate_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."Experience" ADD CONSTRAINT "Experience_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."Experience" ADD CONSTRAINT "Experience_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."Experience" ADD CONSTRAINT "Experience_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceDesignation" ADD CONSTRAINT "ExperienceDesignation_designationAlternativeId_fkey" FOREIGN KEY ("designationAlternativeId") REFERENCES "company"."DesignationAlternative"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceDesignation" ADD CONSTRAINT "ExperienceDesignation_designationRawDataId_fkey" FOREIGN KEY ("designationRawDataId") REFERENCES "rawData"."DesignationRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceDesignation" ADD CONSTRAINT "ExperienceDesignation_experienceId_fkey" FOREIGN KEY ("experienceId") REFERENCES "career"."Experience"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceDesignation" ADD CONSTRAINT "ExperienceDesignation_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_departmentAlternativeId_fkey" FOREIGN KEY ("departmentAlternativeId") REFERENCES "company"."DepartmentAlternative"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_departmentRawDataId_fkey" FOREIGN KEY ("departmentRawDataId") REFERENCES "rawData"."DepartmentRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_experienceDesignationId_fkey" FOREIGN KEY ("experienceDesignationId") REFERENCES "career"."ExperienceDesignation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_shipImo_fkey" FOREIGN KEY ("shipImo") REFERENCES "ship"."Ship"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_shipRawDataImo_fkey" FOREIGN KEY ("shipRawDataImo") REFERENCES "rawData"."ShipRawData"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_subVesselTypeId_fkey" FOREIGN KEY ("subVesselTypeId") REFERENCES "ship"."SubVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_subVesselTypeRawDataId_fkey" FOREIGN KEY ("subVesselTypeRawDataId") REFERENCES "rawData"."SubVesselTypeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceShip" ADD CONSTRAINT "ExperienceShip_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceEquipmentCategory" ADD CONSTRAINT "ExperienceEquipmentCategory_equipmentCategoryId_fkey" FOREIGN KEY ("equipmentCategoryId") REFERENCES "ship"."EquipmentCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceEquipmentCategory" ADD CONSTRAINT "ExperienceEquipmentCategory_equipmentCategoryRawDataId_fkey" FOREIGN KEY ("equipmentCategoryRawDataId") REFERENCES "rawData"."EquipmentCategoryRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceEquipmentCategory" ADD CONSTRAINT "ExperienceEquipmentCategory_experienceShipId_fkey" FOREIGN KEY ("experienceShipId") REFERENCES "career"."ExperienceShip"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceFuelType" ADD CONSTRAINT "ExperienceFuelType_experienceEquipmentCategoryId_fkey" FOREIGN KEY ("experienceEquipmentCategoryId") REFERENCES "career"."ExperienceEquipmentCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceFuelType" ADD CONSTRAINT "ExperienceFuelType_fuelTypeId_fkey" FOREIGN KEY ("fuelTypeId") REFERENCES "ship"."FuelType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceFuelType" ADD CONSTRAINT "ExperienceFuelType_fuelTypeRawDataId_fkey" FOREIGN KEY ("fuelTypeRawDataId") REFERENCES "rawData"."FuelTypeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "career"."ExperienceCargo" ADD CONSTRAINT "ExperienceCargo_experienceShipId_fkey" FOREIGN KEY ("experienceShipId") REFERENCES "career"."ExperienceShip"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Entity" ADD CONSTRAINT "Entity_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntitySubTypeEntity" ADD CONSTRAINT "EntitySubTypeEntity_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntitySubTypeEntity" ADD CONSTRAINT "EntitySubTypeEntity_subEntityTypeId_fkey" FOREIGN KEY ("subEntityTypeId") REFERENCES "company"."EntitySubType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityMember" ADD CONSTRAINT "EntityMember_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityMember" ADD CONSTRAINT "EntityMember_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityMember" ADD CONSTRAINT "EntityMember_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityMember" ADD CONSTRAINT "EntityMember_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityTab" ADD CONSTRAINT "EntityTab_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityTab" ADD CONSTRAINT "EntityTab_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityRequest" ADD CONSTRAINT "EntityRequest_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityRequest" ADD CONSTRAINT "EntityRequest_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityRequest" ADD CONSTRAINT "EntityRequest_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityRequest" ADD CONSTRAINT "EntityRequest_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityProfile" ADD CONSTRAINT "EntityProfile_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityProfile" ADD CONSTRAINT "EntityProfile_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."EntityProfile" ADD CONSTRAINT "EntityProfile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_designationAlternativeId_fkey" FOREIGN KEY ("designationAlternativeId") REFERENCES "company"."DesignationAlternative"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_designationRawDataId_fkey" FOREIGN KEY ("designationRawDataId") REFERENCES "rawData"."DesignationRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_departmentAlternativeId_fkey" FOREIGN KEY ("departmentAlternativeId") REFERENCES "company"."DepartmentAlternative"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_departmentRawDataId_fkey" FOREIGN KEY ("departmentRawDataId") REFERENCES "rawData"."DepartmentRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_shipImo_fkey" FOREIGN KEY ("shipImo") REFERENCES "ship"."Ship"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_shipRawDataImo_fkey" FOREIGN KEY ("shipRawDataImo") REFERENCES "rawData"."ShipRawData"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."Job" ADD CONSTRAINT "Job_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentManufacturer" ADD CONSTRAINT "JobEquipmentManufacturer_equipmentManufacturerId_fkey" FOREIGN KEY ("equipmentManufacturerId") REFERENCES "ship"."EquipmentManufacturer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentManufacturer" ADD CONSTRAINT "JobEquipmentManufacturer_equipmentManufacturerRawDataId_fkey" FOREIGN KEY ("equipmentManufacturerRawDataId") REFERENCES "rawData"."EquipmentManufacturerRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentManufacturer" ADD CONSTRAINT "JobEquipmentManufacturer_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "company"."Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentModel" ADD CONSTRAINT "JobEquipmentModel_equipmentModelId_fkey" FOREIGN KEY ("equipmentModelId") REFERENCES "ship"."EquipmentModel"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentModel" ADD CONSTRAINT "JobEquipmentModel_equipmentModelRawDataId_fkey" FOREIGN KEY ("equipmentModelRawDataId") REFERENCES "rawData"."EquipmentModelRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentModel" ADD CONSTRAINT "JobEquipmentModel_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "company"."Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentCategory" ADD CONSTRAINT "JobEquipmentCategory_equipmentCategoryId_fkey" FOREIGN KEY ("equipmentCategoryId") REFERENCES "ship"."EquipmentCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentCategory" ADD CONSTRAINT "JobEquipmentCategory_equipmentCategoryRawDataId_fkey" FOREIGN KEY ("equipmentCategoryRawDataId") REFERENCES "rawData"."EquipmentCategoryRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEquipmentCategory" ADD CONSTRAINT "JobEquipmentCategory_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "company"."Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEntityBenefit" ADD CONSTRAINT "JobEntityBenefit_entityBenefitRawDataId_fkey" FOREIGN KEY ("entityBenefitRawDataId") REFERENCES "rawData"."EntityBenefitRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobEntityBenefit" ADD CONSTRAINT "JobEntityBenefit_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "company"."Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobApplication" ADD CONSTRAINT "JobApplication_applicantId_fkey" FOREIGN KEY ("applicantId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobApplication" ADD CONSTRAINT "JobApplication_decisionMakerProfileId_fkey" FOREIGN KEY ("decisionMakerProfileId") REFERENCES "user"."Profile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobApplication" ADD CONSTRAINT "JobApplication_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "company"."Job"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobApplication" ADD CONSTRAINT "JobApplication_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."JobApplication" ADD CONSTRAINT "JobApplication_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."DepartmentAlternative" ADD CONSTRAINT "DepartmentAlternative_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "company"."Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company"."DesignationAlternative" ADD CONSTRAINT "DesignationAlternative_designationId_fkey" FOREIGN KEY ("designationId") REFERENCES "company"."Designation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document"."Identity" ADD CONSTRAINT "Identity_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document"."Identity" ADD CONSTRAINT "Identity_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document"."Visa" ADD CONSTRAINT "Visa_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document"."Visa" ADD CONSTRAINT "Visa_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."Post" ADD CONSTRAINT "Post_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."Post" ADD CONSTRAINT "Post_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostMedia" ADD CONSTRAINT "PostMedia_postId_fkey" FOREIGN KEY ("postId") REFERENCES "feed"."Post"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostMedia" ADD CONSTRAINT "PostMedia_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostMedia" ADD CONSTRAINT "PostMedia_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostReaction" ADD CONSTRAINT "PostReaction_postId_fkey" FOREIGN KEY ("postId") REFERENCES "feed"."Post"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostReaction" ADD CONSTRAINT "PostReaction_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostReaction" ADD CONSTRAINT "PostReaction_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostComment" ADD CONSTRAINT "PostComment_parentCommentId_fkey" FOREIGN KEY ("parentCommentId") REFERENCES "feed"."PostComment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostComment" ADD CONSTRAINT "PostComment_postId_fkey" FOREIGN KEY ("postId") REFERENCES "feed"."Post"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostComment" ADD CONSTRAINT "PostComment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostComment" ADD CONSTRAINT "PostComment_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostCommentReaction" ADD CONSTRAINT "PostCommentReaction_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "feed"."PostComment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostCommentReaction" ADD CONSTRAINT "PostCommentReaction_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feed"."PostCommentReaction" ADD CONSTRAINT "PostCommentReaction_entityProfileId_fkey" FOREIGN KEY ("entityProfileId") REFERENCES "company"."EntityProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Community" ADD CONSTRAINT "Community_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES "user"."Profile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."CommunityMember" ADD CONSTRAINT "CommunityMember_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."CommunityMember" ADD CONSTRAINT "CommunityMember_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."CommunityRequest" ADD CONSTRAINT "CommunityRequest_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."CommunityRequest" ADD CONSTRAINT "CommunityRequest_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_equipmentCategoryId_fkey" FOREIGN KEY ("equipmentCategoryId") REFERENCES "ship"."EquipmentCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_equipmentCategoryRawDataId_fkey" FOREIGN KEY ("equipmentCategoryRawDataId") REFERENCES "rawData"."EquipmentCategoryRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_equipmentModelId_fkey" FOREIGN KEY ("equipmentModelId") REFERENCES "ship"."EquipmentModel"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_equipmentModelRawDataId_fkey" FOREIGN KEY ("equipmentModelRawDataId") REFERENCES "rawData"."EquipmentModelRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_equipmentManufacturerId_fkey" FOREIGN KEY ("equipmentManufacturerId") REFERENCES "ship"."EquipmentManufacturer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_equipmentManufacturerRawDataId_fkey" FOREIGN KEY ("equipmentManufacturerRawDataId") REFERENCES "rawData"."EquipmentManufacturerRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_shipImo_fkey" FOREIGN KEY ("shipImo") REFERENCES "ship"."Ship"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_shipRawDataImo_fkey" FOREIGN KEY ("shipRawDataImo") REFERENCES "rawData"."ShipRawData"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_mainVesselTypeId_fkey" FOREIGN KEY ("mainVesselTypeId") REFERENCES "ship"."MainVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_mainVesselTypeRawDataId_fkey" FOREIGN KEY ("mainVesselTypeRawDataId") REFERENCES "rawData"."MainVesselTypeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_subVesselTypeId_fkey" FOREIGN KEY ("subVesselTypeId") REFERENCES "ship"."SubVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_subVesselTypeRawDataId_fkey" FOREIGN KEY ("subVesselTypeRawDataId") REFERENCES "rawData"."SubVesselTypeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_departmentAlternativeId_fkey" FOREIGN KEY ("departmentAlternativeId") REFERENCES "company"."DepartmentAlternative"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Question" ADD CONSTRAINT "Question_departmentRawDataId_fkey" FOREIGN KEY ("departmentRawDataId") REFERENCES "rawData"."DepartmentRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionMedia" ADD CONSTRAINT "QuestionMedia_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionMedia" ADD CONSTRAINT "QuestionMedia_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Answer" ADD CONSTRAINT "Answer_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Answer" ADD CONSTRAINT "Answer_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Answer" ADD CONSTRAINT "Answer_validatedBy_fkey" FOREIGN KEY ("validatedBy") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."Answer" ADD CONSTRAINT "Answer_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerMedia" ADD CONSTRAINT "AnswerMedia_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerMedia" ADD CONSTRAINT "AnswerMedia_answerId_fkey" FOREIGN KEY ("answerId") REFERENCES "forum"."Answer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerVote" ADD CONSTRAINT "AnswerVote_answerId_fkey" FOREIGN KEY ("answerId") REFERENCES "forum"."Answer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerVote" ADD CONSTRAINT "AnswerVote_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerVote" ADD CONSTRAINT "AnswerVote_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerVote" ADD CONSTRAINT "AnswerVote_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionVote" ADD CONSTRAINT "QuestionVote_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionVote" ADD CONSTRAINT "QuestionVote_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionVote" ADD CONSTRAINT "QuestionVote_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionTopic" ADD CONSTRAINT "QuestionTopic_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionTopic" ADD CONSTRAINT "QuestionTopic_topicId_fkey" FOREIGN KEY ("topicId") REFERENCES "forum"."Topic"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionTopic" ADD CONSTRAINT "QuestionTopic_topicRawDataId_fkey" FOREIGN KEY ("topicRawDataId") REFERENCES "rawData"."TopicRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionComment" ADD CONSTRAINT "QuestionComment_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionComment" ADD CONSTRAINT "QuestionComment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."QuestionComment" ADD CONSTRAINT "QuestionComment_parentCommentId_fkey" FOREIGN KEY ("parentCommentId") REFERENCES "forum"."QuestionComment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerComment" ADD CONSTRAINT "AnswerComment_answerId_fkey" FOREIGN KEY ("answerId") REFERENCES "forum"."Answer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerComment" ADD CONSTRAINT "AnswerComment_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "forum"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerComment" ADD CONSTRAINT "AnswerComment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerComment" ADD CONSTRAINT "AnswerComment_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "forum"."Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forum"."AnswerComment" ADD CONSTRAINT "AnswerComment_parentCommentId_fkey" FOREIGN KEY ("parentCommentId") REFERENCES "forum"."AnswerComment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "master"."City" ADD CONSTRAINT "City_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "master"."CallingCode" ADD CONSTRAINT "CallingCode_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "master"."Timezone" ADD CONSTRAINT "Timezone_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "master"."Currency" ADD CONSTRAINT "Currency_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."Request" ADD CONSTRAINT "Request_receiverProfileId_fkey" FOREIGN KEY ("receiverProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."Request" ADD CONSTRAINT "Request_senderProfileId_fkey" FOREIGN KEY ("senderProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."Connection" ADD CONSTRAINT "Connection_connectedId_fkey" FOREIGN KEY ("connectedId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."Connection" ADD CONSTRAINT "Connection_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."BlockedProfile" ADD CONSTRAINT "BlockedProfile_blockedId_fkey" FOREIGN KEY ("blockedId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."BlockedProfile" ADD CONSTRAINT "BlockedProfile_blockerId_fkey" FOREIGN KEY ("blockerId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."Follow" ADD CONSTRAINT "Follow_followerProfileId_fkey" FOREIGN KEY ("followerProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "network"."Follow" ADD CONSTRAINT "Follow_followeeProfileId_fkey" FOREIGN KEY ("followeeProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."Port" ADD CONSTRAINT "Port_cityId_fkey" FOREIGN KEY ("cityId") REFERENCES "master"."City"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."Port" ADD CONSTRAINT "Port_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."Port" ADD CONSTRAINT "Port_timezone_countryIso2_fkey" FOREIGN KEY ("timezone", "countryIso2") REFERENCES "master"."Timezone"("timezone", "countryIso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortImageContribution" ADD CONSTRAINT "PortImageContribution_portRawDataUnLocode_fkey" FOREIGN KEY ("portRawDataUnLocode") REFERENCES "rawData"."PortRawData"("unLocode") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortImageContribution" ADD CONSTRAINT "PortImageContribution_portUnLocode_fkey" FOREIGN KEY ("portUnLocode") REFERENCES "port"."Port"("unLocode") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortImageContribution" ADD CONSTRAINT "PortImageContribution_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortContribution" ADD CONSTRAINT "PortContribution_portRawDataUnLocode_fkey" FOREIGN KEY ("portRawDataUnLocode") REFERENCES "rawData"."PortRawData"("unLocode") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortContribution" ADD CONSTRAINT "PortContribution_portUnLocode_fkey" FOREIGN KEY ("portUnLocode") REFERENCES "port"."Port"("unLocode") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortContribution" ADD CONSTRAINT "PortContribution_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookPost" ADD CONSTRAINT "ScrapBookPost_portUnLocode_fkey" FOREIGN KEY ("portUnLocode") REFERENCES "port"."Port"("unLocode") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookPost" ADD CONSTRAINT "ScrapBookPost_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookReaction" ADD CONSTRAINT "ScrapBookReaction_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookReaction" ADD CONSTRAINT "ScrapBookReaction_scrapBookPostId_fkey" FOREIGN KEY ("scrapBookPostId") REFERENCES "port"."ScrapBookPost"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookComment" ADD CONSTRAINT "ScrapBookComment_parentCommentId_fkey" FOREIGN KEY ("parentCommentId") REFERENCES "port"."ScrapBookComment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookComment" ADD CONSTRAINT "ScrapBookComment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookComment" ADD CONSTRAINT "ScrapBookComment_scrapBookPostId_fkey" FOREIGN KEY ("scrapBookPostId") REFERENCES "port"."ScrapBookPost"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookCommentMention" ADD CONSTRAINT "ScrapBookCommentMention_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "port"."ScrapBookComment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."ScrapBookCommentMention" ADD CONSTRAINT "ScrapBookCommentMention_mentionedProfileId_fkey" FOREIGN KEY ("mentionedProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortVisitor" ADD CONSTRAINT "PortVisitor_portRawDataUnLocode_fkey" FOREIGN KEY ("portRawDataUnLocode") REFERENCES "rawData"."PortRawData"("unLocode") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortVisitor" ADD CONSTRAINT "PortVisitor_portUnLocode_fkey" FOREIGN KEY ("portUnLocode") REFERENCES "port"."Port"("unLocode") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "port"."PortVisitor" ADD CONSTRAINT "PortVisitor_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."ShipRawData" ADD CONSTRAINT "ShipRawData_flagCountryIso2_fkey" FOREIGN KEY ("flagCountryIso2") REFERENCES "master"."Country"("iso2") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."ShipRawData" ADD CONSTRAINT "ShipRawData_mainVesselTypeId_fkey" FOREIGN KEY ("mainVesselTypeId") REFERENCES "ship"."MainVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."ShipRawData" ADD CONSTRAINT "ShipRawData_mainVesselTypeRawDataId_fkey" FOREIGN KEY ("mainVesselTypeRawDataId") REFERENCES "rawData"."MainVesselTypeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."ShipRawData" ADD CONSTRAINT "ShipRawData_subVesselTypeId_fkey" FOREIGN KEY ("subVesselTypeId") REFERENCES "ship"."SubVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."ShipRawData" ADD CONSTRAINT "ShipRawData_subVesselTypeRawDataId_fkey" FOREIGN KEY ("subVesselTypeRawDataId") REFERENCES "rawData"."SubVesselTypeRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."CityRawData" ADD CONSTRAINT "CityRawData_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."AddressRawData" ADD CONSTRAINT "AddressRawData_cityId_fkey" FOREIGN KEY ("cityId") REFERENCES "master"."City"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."AddressRawData" ADD CONSTRAINT "AddressRawData_cityRawDataId_fkey" FOREIGN KEY ("cityRawDataId") REFERENCES "rawData"."CityRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."AddressRawData" ADD CONSTRAINT "AddressRawData_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."PortRawData" ADD CONSTRAINT "PortRawData_cityId_fkey" FOREIGN KEY ("cityId") REFERENCES "master"."City"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."PortRawData" ADD CONSTRAINT "PortRawData_cityRawDataId_fkey" FOREIGN KEY ("cityRawDataId") REFERENCES "rawData"."CityRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."PortRawData" ADD CONSTRAINT "PortRawData_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."PortRawData" ADD CONSTRAINT "PortRawData_timezoneIso2_countryIso2_fkey" FOREIGN KEY ("timezoneIso2", "countryIso2") REFERENCES "master"."Timezone"("timezone", "countryIso2") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."SubVesselTypeRawData" ADD CONSTRAINT "SubVesselTypeRawData_mainVesselTypeId_fkey" FOREIGN KEY ("mainVesselTypeId") REFERENCES "ship"."MainVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score"."RewardAssigned" ADD CONSTRAINT "RewardAssigned_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES "score"."Reward"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score"."RewardAssigned" ADD CONSTRAINT "RewardAssigned_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "score"."RewardProfile" ADD CONSTRAINT "RewardProfile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."Ship" ADD CONSTRAINT "Ship_flagCountryIso2_fkey" FOREIGN KEY ("flagCountryIso2") REFERENCES "master"."Country"("iso2") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."Ship" ADD CONSTRAINT "Ship_mainVesselTypeId_fkey" FOREIGN KEY ("mainVesselTypeId") REFERENCES "ship"."MainVesselType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."Ship" ADD CONSTRAINT "Ship_subVesselTypeId_fkey" FOREIGN KEY ("subVesselTypeId") REFERENCES "ship"."SubVesselType"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipName" ADD CONSTRAINT "ShipName_shipImo_fkey" FOREIGN KEY ("shipImo") REFERENCES "ship"."Ship"("imo") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipImageContribution" ADD CONSTRAINT "ShipImageContribution_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipImageContribution" ADD CONSTRAINT "ShipImageContribution_shipImo_fkey" FOREIGN KEY ("shipImo") REFERENCES "ship"."Ship"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipImageContribution" ADD CONSTRAINT "ShipImageContribution_shipRawDataImo_fkey" FOREIGN KEY ("shipRawDataImo") REFERENCES "rawData"."ShipRawData"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipContribution" ADD CONSTRAINT "ShipContribution_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipContribution" ADD CONSTRAINT "ShipContribution_shipImo_fkey" FOREIGN KEY ("shipImo") REFERENCES "ship"."Ship"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."ShipContribution" ADD CONSTRAINT "ShipContribution_shipRawDataImo_fkey" FOREIGN KEY ("shipRawDataImo") REFERENCES "rawData"."ShipRawData"("imo") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ship"."SubVesselType" ADD CONSTRAINT "SubVesselType_mainVesselTypeId_fkey" FOREIGN KEY ("mainVesselTypeId") REFERENCES "ship"."MainVesselType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."Profile" ADD CONSTRAINT "Profile_countryIso2_fkey" FOREIGN KEY ("countryIso2") REFERENCES "master"."Country"("iso2") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."Profile" ADD CONSTRAINT "Profile_designationAlternativeId_fkey" FOREIGN KEY ("designationAlternativeId") REFERENCES "company"."DesignationAlternative"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."Profile" ADD CONSTRAINT "Profile_designationRawDataId_fkey" FOREIGN KEY ("designationRawDataId") REFERENCES "rawData"."DesignationRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."Profile" ADD CONSTRAINT "Profile_entityId_fkey" FOREIGN KEY ("entityId") REFERENCES "company"."Entity"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."Profile" ADD CONSTRAINT "Profile_entityRawDataId_fkey" FOREIGN KEY ("entityRawDataId") REFERENCES "rawData"."EntityRawData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."ProfileStatus" ADD CONSTRAINT "ProfileStatus_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user"."ProfileMeta" ADD CONSTRAINT "ProfileMeta_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral"."ReferralCodes" ADD CONSTRAINT "ReferralCodes_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral"."ReferralStatus" ADD CONSTRAINT "ReferralStatus_referralCodeId_fkey" FOREIGN KEY ("referralCodeId") REFERENCES "referral"."ReferralCodes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral"."ReferralStatus" ADD CONSTRAINT "ReferralStatus_referredProfileId_fkey" FOREIGN KEY ("referredProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "referral"."ReferralStatus" ADD CONSTRAINT "ReferralStatus_referrerProfileId_fkey" FOREIGN KEY ("referrerProfileId") REFERENCES "user"."Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."_CityRawDataToPort" ADD CONSTRAINT "_CityRawDataToPort_A_fkey" FOREIGN KEY ("A") REFERENCES "rawData"."CityRawData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rawData"."_CityRawDataToPort" ADD CONSTRAINT "_CityRawDataToPort_B_fkey" FOREIGN KEY ("B") REFERENCES "port"."Port"("unLocode") ON DELETE CASCADE ON UPDATE CASCADE;
