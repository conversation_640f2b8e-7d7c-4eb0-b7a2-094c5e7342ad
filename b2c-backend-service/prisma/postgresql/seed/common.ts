import { prismaPG } from '../../../src/config/db';
import { SeedMetaItem } from './types';

export const seedMeta: SeedMetaItem<
  | typeof prismaPG.country
  | typeof prismaPG.callingCode
  | typeof prismaPG.timezone
  | typeof prismaPG.city
  | typeof prismaPG.currency
  | typeof prismaPG.certificateCourse
  | typeof prismaPG.mainVesselType
  | typeof prismaPG.subVesselType
  | typeof prismaPG.department
  | typeof prismaPG.departmentAlternative
  | typeof prismaPG.designation
  | typeof prismaPG.designationAlternative
  | typeof prismaPG.entity
  | typeof prismaPG.entitySubType
  | typeof prismaPG.entitySubTypeEntity
  | typeof prismaPG.equipmentCategory
  | typeof prismaPG.fuelType
  | typeof prismaPG.skill
  | typeof prismaPG.platform
  | typeof prismaPG.appVersion
  | typeof prismaPG.port
  | typeof prismaPG.ship
  | typeof prismaPG.shipName
  | typeof prismaPG.community
  | typeof prismaPG.policy
  | typeof prismaPG.reward
  | typeof prismaPG.newsTopic
  | typeof prismaPG.newsProvider
  | typeof prismaPG.newsProviderTopic
  | typeof prismaPG.documentType
>[] = [
    {
      table: prismaPG.country,
      fileName: 'country.json',
      id: ['iso2'],
    },
    {
      table: prismaPG.callingCode,
      fileName: 'callingCode.json',
      id: ['countryIso2'],
    },
    {
      table: prismaPG.timezone,
      fileName: 'timezone.json',
      objId: 'timezone_countryIso2',
      id: ['countryIso2', 'timezone'],
    },

    {
      table: prismaPG.currency,
      fileName: 'currency.json',
      id: ['countryIso2', 'code'],
    },
    {
      table: prismaPG.certificateCourse,
      fileName: 'certificateCourse.json',
      id: ['id'],
    },
    {
      table: prismaPG.mainVesselType,
      fileName: 'mainVesselType.json',
      id: ['id'],
    },
    {
      table: prismaPG.subVesselType,
      fileName: 'subVesselType.json',
      id: ['id'],
    },

    {
      table: prismaPG.department,
      fileName: 'department.json',
      id: ['id'],
    },
    {
      table: prismaPG.departmentAlternative,
      fileName: 'departmentAlternative.json',
      id: ['id'],
    },
    {
      table: prismaPG.designation,
      fileName: 'designation.json',
      id: ['id'],
    },
    {
      table: prismaPG.designationAlternative,
      fileName: 'designationAlternative.json',
      id: ['id'],
    },
    {
      table: prismaPG.entity,
      fileName: 'entityDummy.json',
      id: ['id'],
    },
    {
      table: prismaPG.entitySubType,
      fileName: 'entitySubType.json',
      id: ['id'],
    },
    {
      table: prismaPG.entitySubTypeEntity,
      fileName: 'entitySubTypeEntityDummy.json',
      objId: 'entityId_subEntityTypeId',
      id: ['entityId', 'subEntityTypeId'],
    },
    {
      table: prismaPG.equipmentCategory,
      fileName: 'equipmentCategory.json',
      id: ['id'],
    },
    {
      table: prismaPG.fuelType,
      fileName: 'fuelType.json',
      id: ['id'],
    },
    {
      table: prismaPG.skill,
      fileName: 'skill.json',
      id: ['id'],
    },
    {
      table: prismaPG.platform,
      fileName: 'platform.json',
      id: ['id'],
    },
    {
      table: prismaPG.appVersion,
      fileName: 'appVersion.json',
      id: ['id', 'platformId', 'versionNo'],
    },
     {
      table: prismaPG.ship,
      fileName: 'shipDummy.json',
      id: ['imo'],
    },
     {
      table: prismaPG.shipName,
      fileName: 'shipNameDummy.json',
      id: ['id' ,'shipImo'],
    },
     {
      table: prismaPG.community,
      fileName: 'community.json',
      id: ['id'],
    },
    {
      table: prismaPG.policy,
      fileName: 'policy.json',
      id: ['id'],
    },
     {
      table: prismaPG.reward,
      fileName: 'reward.json',
      id: ['id'],
    },
    {
      table: prismaPG.newsTopic,
      fileName: 'newsTopics.json',
      id: ['id'],
    },
    {
      table: prismaPG.newsProvider,
      fileName: 'newsProvider.json',
      id: ['id'],
    },
    {
      table: prismaPG.newsProviderTopic,
      fileName: 'newsProviderTopic.json',
      id: ['id'],
    },
    {
      table: prismaPG.documentType,
      fileName: 'documentType.json',
      id: ['id']
    }
  ];
